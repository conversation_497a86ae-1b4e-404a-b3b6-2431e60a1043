"""Logging configuration using structlog with Sentry integration and call stack tracking."""
import inspect
import logging

import structlog
from structlog import contextvars
from structlog.stdlib import Bo<PERSON><PERSON>ogger
from structlog_sentry import SentryProcessor

from config.settings import loaded_config



def add_call_stack(_, __, event_dict):
    """Adds a call stack to the log event dictionary if the log level is ERROR or higher."""

    record = event_dict.get("_record")
    if record and record.levelno >= logging.ERROR:
        event_dict["call_stack"] = get_call_stack()
    return event_dict


def get_logger(*args, **kwargs) -> BoundLogger:  # pylint: disable=unused-argument
    """Create structlog logger for logging."""
    if loaded_config.env.lower() == "local":
        renderer = structlog.dev.ConsoleRenderer(colors=True)
    else:
        renderer = structlog.processors.JSONRenderer()

    structlog.configure(
        processors=[
            structlog.processors.TimeStamper(fmt="iso"),
            contextvars.merge_contextvars,
            structlog.processors.add_log_level,
            structlog.processors.StackInfoRenderer(),
            SentryProcessor(level=logging.ERROR),
            structlog.processors.format_exc_info,
            add_call_stack,
            renderer
        ],
        cache_logger_on_first_use=True,
    )
    return structlog.get_logger(**kwargs)


def get_call_stack():
    """Retrieves a simplified call stack with function name, file,
    and line number for recent frames."""

    stack = inspect.stack()
    call_stack = []
    for frame_info in stack[1:5]:  # Skip the current frame
        call_stack.append({
            "function": frame_info.function,
            "file": frame_info.filename,
            "line": frame_info.lineno
        })
    return call_stack

logger = get_logger()