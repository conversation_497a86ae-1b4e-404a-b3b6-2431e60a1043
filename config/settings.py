"""Application configuration settings using Pydantic BaseSettings for environment management."""
import enum
import os
from typing import ClassVar, Optional, Dict, List
from pydantic_settings import BaseSettings

from clerk_integration.utils import <PERSON><PERSON><PERSON><PERSON><PERSON>per

from config.config_parser import docker_args
from utils.aiohttprequest import AioHttpRequest
from utils.connection_manager import ConnectionManager
from utils.sqlalchemy import async_db_url

args = docker_args


class LogLevel(enum.Enum):  # noqa: WPS600
    """Possible log levels."""

    NOTSET = "NOTSET"
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    FATAL = "FATAL"


class Settings(BaseSettings):
    """Application settings configuration with environment variables and default values."""
    model_config = {
        "arbitrary_types_allowed": True,
        "extra": "ignore",
    }

    CONSUMER_TYPE: Optional[str] = args.consumer_type
    env: Optional[str] = args.env
    port: Optional[int] = args.port
    host: Optional[str] = args.host
    debug: Optional[bool] = args.debug
    workers_count: int = 1
    mode: Optional[str] = args.mode
    postgres_fynix_almanac_read_write: Optional[str] = args.postgres_fynix_almanac_read_write
    db_url: Optional[str] = async_db_url(args.postgres_fynix_almanac_read_write)
    db_echo: Optional[bool] = args.debug
    server_type: Optional[str] = args.server_type
    BASE_DIR: ClassVar[str] = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    POD_NAMESPACE: Optional[str] = args.K8S_POD_NAMESPACE
    NODE_NAME: Optional[str] = args.K8S_NODE_NAME
    POD_NAME: Optional[str] = args.K8S_POD_NAME
    #openai_gpt4o_api_key: Optional[str] = args.openai_gpt4o_api_key
    openai_gpt4o_api_key: str = os.getenv("OPENAI_KEY1", args.openai_gpt4o_api_key)
    sentry_sample_rate: float = 1.0
    sentry_environment: Optional[str] = args.sentry_environment
    sentry_dsn: Optional[str] = args.sentry_dsn
    log_level: Optional[str] = LogLevel.INFO.value
    elastic_search_url: Optional[str] = args.elastic_search_url

    """ global class instances """
    connection_manager: Optional[ConnectionManager] = None
    read_connection_manager: Optional[ConnectionManager] = None
    aiohttp_request: Optional[AioHttpRequest] = None
    model_mappings: Optional[Dict] = None
    embedding_mappings: Optional[Dict] = None
    kafka_bootstrap_servers: Optional[str] = args.kafka_broker_list
    locksmith_main_private_url: Optional[str] = args.locksmith_main_private_url
    kafka_partitions: Optional[str] = args.kafka_partitions
    max_docs_per_kb: Optional[int] = args.max_docs_per_kb
    embedding_model: Optional[str] = args.embedding_model
    chunk_token_limit: Optional[str] = args.chunk_token_limit
    gcp: Optional[str] = os.getenv("GCP_GCS_CREDENTIALS", args.gcp)
    clerk_secret_key: Optional[str] = args.clerk_secret_key
    clerk_auth_helper: Optional[ClerkAuthHelper] = ClerkAuthHelper(
        "Almanac", clerk_secret_key=args.clerk_secret_key
    )
    ingestion_integrations: Optional[List[Dict]] = None

    # New Relic Configuration
    new_relic_app_name: Optional[str] = args.new_relic_app_name
    new_relic_license_key: Optional[str] = args.new_relic_license_key
    new_relic_monitor_mode: Optional[bool] = args.new_relic_monitor_mode
    new_relic_developer_mode: Optional[bool] = args.new_relic_developer_mode


    CRON_JOB_UPDATE_KB: str = "update_kb"

loaded_config = Settings()