env: "local"
port: 8099
host: "localhost"
debug: true
mode: "server"
server_type: "public"
agentix_main_url: "http://localhost:8081"
postgres_fynix_almanac_read_write: "postgresql+psycopg2://postgres:postgres@127.0.0.1:5432/almanac"
realm: "fexz0"
K8S_POD_NAMESPACE: "temp"
K8S_NODE_NAME: "temp"
consumer_type: ""
K8S_POD_NAME: "temp"
elastic_search_url: "http://localhost:9200"

openai_gpt4o_api_key: ""

sentry_environment: "development"
sentry_dsn: ""
kafka_broker_list: "127.0.0.1:9092"
locksmith_main_private_url: "http://localhost:8099"
clerk_secret_key: ""
kafka_partitions: 10
max_docs_per_kb: 10
embedding_model: "text-embedding-ada-002"
chunk_token_limit: 8000
gcp: ""
#gcp_type: "service_account"
#gcp_project_id: ""
#gcp_private_key_id: ""
#gcp_private_key: ""
#gcp_client_email: ""
#gcp_client_id: ""
#gcp_auth_uri: "https://accounts.google.com/o/oauth2/auth"
#gcp_token_uri: "https://oauth2.googleapis.com/token"
#gcp_auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs"
#gcp_client_x509_cert_url: ""
#gcp_universe_domain: "googleapis.com"

# New Relic Configuration
new_relic_app_name: "Almanac"
new_relic_license_key: ""
new_relic_monitor_mode: false
new_relic_developer_mode: false