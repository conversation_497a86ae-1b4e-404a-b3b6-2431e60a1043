"""FastAPI application factory with middleware configuration and lifespan management."""
import asyncio
import os
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi_prometheus_middleware import (
    generate_prometheus_data,
    PrometheusMiddleware,
    metrics_endpoint
)
from starlette.middleware.sessions import SessionMiddleware

from app.router import api_router
from config.logging import logger
from config.sentry import configure_sentry
from utils.custom_middleware import SecurityHeadersMiddleware
from utils.load_config import run_on_startup, run_on_exit

PROMETHEUS_LOG_TIME = 10

async def repeated_task_for_prometheus():
    """Periodically generates and writes Prometheus metrics data to a log file 
    based on environment and config."""

    while True:
        # Get the log file path from config
        from config.settings import loaded_config  # pylint: disable=import-outside-toplevel
        log_file = f"{loaded_config.BASE_DIR}/logs.prom"
        if loaded_config.POD_NAME != 'temp' and os.getenv("METRICS_DIR"):
            log_file = f'{os.getenv("METRICS_DIR")}/{loaded_config.POD_NAME}.prom'

        await generate_prometheus_data(log_file)
        await asyncio.sleep(PROMETHEUS_LOG_TIME)


@asynccontextmanager
async def lifespan(app: FastAPI):  # pylint: disable=unused-argument
    """FastAPI lifespan context manager to handle startup and shutdown tasks, 
    including Prometheus metric logging."""

    await run_on_startup()
    asyncio.create_task(repeated_task_for_prometheus())
    yield
    await run_on_exit()



def get_app() -> FastAPI:

    """
    Get FastAPI application.

    This is the main constructor of an application.

    :return: application.
    """
    # if loaded_config.sentry_dsn:
    # Enables sentry integration.
    configure_sentry()

    almanac_app = FastAPI(
        debug=True,
        title="almanac",
        docs_url=None,
        openapi_url="/openapi.json",
        lifespan=lifespan,
    )
    # Configure CORS to allow only 'wmsz0.de'
    almanac_app.add_middleware(
        CORSMiddleware,
        allow_origins=[],  # Only allow this origins
        # allow_origins=[],  # Only allow this origins  # Only allow this origins
        # allow_credentials=True,
        allow_methods=["*"],  # Allows all methods
        allow_headers=["*"],  # Allows all headers
    )

    almanac_app.include_router(api_router)
    almanac_app.add_middleware(
        SessionMiddleware, secret_key="** Session Middleware **"
    )
    almanac_app.add_middleware(SecurityHeadersMiddleware)

    almanac_app.add_middleware(
        PrometheusMiddleware,
        prefix="almanac",
        logger=logger
    )
    almanac_app.add_route('/metrics', metrics_endpoint)

    return almanac_app
