"""Configuration management for the application."""
import os
from typing import Dict, Any
from functools import lru_cache

import yaml


class Config:
    """Manages application configuration data and sets up OpenAI-specific config."""
    
    def __init__(self, config_data: Dict[str, Any]):
        """Initializes the Config object with configuration data and sets up OpenAI-specific config.

        Args:
            config_data (Dict[str, Any]): Dictionary containing configuration settings.
        """

        self.data = config_data
        self.openai = OpenAIConfig(config_data)


class OpenAIConfig:
    """Manages OpenAI-specific configuration settings."""
    
    def __init__(self, config_data: Dict[str, Any]):
        """Initializes the OpenAIConfig with API key from the provided configuration data.

        Args:
            config_data (Dict[str, Any]): Dictionary containing configuration settings, 
                including the OpenAI API key.
        """

        self.api_key = config_data.get('openai_gpt4o_api_key')


@lru_cache()
def get_config() -> Config:
    """Get application configuration, using cache to avoid repeated file reads"""
    config_path = os.path.join(
        os.path.dirname(os.path.dirname(__file__)), 'config', 'default.yaml'
    )
    with open(config_path, 'r', encoding='utf-8') as f:
        config_data = yaml.safe_load(f)
    return Config(config_data)
