"""Custom routing implementation for FastAPI with enhanced logging and error handling."""
import datetime
import time
from typing import Callable
from uuid import uuid4

import or<PERSON><PERSON>
from clerk_integration.utils import UserData
from fastapi import Request, Response
from fastapi.exceptions import RequestValidationError, HTTPException
from fastapi.responses import ORJSONResponse
from fastapi.routing import APIRoute
from pydantic import ValidationError
from starlette.responses import StreamingResponse
from starlette.status import (
    HTTP_500_INTERNAL_SERVER_ERROR, 
    HTTP_400_BAD_REQUEST, 
    HTTP_401_UNAUTHORIZED
)
from starlette.templating import _TemplateResponse

from config.logging import logger
from utils.serializers import ResponseData
from utils.exceptions import ApiException, SessionExpiredException, CustomException


class CustomRequestRoute(APIRoute):
    """Custom API route handler with enhanced logging, error handling, and metrics."""

    def get_route_handler(self) -> Callable:  # pylint: disable=too-many-return-statements
        """Overrides the default route handler to wrap requests with custom logging, 
        error handling, and metrics."""

        original_route_handler = super().get_route_handler()

        async def custom_route_handler(request: Request) -> Response:
            """Handles incoming requests with enhanced logging, error handling, 
            and response processing logic."""

            request_data = await process_request_data(request)
            start_time = time.perf_counter()

            try:
                process_request_headers(request, request_data)
                response = await original_route_handler(request)
                response_data = process_response(response, request_data, start_time)
                log_metrics_and_request(
                    request, request_data, response_data, start_time
                )
                return response

            except (orjson.JSONDecodeError, ApiException) as exc:
                return handle_exception(
                    request, request_data, exc, HTTP_400_BAD_REQUEST, start_time
                )
            except SessionExpiredException as exc:
                return handle_exception(
                    request, request_data, exc, HTTP_401_UNAUTHORIZED, start_time
                )
            except (RequestValidationError, ValidationError) as exc:
                errors = format_pydantic_errors(exc)
                return handle_exception(
                    request, request_data, errors, HTTP_400_BAD_REQUEST, start_time
                )
            except CustomException as exc:
                return handle_exception(
                    request, request_data, exc, HTTP_400_BAD_REQUEST, start_time
                )
            except HTTPException as exc:
                return handle_exception(
                    request, request_data, exc.detail, exc.status_code, start_time
                )
            except Exception as exc:  # pylint: disable=broad-exception-caught
                return handle_exception(
                    request, request_data, exc, HTTP_500_INTERNAL_SERVER_ERROR, start_time
                )

        return custom_route_handler


async def process_request_data(request: Request) -> dict:
    """Extracts metadata and body from the incoming request for logging and processing.

    Returns:
        dict: Dictionary containing request details such as method, URL, headers, and body.
    """

    request_data = {
        'client_host': request.client,
        'url': request.url.components,
        'url_path': request.scope['route'].path,
        'request_method': request.method,
        'path_params': request.path_params.items(),
        'query_params': request.query_params.items(),
        'headers': request.headers.items(),
        'request_body': await request.body()
    }
    return request_data


def request_exception_handler(
    method=None, 
    url_path=None, 
    request_data=None, 
    exc=None, 
    start_time=None,
    status_code=None
) -> Response:
    """Handles exceptions during request processing and returns a standardized error response.

    Returns:
        ORJSONResponse: JSON-formatted error response with proper status code.
    """

    request_data["error"] = exc if isinstance(exc, list) else [str(exc)]
    end_time = time.perf_counter()
    request_data['request_duration'] = end_time - start_time
    logger.exception("Exception occurred for %s", request_data['url_path'])
    error_response = ResponseData.model_construct(
        errors=request_data["error"], success=False
    ).dict()

    return ORJSONResponse(content=error_response, status_code=status_code)


def sanitize_label(value):
    """Sanitizes a value to ensure it is a UTF-8 safe string."""

    if isinstance(value, bytes):
        value = value.decode("utf-8", errors="replace")
    if not isinstance(value, str):
        value = str(value)
    return value.encode("utf-8", errors="replace").decode("utf-8", errors="replace")


def convert_datetimes(obj):
    """Recursively converts all datetime objects in a nested structure to 
    ISO 8601 formatted strings."""

    if isinstance(obj, dict):
        return {key: convert_datetimes(value) for key, value in obj.items()}
    # pylint: disable=no-else-return
    elif isinstance(obj, list):
        return [convert_datetimes(item) for item in obj]
    elif isinstance(obj, datetime.datetime):  # pylint: disable=isinstance-second-argument-not-valid-type
        return obj.isoformat()  # Convert datetime to ISO 8601 string
    return obj


def handle_exception(request, request_data, exc, status_code, start_time):
    """Handles an exception by logging it, formatting the response, 
    and returning a JSON error response."""

    end_time = time.perf_counter()
    request_data['request_duration'] = end_time - start_time

    error_str = [str(exc)] if not isinstance(exc, list) else exc
    request_data["error"] = error_str
    request_data["exception_type"] = type(exc).__name__

    # logger.exception(
    #     "Exception occurred for %s", request_data['url_path'],
    #     extra={'request_data': request_data}
    # )

    message = error_str[0] if isinstance(error_str, list) and error_str else error_str
    error_response = ResponseData.model_construct(
        errors=error_str, success=False, message=message
    ).dict()
    response_data = {'status_code': status_code, 'body': error_response}
    log_metrics_and_request(request, request_data, response_data, start_time)

    return ORJSONResponse(content=error_response, status_code=status_code)


def format_pydantic_errors(validation_error: ValidationError):
    """Formats Pydantic validation errors into a readable list of messages with field locations."""

    return [
        {"msg": f"{error['loc'][-1]}: {error['msg']}", "location": error['loc']}
        for error in validation_error.errors()
    ]


def process_request_headers(request: Request, request_data: dict):
    """Parses and attaches structured request body and user data from headers to request state."""

    content_type = request.headers.get("content-type")
    request_data['request_body'] = (
        orjson.loads(request_data['request_body'])
        if request_data['request_body'] and not content_type.startswith("multipart/form-data")
        else {}
    )
    if x_user_data := request.headers.get("x-user-data"):
        request.state.user_data = UserData.construct(**orjson.loads(x_user_data))


def process_response(response: Response, request_data: dict, start_time: float) -> dict:
    """Extracts relevant response information including status code and response body 
    for logging and metrics."""

    end_time = time.perf_counter()
    request_data['request_duration'] = end_time - start_time

    if isinstance(response, StreamingResponse):
        return {
            'status_code': response.status_code, 
            'body': {'data': 'streaming response', 'identifier': str(uuid4())}
        }
    if isinstance(response, _TemplateResponse):
        return {'status_code': response.status_code, 'body': "HTML response"}
    return {
        'status_code': response.status_code, 
        'body': orjson.loads(response.body.decode('utf-8')) if response.body else ''
    }


def log_metrics_and_request(
    request, request_data, response_data, start_time
):  # pylint: disable=unused-argument
    """Logs the request and response metadata along with the time consumed to Prometheus/GCP."""

    try:
        end_time = time.perf_counter()

        consumed_time = end_time - start_time
        log_api_requests_to_gcp(request_data, response_data, consumed_time)
    except Exception as e:  # pylint: disable=broad-exception-caught
        logger.error("Prometheus Error: %s", e)


def log_api_requests_to_gcp(request_data, response_data, consumed_time):
    """Logs API request and response data to GCP structured logging 
    based on the response status."""

    api_logs = {
        "status": response_data['status_code'],
        'consumed_time': consumed_time or 0,
        'request': request_data,
        'response': response_data
    }
    if response_data['status_code'] == 200:
        logger.info("API request completed successfully", exc_info=api_logs)
    else:
        logger.error(
            f"API request failed with status {response_data['status_code']}", 
            exc_info=api_logs
        )
