"""
Router configuration for the FastAPI application.

This module defines the main API router and organizes routes based on server type.
It includes health check endpoints and version-specific route groups.
"""
from fastapi.responses import JSONResponse
from fastapi.routing import APIRouter

from app.routing import CustomRequestRoute
from code_indexing.router import router as code_indexing_router
from config.settings import loaded_config
from knowledge_base.router import router as knowledge_base_router


async def healthz():
    """Health check endpoint to verify the service is running and responsive."""

    return JSONResponse(status_code=200, content={"success": True})


api_router = APIRouter()

# all version v1.0 routes
api_router_v1 = APIRouter(prefix='/v1.0', route_class=CustomRequestRoute)

if loaded_config.server_type == "public":
    # Declare your routes here
    api_router_v1.include_router(code_indexing_router)
    # api_router_v1.include_router(external_data_indexing_router)
    api_router_v1.include_router(knowledge_base_router)
elif loaded_config.server_type == "websocket":
    # Declare your websockets here
    pass
else:
    # all common routes
    pass

# health check routes
api_router_healthz = APIRouter()
api_router_healthz.add_api_route(
    "/_healthz",
    methods=['GET'],
    endpoint=healthz,
    include_in_schema=False
)
api_router_healthz.add_api_route(
    "/_readyz",
    methods=['GET'],
    endpoint=healthz,
    include_in_schema=False
)

api_router.include_router(api_router_healthz)
api_router.include_router(api_router_v1)
