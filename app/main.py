"""
Entrypoint for starting the FastAPI application using Uvicorn with settings from the configuration.
"""

import uvicorn
from config.settings import loaded_config


def main() -> None:
    """Starts the Uvicorn server with configuration loaded from settings."""

    uvicorn.run(
        "app.application:get_app",
        workers=loaded_config.workers_count,
        host=loaded_config.host,
        port=loaded_config.port,
        reload=loaded_config.debug,
        factory=True,
    )
