# medley
fastapi~=0.115.3
requests~=2.32.0
SQLAlchemy~=1.4.48
elasticsearch~=8.13.0
aiohttp>=3.11.14,<4
Jinja2~=3.1.4
redis~=5.0.0
pytz~=2023.3.post1
pyyaml~=6.0.1
pydantic==2.11.3
pydantic-settings==2.8.1
asyncpg~=0.28.0
websockets~=12.0
orjson~=3.9.15
uvicorn~=0.23.2
python-dateutil~=2.9.0.post0
eventqueue@git+ssh://*********************/v3/GoFynd/FEX/event-queue@event-queue-v1.0.0
greenlet~=2.0.2
newrelic~=9.7.0
openai==1.66.3
fastjsonschema~=2.19.1
psycopg2-binary~=2.9.9
itsdangerous~=2.1.2
httpx==0.28.1
Authlib~=1.3.1
APScheduler~=3.10.4
ConfigArgParse~=1.7
structlog==24.1.0
structlog-sentry==2.0.3
opentelemetry-instrumentation-fastapi==0.41b0
azure-devops==7.1.0b4
google-cloud-storage==2.18.0
google-auth==2.32.0
anthropic==0.34.1
alembic==1.12.0
beautifulsoup4~=4.12.3
lxml~=5.1.0
html5lib~=1.1
tiktoken==0.8.0
packaging==23.1
typing_extensions>=4.11,<5
google-cloud-bigquery~=3.17.2
spacy~=3.7.4
clerk_integration@git+https://github.com/mitanshu610/clerk_integration@main
bcrypt~=4.1.2
fastapi-prometheus-middleware@git+ssh://*********************/v3/GoFynd/FEX/fastapi-promethus-api-tracking@main
httpcore==1.0.9
protobuf==4.25.8
googleapis-common-protos==1.70.0

# Test dependencies
pytest~=8.3.0
pytest-cov~=6.0.0
pytest-mock~=3.14.0
pytest-asyncio~=0.26.0
factory-boy~=3.3.0
faker~=30.0.0
coverage~=7.6.0
httpx[testing]~=0.28.1
freezegun~=1.5.0
responses~=0.25.0