#
# This file is autogenerated by pip-compile with Python 3.10
# by the following command:
#
#    pip-compile requirements/requirements.in
#
aiofiles==23.2.1
    # via eventqueue
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.11.14
    # via
    #   -r requirements/requirements.in
    #   clerk-integration
aiokafka==0.8.1
    # via eventqueue
aiosignal==1.3.2
    # via aiohttp
alembic==1.12.0
    # via -r requirements/requirements.in
annotated-types==0.7.0
    # via pydantic
anthropic==0.34.1
    # via -r requirements/requirements.in
anyio==4.9.0
    # via
    #   anthropic
    #   httpx
    #   openai
    #   starlette
apscheduler==3.10.4
    # via -r requirements/requirements.in
asgiref==3.8.1
    # via opentelemetry-instrumentation-asgi
async-timeout==4.0.3
    # via
    #   aiohttp
    #   aiokafka
    #   eventqueue
    #   redis
asyncpg==0.28.0
    # via -r requirements/requirements.in
attrs==25.3.0
    # via aiohttp
authlib==1.3.2
    # via -r requirements/requirements.in
azure-core==1.34.0
    # via msrest
azure-devops==7.1.0b4
    # via -r requirements/requirements.in
bcrypt==4.1.3
    # via -r requirements/requirements.in
beautifulsoup4==4.12.3
    # via -r requirements/requirements.in
blis==0.7.11
    # via thinc
cachetools==5.5.2
    # via google-auth
catalogue==2.0.10
    # via
    #   spacy
    #   srsly
    #   thinc
certifi==2025.6.15
    # via
    #   elastic-transport
    #   httpcore
    #   httpx
    #   msrest
    #   requests
    #   sentry-sdk
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.2
    # via requests
clerk-backend-api==1.8.0
    # via clerk-integration
clerk-integration @ git+https://github.com/mitanshu610/clerk_integration@main
    # via -r requirements/requirements.in
click==8.2.1
    # via
    #   typer
    #   uvicorn
cloudpathlib==0.21.1
    # via weasel
confection==0.1.5
    # via
    #   thinc
    #   weasel
configargparse==1.7.1
    # via -r requirements/requirements.in
confluent-kafka==2.3.0
    # via eventqueue
coverage[toml]==7.6.12
    # via
    #   -r requirements/requirements.in
    #   pytest-cov
cryptography==43.0.3
    # via
    #   authlib
    #   clerk-backend-api
cymem==2.0.11
    # via
    #   preshed
    #   spacy
    #   thinc
distro==1.9.0
    # via
    #   anthropic
    #   openai
elastic-transport==8.17.1
    # via elasticsearch
elasticsearch==8.13.2
    # via -r requirements/requirements.in
eval-type-backport==0.2.2
    # via clerk-backend-api
eventqueue @ git+ssh://*********************/v3/GoFynd/FEX/event-queue@event-queue-v1.0.0
    # via -r requirements/requirements.in
exceptiongroup==1.3.0
    # via
    #   anyio
    #   pytest
factory-boy==3.3.3
    # via -r requirements/requirements.in
faker==30.0.0
    # via
    #   -r requirements/requirements.in
    #   factory-boy
fastapi==0.115.3
    # via
    #   -r requirements/requirements.in
    #   clerk-integration
    #   fastapi-prometheus-middleware
fastapi-prometheus-middleware @ git+ssh://*********************/v3/GoFynd/FEX/fastapi-promethus-api-tracking@main
    # via -r requirements/requirements.in
fastjsonschema==2.19.1
    # via -r requirements/requirements.in
filelock==3.18.0
    # via huggingface-hub
freezegun==1.5.2
    # via -r requirements/requirements.in
frozenlist==1.7.0
    # via
    #   aiohttp
    #   aiosignal
fsspec==2025.5.1
    # via huggingface-hub
google-api-core==2.25.1
    # via
    #   google-cloud-bigquery
    #   google-cloud-core
    #   google-cloud-storage
google-auth==2.32.0
    # via
    #   -r requirements/requirements.in
    #   google-api-core
    #   google-cloud-core
    #   google-cloud-storage
google-cloud-bigquery==3.17.2
    # via -r requirements/requirements.in
google-cloud-core==2.4.3
    # via
    #   google-cloud-bigquery
    #   google-cloud-storage
google-cloud-storage==2.18.0
    # via -r requirements/requirements.in
google-crc32c==1.7.1
    # via
    #   google-cloud-storage
    #   google-resumable-media
google-resumable-media==2.7.2
    # via
    #   google-cloud-bigquery
    #   google-cloud-storage
googleapis-common-protos==1.70.0
    # via
    #   -r requirements/requirements.in
    #   google-api-core
greenlet==2.0.2
    # via -r requirements/requirements.in
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
hf-xet==1.1.5
    # via huggingface-hub
html5lib==1.1
    # via -r requirements/requirements.in
httpcore==1.0.9
    # via
    #   -r requirements/requirements.in
    #   httpx
httpx[testing]==0.28.1
    # via
    #   -r requirements/requirements.in
    #   anthropic
    #   clerk-backend-api
    #   openai
huggingface-hub==0.33.1
    # via tokenizers
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
    #   yarl
importlib-metadata==8.7.0
    # via opentelemetry-api
iniconfig==2.1.0
    # via pytest
isodate==0.7.2
    # via msrest
itsdangerous==2.1.2
    # via -r requirements/requirements.in
jinja2==3.1.6
    # via
    #   -r requirements/requirements.in
    #   spacy
jiter==0.10.0
    # via
    #   anthropic
    #   openai
kafka-python==2.0.2
    # via
    #   aiokafka
    #   eventqueue
langcodes==3.5.0
    # via spacy
language-data==1.3.0
    # via langcodes
lxml==5.1.1
    # via -r requirements/requirements.in
lz4==4.3.2
    # via eventqueue
mako==1.3.10
    # via alembic
marisa-trie==1.2.1
    # via language-data
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via
    #   jinja2
    #   mako
marshmallow==3.20.1
    # via eventqueue
mdurl==0.1.2
    # via markdown-it-py
msrest==0.7.1
    # via azure-devops
multidict==6.6.2
    # via
    #   aiohttp
    #   yarl
murmurhash==1.0.13
    # via
    #   preshed
    #   spacy
    #   thinc
mypy-extensions==1.1.0
    # via typing-inspect
newrelic==9.7.1
    # via -r requirements/requirements.in
numpy==1.26.4
    # via
    #   blis
    #   spacy
    #   thinc
oauthlib==3.3.1
    # via requests-oauthlib
openai==1.66.3
    # via -r requirements/requirements.in
opentelemetry-api==1.34.1
    # via
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation==0.41b0
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation-asgi==0.41b0
    # via opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation-fastapi==0.41b0
    # via -r requirements/requirements.in
opentelemetry-semantic-conventions==0.41b0
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
opentelemetry-util-http==0.41b0
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
orjson==3.9.15
    # via
    #   -r requirements/requirements.in
    #   fastapi-prometheus-middleware
packaging==23.1
    # via
    #   -r requirements/requirements.in
    #   aiokafka
    #   eventqueue
    #   google-cloud-bigquery
    #   huggingface-hub
    #   marshmallow
    #   pytest
    #   spacy
    #   thinc
    #   weasel
pluggy==1.6.0
    # via pytest
preshed==3.0.10
    # via
    #   spacy
    #   thinc
prometheus-client==0.3.0
    # via
    #   eventqueue
    #   fastapi-prometheus-middleware
propcache==0.3.2
    # via
    #   aiohttp
    #   yarl
proto-plus==1.26.1
    # via google-api-core
protobuf==4.25.8
    # via
    #   -r requirements/requirements.in
    #   google-api-core
    #   googleapis-common-protos
    #   proto-plus
psycopg2-binary==2.9.10
    # via -r requirements/requirements.in
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pycparser==2.22
    # via cffi
pydantic==2.11.3
    # via
    #   -r requirements/requirements.in
    #   anthropic
    #   clerk-backend-api
    #   clerk-integration
    #   confection
    #   fastapi
    #   fastapi-prometheus-middleware
    #   openai
    #   pydantic-settings
    #   spacy
    #   thinc
    #   weasel
pydantic-core==2.33.1
    # via pydantic
pydantic-settings==2.8.1
    # via -r requirements/requirements.in
pygments==2.19.2
    # via rich
pyjwt==2.10.1
    # via clerk-backend-api
pytest==8.3.5
    # via
    #   -r requirements/requirements.in
    #   pytest-asyncio
    #   pytest-cov
    #   pytest-mock
pytest-asyncio==0.26.0
    # via -r requirements/requirements.in
pytest-cov==6.0.0
    # via -r requirements/requirements.in
pytest-mock==3.14.1
    # via -r requirements/requirements.in
python-dateutil==2.9.0.post0
    # via
    #   -r requirements/requirements.in
    #   clerk-backend-api
    #   faker
    #   freezegun
    #   google-cloud-bigquery
python-dotenv==1.1.1
    # via pydantic-settings
pytz==2023.4
    # via
    #   -r requirements/requirements.in
    #   apscheduler
pyyaml==6.0.2
    # via
    #   -r requirements/requirements.in
    #   huggingface-hub
    #   responses
redis==5.0.8
    # via -r requirements/requirements.in
regex==2024.11.6
    # via tiktoken
requests==2.32.4
    # via
    #   -r requirements/requirements.in
    #   azure-core
    #   google-api-core
    #   google-cloud-bigquery
    #   google-cloud-storage
    #   huggingface-hub
    #   msrest
    #   requests-oauthlib
    #   responses
    #   spacy
    #   tiktoken
    #   weasel
requests-oauthlib==2.0.0
    # via msrest
responses==0.25.7
    # via -r requirements/requirements.in
rich==14.0.0
    # via typer
rsa==4.9.1
    # via google-auth
sentry-sdk==2.32.0
    # via structlog-sentry
shellingham==1.5.4
    # via typer
six==1.17.0
    # via
    #   apscheduler
    #   azure-core
    #   html5lib
    #   python-dateutil
smart-open==7.1.0
    # via weasel
sniffio==1.3.1
    # via
    #   anthropic
    #   anyio
    #   openai
soupsieve==2.7
    # via beautifulsoup4
spacy==3.7.5
    # via -r requirements/requirements.in
spacy-legacy==3.0.12
    # via spacy
spacy-loggers==1.0.5
    # via spacy
sqlalchemy==1.4.54
    # via
    #   -r requirements/requirements.in
    #   alembic
srsly==2.5.1
    # via
    #   confection
    #   spacy
    #   thinc
    #   weasel
starlette==0.41.3
    # via
    #   fastapi
    #   fastapi-prometheus-middleware
structlog==24.1.0
    # via
    #   -r requirements/requirements.in
    #   structlog-sentry
structlog-sentry==2.0.3
    # via -r requirements/requirements.in
thinc==8.2.5
    # via spacy
tiktoken==0.8.0
    # via -r requirements/requirements.in
tokenizers==0.21.2
    # via anthropic
tomli==2.2.1
    # via
    #   coverage
    #   pytest
tqdm==4.67.1
    # via
    #   huggingface-hub
    #   openai
    #   spacy
typer==0.16.0
    # via
    #   spacy
    #   weasel
typing-extensions==4.14.0
    # via
    #   -r requirements/requirements.in
    #   alembic
    #   anthropic
    #   anyio
    #   asgiref
    #   azure-core
    #   cloudpathlib
    #   exceptiongroup
    #   fastapi
    #   huggingface-hub
    #   multidict
    #   openai
    #   opentelemetry-api
    #   pydantic
    #   pydantic-core
    #   rich
    #   typer
    #   typing-inspect
    #   typing-inspection
    #   uvicorn
typing-inspect==0.9.0
    # via clerk-backend-api
typing-inspection==0.4.1
    # via pydantic
tzlocal==5.3.1
    # via apscheduler
ujson==5.8.0
    # via eventqueue
urllib3==2.5.0
    # via
    #   elastic-transport
    #   requests
    #   responses
    #   sentry-sdk
uvicorn==0.23.2
    # via -r requirements/requirements.in
wasabi==1.1.3
    # via
    #   spacy
    #   thinc
    #   weasel
weasel==0.4.1
    # via spacy
webencodings==0.5.1
    # via html5lib
websockets==12.0
    # via -r requirements/requirements.in
wrapt==1.17.2
    # via
    #   opentelemetry-instrumentation
    #   smart-open
yarl==1.20.1
    # via aiohttp
zipp==3.23.0
    # via importlib-metadata

# The following packages are considered to be unsafe in a requirements file:
# setuptools
