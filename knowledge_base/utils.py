from typing import List, Dict


def transform_tables_data(
    documents: List[Dict], tables_and_columns: List[Dict]
) -> List[Dict]:
    """Transform the documents and tables_and_columns data into a standardized format."""
    # Build lookup maps for faster access
    table_descriptions = {
        doc["table_name"]: doc["table_description"] for doc in documents
    }
    column_descriptions = {
        (doc["table_name"], doc["column_name"]): doc["column_description"]
        for doc in documents
    }
    selected_tables = {doc["table_name"] for doc in documents}
    selected_columns = {
        (doc["table_name"], doc["column_name"]) for doc in documents
    }

    result = []
    for table_obj in tables_and_columns:
        for table_name, columns in table_obj.items():
            table_info = {
                "name": table_name,
                "description": table_descriptions.get(table_name, ""),
                "selected": table_name in selected_tables,
                "columns": [
                    {
                        "name": column_name,
                        "selected": (table_name, column_name) in selected_columns,
                        "type": column_type,
                        "description": column_descriptions.get(
                            (table_name, column_name), ""
                        ),
                    }
                    for column_name, column_type in columns.items()
                ],
            }
            result.append(table_info)

    return result