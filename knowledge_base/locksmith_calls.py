"""API client functions for locksmith service access control operations.

This module provides functions for managing knowledge base access permissions
through the locksmith service API. It includes operations for:
- Granting and revoking data source access
- Checking user/team/organization access permissions
- Retrieving accessible datasources for users and organizations
- Managing specific access revocations

All functions handle HTTP communication with the locksmith service
and provide proper error handling and logging.
"""

from typing import Optional, List
from config.logging import logger
from config.settings import loaded_config
from knowledge_base.constants import APP_SLASH_JSON
from utils.exceptions import ApiException
import httpx
from clerk_integration.utils import UserData


async def _call_grant_data_source_access(
    team_id: Optional[str],
    knowledge_base_id: int,
    user_data: Optional[UserData] = None,
    org_id: Optional[str] = None,
    user_id: Optional[str] = None,
) -> dict:
    """Grants access to a knowledge base for a user, team, or organization.

    Args:
        team_id (Optional[str]): Team ID to grant access to.
        knowledge_base_id (int): ID of the knowledge base.
        user_data (Optional[UserData]): UserData object containing user/org ID.
        org_id (Optional[str]): Organization ID to grant access to.
        user_id (Optional[str]): User ID to grant access to.

    Returns:
        dict: API response confirming granted access.

    Raises:
        ApiException: If the request fails with non-200 status.
    """

    logger.info("Calling grant_data_source_access API")

    team_id, user_id, org_id = (
        team_id,
        user_data.userId if user_data else user_id,
        user_data.orgId if user_data else org_id,
    )

    if team_id:
        org_id = None
        user_id = None

    if org_id:
        team_id = None
        user_id = None

    payload = {
        "datasource_id": knowledge_base_id,
        "user_id": user_id,
        "team_id": team_id,
        "org_id": org_id,
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{loaded_config.locksmith_main_private_url}/v1.0/datasources/access",
            json=payload,
            headers={"Content-Type": APP_SLASH_JSON},
        )

        if response.status_code != 200:
            logger.error(
                f"Failed to grant data source access for team_id={team_id}, "
                f"user_id={user_id}, org_id={org_id}, kb_id={knowledge_base_id}: "
                f"{response.text}"
            )
            raise ApiException(
                f"Failed with status {response.status_code}: {response.text}"
            )

        logger.info("Successfully granted data source access")
        return response.json()


async def _call_revoke_data_source_access(knowledge_base_id: int) -> dict:
    """Revokes all access to a given knowledge base.

    Args:
        knowledge_base_id (int): The ID of the knowledge base to revoke access for.

    Returns:
        dict: API response confirming revocation.

    Raises:
        ApiException: If the request fails with non-200 status.
    """

    logger.info("Calling revoke data source access API")

    # Add query parameter to the URL instead of sending in body for DELETE request
    url = f"{loaded_config.locksmith_main_private_url}/v1.0/datasources/access?datasource_id={knowledge_base_id}"

    async with httpx.AsyncClient() as client:
        response = await client.delete(url, headers={"Content-Type": APP_SLASH_JSON})

        if response.status_code != 200:
            logger.error(f"Failed to revoke data source access: {response.text}")
            raise ApiException(
                f"Failed with status {response.status_code}: {response.text}"
            )

        logger.info("Successfully revoked data source access")
        return response.json()


async def _call_check_data_source_access(
    kb_id: int,
    user_id: Optional[str] = None,
    org_id: Optional[str] = None,
    team_id: Optional[str] = None,
) -> bool:
    """Checks whether a user, team, or org has access to a given knowledge base.

    Args:
        kb_id (int): Knowledge base ID to check access for.
        user_id (Optional[str]): Optional user ID.
        org_id (Optional[str]): Optional organization ID.
        team_id (Optional[str]): Optional team ID.

    Returns:
        bool: True if access is granted, False otherwise.

    Raises:
        ApiException: If the request fails with non-200 status.
    """

    logger.info("Checking datasource access")

    params = {
        "datasource_id": kb_id,
        "user_id": str(user_id) if user_id else None,
        "team_id": team_id if team_id else None,
        "org_id": str(org_id) if org_id else None,
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{loaded_config.locksmith_main_private_url}/v1.0/datasources/check/access",
            json=params,
            headers={"Content-Type": APP_SLASH_JSON},
        )

        if response.status_code != 200:
            logger.error(f"Failed to check data source access: {response.text}")
            raise ApiException(
                f"Failed with status {response.status_code}: {response.text}"
            )

        logger.info("Successfully checked data source access")
        return response.json()["data"]["access"]


async def _call_get_accessible_datasources_by_user(user_data: UserData) -> dict:
    """Retrieves all knowledge base IDs accessible by a given user.

    Args:
        user_data (UserData): The requesting user's information.

    Returns:
        dict: Dictionary containing categorized lists of accessible knowledge bases.

    Raises:
        ApiException: If the request fails.
    """

    logger.info("Calling get_accessible_datasources_by_user API")

    params = {
        "user_id": str(user_data.userId),
        "org_id": str(user_data.orgId) if user_data.orgId else None,
    }

    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"{loaded_config.locksmith_main_private_url}/v1.0/datasources/access",
            params=params,
            headers={"Content-Type": APP_SLASH_JSON},
        )

        if response.status_code != 200:
            logger.error(f"Failed to get accessible datasources: {response.text}")
            raise ApiException(
                f"Failed with status {response.status_code}: {response.text}"
            )

        logger.info("Successfully fetched accessible datasources")
        return response.json()["data"]


async def _call_get_org_accessible_datasources(org_id: str) -> List[int]:
    """
    Get all knowledge base IDs that an organization has access to.

    Args:
        org_id: Organization ID to check access for

    Returns:
        List of knowledge base IDs that the organization has access to
    """
    logger.info(f"Getting accessible datasources for org_id={org_id}")

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{loaded_config.locksmith_main_private_url}/v1.0/datasources/access/org/{org_id}",
                headers={"Content-Type": APP_SLASH_JSON},
            )

            if response.status_code != 200:
                logger.error(
                    f"Failed to get org accessible datasources: {response.text}"
                )
                raise ApiException(
                    f"Failed with status {response.status_code}: {response.text}"
                )

            # Extract all datasource IDs from the response
            datasources = response.json().get("data", [])
            kb_ids = [
                datasource.get("datasource_id")
                for datasource in datasources
                if datasource.get("datasource_id")
            ]

            logger.info(
                f"Organization {org_id} has access to {len(kb_ids)} datasources"
            )
            return kb_ids

    except Exception:
        logger.error(f"Error getting org accessible datasources", exc_info=True)
        raise ApiException(f"Failed to get organization accessible datasources")


async def _call_get_team_accessible_datasources(team_id: str) -> List[int]:
    """
    Get all knowledge base IDs that a team has access to.

    Args:
        team_id: Team ID to check access for

    Returns:
        List of knowledge base IDs that the team has access to
    """
    logger.info(f"Getting accessible datasources for team_id={team_id}")

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{loaded_config.locksmith_main_private_url}/v1.0/datasources/access/team/{team_id}",
                headers={"Content-Type": APP_SLASH_JSON},
            )

            if response.status_code != 200:
                logger.error(
                    f"Failed to get team accessible datasources: {response.text}"
                )
                raise ApiException(
                    f"Failed with status {response.status_code}: {response.text}"
                )

            # Extract all datasource IDs from the response
            datasources = response.json().get("data", [])
            kb_ids = [
                datasource.get("datasource_id")
                for datasource in datasources
                if datasource.get("datasource_id")
            ]

            logger.info(f"Team {team_id} has access to {len(kb_ids)} datasources")
            return kb_ids

    except Exception:
        logger.error(f"Error getting team accessible datasources", exc_info=True)
        raise ApiException(f"Failed to get team accessible datasources")


async def _call_get_datasource_access_details(datasource_id: int) -> dict:
    """
    Get all access details for a specific datasource/knowledge base.

    Args:
        datasource_id: The ID of the datasource to get access details for

    Returns:
        Dictionary containing users, teams, and organizations with access to the datasource
    """
    logger.info(f"Getting access details for datasource_id={datasource_id}")

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{loaded_config.locksmith_main_private_url}/v1.0/datasources/access/datasource/{datasource_id}",
                headers={"Content-Type": APP_SLASH_JSON},
            )

            if response.status_code != 200:
                logger.error(
                    f"Failed to get datasource access details: {response.text}"
                )
                raise ApiException(
                    f"Failed with status {response.status_code}: {response.text}"
                )

            access_data = response.json().get("data", {})
            logger.info(
                f"Successfully retrieved access details for datasource {datasource_id}"
            )
            return access_data

    except Exception:
        logger.error(f"Error getting datasource access details", exc_info=True)
        raise ApiException(f"Failed to get datasource access details")


async def _call_revoke_specific_data_source_access(
    knowledge_base_id: int,
    user_id: Optional[str] = None,
    team_id: Optional[str] = None,
    org_id: Optional[str] = None,
) -> dict:
    """
    Revoke access to a knowledge base for a specific user, team, or organization.

    Args:
        knowledge_base_id: ID of the knowledge base
        user_id: Optional user ID to revoke access for
        team_id: Optional team ID to revoke access for
        org_id: Optional organization ID to revoke access for

    Returns:
        Response from the locksmith service
    """

    payload = {
        "datasource_id": knowledge_base_id,
        "user_id": user_id,
        "team_id": team_id,
        "org_id": org_id,
    }
    logger.info("Revoking specific data source access", extra=payload)

    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{loaded_config.locksmith_main_private_url}/v1.0/datasources/specific/access",
            json=payload,
            headers={"Content-Type": APP_SLASH_JSON},
        )

        if response.status_code != 200:
            logger.error(
                f"Failed to revoke specific data source access: {response.text}"
            )
            raise ApiException(f"Failed to revoke access")

        logger.info("Successfully revoked specific data source access")
        return response.json()
