from config.logging import logger
from config.settings import loaded_config
from knowledge_base.dao import KnowledgeBaseDao
from knowledge_base.models import KBState
from knowledge_base.serializers import KnowledgeBaseRequest, ProviderContext
from knowledge_base.service import KnowledgeBaseService
from utils.connection_handler import <PERSON>Hand<PERSON>


async def update_kb_cron():
    """Periodic task to refresh all eligible knowledge bases.

    Fetches active KBs, constructs request objects from stored metadata, and triggers
    the `start_loading` flow for each. Manages rollback and error handling per KB.

    Intended to be triggered on a schedule (e.g., via cron or Celery beat).
    """

    logger.info("Starting update_kb_cron")
    connection_handler = None
    session = None
    try:
        connection_handler = ConnectionHandler(connection_manager=loaded_config.connection_manager)
        session = connection_handler.session

        # Create DAOs and service
        kb_dao = KnowledgeBaseDao(session)
        kb_service = KnowledgeBaseService(session)

        # Get all active knowledge bases using the DAO method
        knowledge_bases = await kb_dao.get_knowledge_bases_for_cron()

        logger.info(f"Found {len(knowledge_bases)} knowledge bases to update")

        for kb in knowledge_bases:
            try:
                # Skip if no settings_json or credentials
                if not kb.settings_json or 'credentials' not in kb.settings_json:
                    logger.warning(f"Knowledge base {kb.id} has no credentials in settings_json, skipping")
                    continue

                # Create provider context from stored settings
                provider_context = ProviderContext(
                    kb_id=str(kb.id),
                    provider=kb.settings_json.get('provider'),
                    credentials=kb.settings_json.get('credentials', {}),
                    kb_name=kb.name
                )

                # Create request object
                request = KnowledgeBaseRequest(
                    add_context_through_provider=provider_context,
                    team_id=kb.team_id,
                    context="add_context_through_provider"
                )

                # Update KB state to indexing
                await kb_dao.update_state_by_id(kb.id, KBState.updating)

                # Call the existing start_loading method
                result = await kb_service.start_loading(request=request, connection_handler=connection_handler, user_data=None)

                if not result.success:
                    # Log the error but don't mark KB as error
                    logger.warning(f"Update process for KB {kb.id} didn't succeed: {result.message}")
                    # Restore previous state instead of setting to error
                    await kb_dao.update_state_by_id(kb.id, KBState.update_error)
                    await session.commit()
                else:
                    logger.info(f"Successfully started update for KB {kb.id}")

            except Exception as e: # pylint: disable=broad-exception-caught
                # Rollback the transaction for this KB and continue with the next one
                await session.rollback()
                logger.warning(f"Error during update process for knowledge base {kb.id}: {str(e)}")

                try:
                    # Restore previous state instead of setting to error
                    await kb_dao.update_state_by_id(kb.id, KBState.update_error)
                    await session.commit()
                except Exception as inner_e: # pylint: disable=broad-exception-caught
                    await session.rollback()
                    logger.error(f"Failed to restore KB {kb.id} state: {str(inner_e)}")

                continue

    except Exception as e: # pylint: disable=broad-exception-caught
        if session:
            await session.rollback()
        logger.error(f"Error in update_kb_cron: {str(e)}")
    finally:
        if connection_handler:
            await connection_handler.session.close()