import asyncio
import time
import sys
import aiofiles

from config.logging import logger
from config.settings import loaded_config
from knowledge_base.cron.tasks import update_kb_cron
from utils.connection_manager import ConnectionManager


K8S_SCHEDULE = {
    'update_kb': {
        'task': update_kb_cron,
        'options': {'queue': 'polling_queue'},
        'args': ()
    }
}

run_healthz = True


async def _healthz():
    """Periodically writes to a health check file to signal liveness.

    Runs until `run_healthz` is set to False.
    """

    global run_healthz
    while run_healthz:
        async with aiofiles.open("/tmp/_healthz", mode="w") as out:
            await out.write(" ")
            await out.flush()
            print("Healthz check.")
        await asyncio.sleep(30)

    print("Healthz check stopped")


async def run(loop):
    """Executes the configured cron task based on environment configuration.

    Fetches task metadata, runs it asynchronously, and gracefully terminates health checks.
    """

    global run_healthz
    cron_job_name = loaded_config.CRON_JOB_UPDATE_KB  # Get from environment or config
    logger.info(f"Running cron_job_name | {cron_job_name}")
    cron_job_data = K8S_SCHEDULE[cron_job_name]
    logger.info(cron_job_data)

    cron_task = cron_job_data['task']
    logger.info(f'cron_task: {cron_task.__name__}')

    # For async tasks, we don't need run_in_executor
    await cron_task()

    logger.info("Cron job completed")
    time.sleep(10)  # Give some time for any pending operations
    run_healthz = False


try:
    loop = asyncio.get_event_loop()
    connection_manager = ConnectionManager(
        db_url=loaded_config.db_url,
        db_echo=loaded_config.db_echo
    )
    loaded_config.connection_manager = connection_manager
    loop.run_until_complete(asyncio.gather(run(loop), _healthz()))
    loop.close()
    sys.exit(0)
except Exception as e: # pylint: disable=broad-exception-caught
    print(f"Error running cron job: {str(e)}")
    sys.exit(1)