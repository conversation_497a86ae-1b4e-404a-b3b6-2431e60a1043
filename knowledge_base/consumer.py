from config.logging import logger
from config.settings import loaded_config
from knowledge_base.dao import (
    SourceItemDao, ChunkDao, IngestionRunDao, KnowledgeBaseDao, IngestionErrorDao
)
from knowledge_base.factory import KnowledgeBaseFactory
from knowledge_base.models import KBState, IntegrationType
from utils.connection_handler import ConnectionHandler
from utils.vector_db import ElasticSearchAdapter
from utils.vector_db.elastic_sql_adapter import ElasticsearchSQLAdapter
from utils.vector_db.embeddings import EmbeddingGenerator
import os
from typing import Dict, List, Any

async def knowledge_base_consumer(message):
    """Main entrypoint for consuming and processing a knowledge base ingestion message.

    This function coordinates the full ETL process:
    - Extracts data using an appropriate extractor.
    - Transforms it using a transformer into embeddings and chunked documents.
    - Loads it into the vector store (Elasticsearch).
    - Updates the ingestion run and knowledge base status.

    Args:
        message (dict): Kafka message containing the payload and ingestion metadata.

    Raises:
        Logs and raises errors during the ETL stages, and rolls back on failure.
    """

    logger.info(f"Processing knowledge base consumer: {message}")
    connection_handler = None
    extractor = None
    transformer = None
    loader = None
    ingestion_run_dao = None
    knowledge_base_dao = None
    payload_data = None
    ingestion_error_dao = None
    try:
        payload_data = message.get('payload', {}).get('payload', {})
        logger.info(f"Payload data in consumer: {payload_data}")

        extractor, transformer, loader = await get_etl(payload_data)
        connection_handler = ConnectionHandler(connection_manager=loaded_config.connection_manager)
        session = connection_handler.session
        embedder = EmbeddingGenerator(api_key=loaded_config.openai_gpt4o_api_key)
        source_item_dao = SourceItemDao(session)
        chunk_dao = ChunkDao(session)
        ingestion_run_dao = IngestionRunDao(session)
        knowledge_base_dao = KnowledgeBaseDao(session)
        ingestion_error_dao = IngestionErrorDao(session)

        if payload_data.get("type", None) == "sql_data_analyst":
            extracted_data = transformed_data = payload_data.get("tables")
            result = await loader.load({
                "index_name": "data_analyst_index",
                "documents": transformed_data,
                "knowledge_base_id":payload_data["knowledge_base_id"],
                "tables": payload_data["tables"],
                "database_name": payload_data["database_name"]
            })
            logger.info(f"Load Result: {result}")
        else:
            extracted_data = await extractor.extract()
            transformed_data = None

            folder_structure = await build_folder_structure_from_paths(extracted_data)
            await knowledge_base_dao.update_memory_key_by_id(
                kb_id=int(payload_data["knowledge_base_id"]),
                key="folder_structure",
                value=folder_structure,
                commit=True
            )
            source_items, updated_extracted_data = await convert_extracted_data_to_bulk_insert(
                extracted_data,
                int(payload_data["knowledge_base_id"]),
                payload_data["mode"],
                source_item_dao, chunk_dao, ElasticSearchAdapter(), "knowledge_base_index"
            )

            await source_item_dao.bulk_insert(source_items)

            transformed_data = await transformer.transform(
                updated_extracted_data, 
                embedder, 
                knowledge_base_id=payload_data["knowledge_base_id"]
            )

            await chunk_dao.bulk_insert(await convert_transformed_data_to_chunks_dicts(transformed_data))

            result = await loader.load({"index_name": "knowledge_base_index", "documents": transformed_data})
            logger.info(f"Load Result: {result}")

        if payload_data["mode"] == "ADD":
            logger.info(f"Mode: {payload_data['mode']}")
            await ingestion_run_dao.update_run_summary(
                run_id=int(payload_data["ingestion_run_id"]),
                status="completed",
                total_source_item=len(extracted_data),
                total_chunks=len(transformed_data),
                failed_chunk_elastic_doc_id=[]
            )
        else:  # UPDATE mode
            # Get all source items for this knowledge base
            source_items = await source_item_dao.get_all_by_kb_id(int(payload_data["knowledge_base_id"]))
            total_source_items = len(source_items)

            # Get source item IDs
            source_item_ids = [item.id for item in source_items]

            # Get total chunks for these source items
            total_chunks = await chunk_dao.get_count_by_source_item_ids(source_item_ids)

            await ingestion_run_dao.update_run_summary(
                run_id=int(payload_data["ingestion_run_id"]),
                status="completed",
                total_source_item=total_source_items,
                total_chunks=total_chunks,
                failed_chunk_elastic_doc_id=result.get("failed", [])
            )
        logger.info(f"Updated ingestion run summary")

        await knowledge_base_dao.update_state_by_id(int(payload_data["knowledge_base_id"]), KBState.ready, commit=True)
        logger.info("Updated knowledge base state")

    except Exception as e: # pylint: disable=broad-exception-caught
        logger.error(f"Error processing knowledge base consumer: {e}")
        if ingestion_error_dao:
            await ingestion_error_dao.create_ingestion_error(
                run_id=payload_data["ingestion_run_id"],
                error_message=str(e)
            )

        await ingestion_run_dao.update_run_summary(
            run_id=payload_data["ingestion_run_id"],
            status="failed",
            total_source_item=0,
            total_chunks=0,
            failed_chunk_elastic_doc_id=[]
        ) if ingestion_run_dao else None

        if knowledge_base_dao:
            error_state = (
                KBState.error if payload_data["mode"] == "ADD" 
                else KBState.update_error
            )
            await knowledge_base_dao.update_state_by_id(
                payload_data["knowledge_base_id"], error_state, commit=True
            )
        await connection_handler.session.rollback()
    finally:
        await connection_handler.session.close()


async def convert_transformed_data_to_chunks_dicts(
    transformed_data: list
) -> list[dict]:
    """Converts transformed documents into a format suitable for bulk chunk database insertion.

    Args:
        transformed_data (list): List of transformed documents with metadata.

    Returns:
        list[dict]: List of chunk records formatted for DB insert.
    """

    chunks = []

    for doc in transformed_data:
        metadata = doc.get("metadata") or {}
        chunk_dict = {
            "source_item_id": doc.get("id").split("#")[0],
            "chunk_index": metadata.get("chunk_number", 1),
            "es_doc_id": doc.get("id"),
            "start_offset": metadata.get("start_line"),
            "end_offset":  metadata.get("end_line"),
            "checksum": doc.get("chunk_checksum") or None,
            "metadata_json": metadata or {}
        }

        chunks.append(chunk_dict)

    return chunks

async def convert_extracted_data_to_bulk_insert(
    extracted_data: list[dict],
    knowledge_base_id: int,
    mode: str,
    source_item_dao: SourceItemDao,
    chunk_dao: ChunkDao,
    elastic_adapter,
    index_name: str,
) -> tuple[list[dict], list[dict]]:
    """Compares extracted files with existing source items to determine which to add, update, or delete.

    Performs checksum comparison, prepares source items, deletes outdated ones,
    and returns updated data for transformation.

    Args:
        extracted_data (list[dict]): Newly extracted file records.
        knowledge_base_id (int): The ID of the current knowledge base.
        mode (str): Either 'ADD' or 'UPDATE'.
        source_item_dao (SourceItemDao): DAO for managing source items.
        chunk_dao (ChunkDao): DAO for managing chunks.
        elastic_adapter: Vector DB adapter for deleting old records from Elasticsearch.
        index_name (str): The name of the Elasticsearch index.

    Returns:
        tuple: (list of source item dicts, list of extracted records to be transformed)
    """

    source_items = []
    updated_extracted_data = []

    # Fetch existing source items
    existing_source_items = await source_item_dao.get_all_by_kb_id(knowledge_base_id)
    existing_items_dict = {item.logical_path: item for item in existing_source_items}

    # Process files based on mode
    if mode == "ADD":
        # For "ADD" mode, directly prepare extracted data for insertion
        source_items = [await prepare_source_item(record, knowledge_base_id) for record in extracted_data]
        updated_extracted_data = extracted_data

    elif mode == "UPDATE":
        # Separate extracted data into files to add, update, or delete
        files_to_add = []
        files_to_update = []
        files_to_delete = []

        # Classify extracted data
        for record in extracted_data:
            logical_path = record.get("path")
            checksum = record.get("checksum")
            existing_source_item = existing_items_dict.get(logical_path)

            if existing_source_item:
                if existing_source_item.checksum != checksum:
                    files_to_update.append(record)  # Files with checksum mismatch
            else:
                files_to_add.append(record)  # New files to be added

        # Determine files to delete (not in extracted data but in DB)
        files_to_delete = list(existing_items_dict.keys() - {record.get("path") for record in extracted_data})

        # Prepare files for bulk insert and deletion
        source_items.extend([await prepare_source_item(record, knowledge_base_id) for record in files_to_add])
        updated_extracted_data.extend(files_to_add)

        # Handle file updates (delete old, insert new)
        for record in files_to_update:
            logical_path = record.get("path")
            existing_source_item = existing_items_dict[logical_path]
            await source_item_dao.delete_by_id(existing_source_item.id)
            await elastic_adapter.delete_document_by_id_prefix(index_name, f"{existing_source_item.id}#")
            source_items.append(await prepare_source_item(record, knowledge_base_id))
            updated_extracted_data.append(record)

        # Handle file deletions
        for logical_path in files_to_delete:
            existing_source_item = existing_items_dict[logical_path]
            await source_item_dao.delete_by_id(existing_source_item.id)
            await elastic_adapter.delete_document_by_id_prefix(index_name, f"{existing_source_item.id}#")

    return source_items, updated_extracted_data

async def prepare_source_item(record: dict, knowledge_base_id: int) -> dict:
    """Formats a single extracted file record into a source item structure for DB insertion.

    Args:
        record (dict): A single file record from the extractor.
        knowledge_base_id (int): Knowledge base ID to associate the item with.

    Returns:
        dict: Formatted source item ready for bulk insert.
    """

    return {
        "kb_id": knowledge_base_id,
        "provider_item_id": record.get("provider_item_id"),
        "kind": record.get("kind"),
        "logical_path": record.get("path"),
        "version_tag": str(record.get("version_tag")),
        "checksum": record.get("checksum"),
        "metadata_json": {},
        "id": record.get('uuid')
    }

async def build_folder_structure_from_paths(file_records: List[dict]) -> Dict[str, Any]:
    """Constructs a nested folder structure from a list of file paths.

    Args:
        file_records (List[dict]): List of file records with 'path' fields.

    Returns:
        Dict[str, Any]: Nested dictionary representing the folder tree.
    """

    folder_tree = {}
    for record in file_records:
        path = record["path"]
        parts = path.split(os.sep)

        current = folder_tree
        for i, part in enumerate(parts):
            is_file = "." in part and i == len(parts) - 1
            if is_file:
                current[part] = None
            else:
                if part not in current:
                    current[part] = {}
                current = current[part]

    return folder_tree

async def get_etl(payload: dict):
    """Initializes the ETL pipeline components based on the integration provider.

    Args:
        payload (dict): Kafka message payload containing provider config and metadata.

    Returns:
        tuple: (extractor, transformer, loader), any of which may be None depending on provider.

    Raises:
        ValueError: If the provider is not supported.
    """

    provider = payload.get("provider")

    if provider == "azure_devops" or provider == "github" or provider == "gitlab":
        repo_url = payload.get("url")
        branch = payload.get("branch_name")
        pat = payload.get("pat")

        extractor = await KnowledgeBaseFactory.get_extractor(
            provider, repo_url=repo_url, branch_name=branch, pat=pat
        )
        transformer = await KnowledgeBaseFactory.get_transformer("repo_transformer")
        loader = await KnowledgeBaseFactory.get_loader(
            "elasticsearch", db_adapter=ElasticSearchAdapter()
        )
        return extractor, transformer, loader

    elif provider == "quip":
        pat = payload.get("pat")
        urls = payload.get("urls", [])

        extractor = await KnowledgeBaseFactory.get_extractor(
            provider, pat=pat, urls=urls, max_docs_per_kb=int(loaded_config.max_docs_per_kb)
        )
        transformer = await KnowledgeBaseFactory.get_transformer("quip_transformer")
        loader = await KnowledgeBaseFactory.get_loader("elasticsearch", db_adapter=ElasticSearchAdapter())
        return extractor, transformer, loader

    elif provider == IntegrationType.google_docs.value:
        docs = payload.get("docs", [])
        token = payload.get("token")

        extractor = await KnowledgeBaseFactory.get_extractor(
            provider, docs=docs, token=token
        )
        transformer = await KnowledgeBaseFactory.get_transformer("google_docs_transformer")
        loader = await KnowledgeBaseFactory.get_loader(
            "elasticsearch", db_adapter=ElasticSearchAdapter()
        )
        return extractor, transformer, loader

    elif provider == IntegrationType.bigquery.value:
        loader = await KnowledgeBaseFactory.get_loader(
            "sql_schema_loader", db_adapter=ElasticsearchSQLAdapter()
        )
        transformer = None
        extractor = None
        return extractor, transformer, loader

    elif provider == IntegrationType.fynix_extension.value:
        loader = await KnowledgeBaseFactory.get_loader(
            "elasticsearch", db_adapter=ElasticSearchAdapter()
        )
        transformer = await KnowledgeBaseFactory.get_transformer("repo_transformer")
        extractor = None
        return extractor, transformer, loader

    raise ValueError(f"Unsupported provider: {provider}")
