import asyncio
import hashlib
import re
import uuid
from urllib.parse import urlparse

import httpx
from bs4 import BeautifulSoup

from config.logging import logger
from knowledge_base.constants import INVALID_FOLDER_CHARS_REGEX
from knowledge_base.extractors.base import DataExtractor
from knowledge_base.models import SourceItemKind


class QuipExtractor(DataExtractor):
    def __init__(self, pat: str, urls: list[str], max_docs_per_kb: int = 10):
        """Initializes the Quip extractor with access credentials and target URLs.

        Args:
            pat (str): Personal Access Token for Quip API.
            urls (list[str]): List of Quip document or folder URLs.
            max_docs_per_kb (int): Maximum number of documents to extract per knowledge base.
        """

        self.pat = pat
        self.urls = urls
        self.max_docs_per_kb = max_docs_per_kb
        self.base_api_url = "https://platform.quip.com/1"
        self.headers = {"Authorization": f"Bearer {self.pat}"}
        self.semaphore = asyncio.Semaphore(5)  # Reduced for stability
        self.folder_name_cache = {}  # Cache folder names
        self.thread_title_cache = {}  # Cache thread titles
        self.folder_content_cache = {}  # Cache folder contents
        self.client = httpx.AsyncClient(timeout=30.0)

    async def __aenter__(self):
        """Enter the async context manager, returning the current instance.

        Returns:
            QuipExtractor: The initialized instance.
        """

        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit the async context manager and close the HTTP client."""

        await self.client.aclose()

    async def extract(self, **kwargs) -> list[dict]:
        """Fetch data from Quip and return a list of records."""
        try:
            logger.info(f"Extracting Quip content from: {self.urls}")
            max_total_docs = self.max_docs_per_kb  # Overall maximum docs

            thread_ids = []
            folder_mapping = {}  # Map thread_ids to their folder paths and titles

            # Process each URL sequentially instead of in parallel
            for url in self.urls:
                await self._process_url(url, folder_mapping, thread_ids, max_total_docs)

            # Process all collected thread IDs sequentially
            all_records = []

            for thread_id in thread_ids:
                # Use the folder mapping if available
                mapping = folder_mapping.get(thread_id, {"path": "", "title": ""})
                folder_path = mapping["path"]
                thread_title = mapping["title"] or self.thread_title_cache.get(
                    thread_id, ""
                )
                source_url = mapping.get(
                    "source_url", ""
                )  # Get the source URL from mapping

                record = await self._get_thread_content(
                    thread_id, folder_path, thread_title, source_url
                )
                if record:
                    all_records.append(record)

            logger.info(f"Extracted {len(all_records)} document(s) from Quip.")
            return all_records
        finally:
            await self.client.aclose()

    async def _extract_id_from_url(self, url: str) -> str | None:
        """Extract Quip ID from URL, handling both document and folder URLs"""
        try:
            parsed = urlparse(url)
            path = parsed.path.strip("/")

            # Log the parsed path for debugging
            logger.info(f"Parsed URL path: {path}")

            # Quip URLs typically have the format: domain.com/ID/optional-title
            # We need just the ID part
            parts = path.split("/")

            # The ID is always the first part after the domain
            quip_id = parts[0] if parts else None

            logger.info(f"Extracted Quip ID: {quip_id}")
            return quip_id
        except Exception as e:  # pylint: disable=broad-exception-caught
            logger.error(f"Error extracting ID from URL {url}: {str(e)}")
            return None

    async def _get_item_type(self, quip_id: str) -> str | None:
        """Determine if it's a folder or thread"""
        async with self.semaphore:
            folder_url = f"{self.base_api_url}/folders/{quip_id}"
            thread_url = f"{self.base_api_url}/threads/{quip_id}"

            # Send both requests concurrently
            try:
                folder_task = self.client.get(folder_url, headers=self.headers)
                thread_task = self.client.get(thread_url, headers=self.headers)

                folder_resp, thread_resp = await asyncio.gather(
                    folder_task, thread_task
                )

                logger.info(f"Folder response status: {folder_resp.status_code}")
                logger.info(f"Thread response status: {thread_resp.status_code}")

                if folder_resp.status_code == 200:
                    return "folder"
                if thread_resp.status_code == 200:
                    return "thread"

                # Log the error responses for debugging
                logger.error(f"Folder API error: {folder_resp.text}")
                logger.error(f"Thread API error: {thread_resp.text}")

                # Check if this might be a thread ID with additional path components
                # Try with just the first part of the ID
                if "/" in quip_id:
                    base_id = quip_id.split("/")[0]
                    logger.info(f"Trying with base ID: {base_id}")
                    return await self._get_item_type(base_id)

                return None
            except Exception as e:  # pylint: disable=broad-exception-caught
                logger.error(f"Error determining item type: {str(e)}")
                return None

    async def _get_folder_name(self, folder_id: str) -> str:
        """Get the name of a folder with caching"""
        # Check cache first
        if folder_id in self.folder_name_cache:
            return self.folder_name_cache[folder_id]

        try:
            async with self.semaphore:
                folder_url = f"{self.base_api_url}/folders/{folder_id}"
                response = await self.client.get(folder_url, headers=self.headers)
                if response.status_code == 200:
                    folder_data = response.json()
                    # Cache the folder content for later use
                    self.folder_content_cache[folder_id] = folder_data

                    # Try to get title from different places in the response
                    folder_name = folder_data.get("title")

                    # If no title directly, try to get from folder or thread objects
                    if not folder_name and "folder" in folder_data:
                        folder_name = folder_data["folder"].get("title")

                    if not folder_name and "thread" in folder_data:
                        folder_name = folder_data["thread"].get("title")

                    # If we found a name, cache it and return
                    if folder_name:
                        # Clean the folder name for filesystem safety
                        safe_folder_name = re.sub(
                            INVALID_FOLDER_CHARS_REGEX, "-", folder_name
                        )
                        self.folder_name_cache[folder_id] = safe_folder_name
                        return safe_folder_name

                    # If we get here, we couldn't find a title
                    logger.warning(f"No title found for folder {folder_id}")
                    return f"Folder-{folder_id[:8]}"
                else:
                    logger.warning(f"Failed to get folder name: {response.status_code}")
                    return f"Folder-{folder_id[:8]}"
        except Exception as e:  # pylint: disable=broad-exception-caught
            logger.error(f"Error getting folder name for {folder_id}: {str(e)}")
            return f"Folder-{folder_id[:8]}"

    async def _process_url(self, url, folder_mapping, thread_ids, max_total_docs):
        """Process a single URL (document or folder)"""
        quip_id = await self._extract_id_from_url(url)
        if not quip_id:
            logger.warning(f"Invalid Quip URL format: {url}")
            return

        logger.info(f"Processing Quip ID: {quip_id} from URL: {url}")

        item_type = await self._get_item_type(quip_id)
        logger.info(f"Determined item type: {item_type} for ID: {quip_id}")

        # If it's a document, add it directly
        if item_type == "thread":
            thread_title = await self._get_thread_title(quip_id)
            logger.info(f"Found thread with title: {thread_title or 'Untitled'}")
            folder_mapping[quip_id] = {
                "path": "",  # Root path
                "title": thread_title or f"Document-{quip_id[:8]}",
                "source_url": url,  # Store the original URL
            }
            thread_ids.append(quip_id)

        # If it's a folder, process it
        elif item_type == "folder":
            folder_name = await self._get_folder_name(quip_id)
            if not folder_name:
                # Try to extract folder name from URL if API doesn't provide it
                url_parts = url.split("/")
                if len(url_parts) > 4:  # Has a path component after the ID
                    folder_name = url_parts[-1]
                    logger.info(f"Using folder name from URL: {folder_name}")
                else:
                    folder_name = f"Folder-{quip_id[:8]}"

            logger.info(f"Processing folder: {folder_name} (ID: {quip_id})")

            # Process the folder recursively with global document limit
            await self._process_folder_recursively(
                quip_id,
                folder_name,
                folder_mapping,
                thread_ids,
                max_total_docs,
                url,  # Pass the original URL
            )
        else:
            logger.warning(f"Unknown item type for ID: {quip_id}, URL: {url}")

    async def _process_folder_recursively(
        self,
        folder_id,
        folder_name,
        folder_mapping,
        thread_ids,
        max_total_docs,
        source_url,
    ):
        """Process a folder and its subfolders recursively"""
        # Stop if we've already reached the maximum total documents
        if len(thread_ids) >= max_total_docs:
            return

        # Check if we already have the folder content cached
        folder_data = self.folder_content_cache.get(folder_id)

        if not folder_data:
            try:
                async with self.semaphore:
                    folder_url = f"{self.base_api_url}/folders/{folder_id}"
                    logger.info(f"Fetching folder data from: {folder_url}")
                    response = await self.client.get(folder_url, headers=self.headers)
                    if response.status_code != 200:
                        logger.warning(
                            f"Failed to fetch folder contents: {response.status_code}, {response.text}"
                        )
                        return
                    folder_data = response.json()
                    logger.info(f"Folder data received: {folder_data.keys()}")
                    # Cache the folder data for future use
                    self.folder_content_cache[folder_id] = folder_data
            except Exception as e:  # pylint: disable=broad-exception-caught
                logger.error(f"Error fetching folder {folder_id}: {str(e)}")
                return

        # Process threads and subfolders
        children = folder_data.get("children", [])
        logger.info(f"Found {len(children)} children in folder {folder_id}")

        thread_children = [child for child in children if child.get("thread_id")]
        folder_children = [child for child in children if child.get("folder_id")]

        logger.info(
            f"Found {len(thread_children)} threads and {len(folder_children)} subfolders"
        )

        # Process threads first (they're usually faster)
        for child in thread_children:
            if len(thread_ids) >= max_total_docs:
                break

            thread_id = child["thread_id"]
            thread_title = None

            # Try to get title from the child data first
            if "thread" in child and child["thread"].get("title"):
                thread_title = child["thread"]["title"]
                self.thread_title_cache[thread_id] = thread_title

            # If no title, make a separate API call to get it
            if not thread_title:
                thread_title = await self._get_thread_title(thread_id)

            # Use a more descriptive fallback if still no title
            if not thread_title:
                thread_title = f"Document-{thread_id[:8]}"

            # Store thread info for later processing
            folder_mapping[thread_id] = {
                "path": folder_name,
                "title": thread_title,
                "source_url": source_url,
            }
            thread_ids.append(thread_id)

            logger.info(
                f"Adding document '{thread_title}' from folder {folder_name} ({len(thread_ids)}/{max_total_docs})"
            )

        # Process subfolders if we haven't reached the limit
        if len(thread_ids) < max_total_docs:
            for child in folder_children:
                child_folder_id = child["folder_id"]
                child_folder_name = await self._resolve_folder_name(
                    child, child_folder_id
                )

                self.folder_name_cache[child_folder_id] = child_folder_name

                # Create the nested path
                nested_path = (
                    f"{folder_name}/{child_folder_name}"
                    if folder_name
                    else child_folder_name
                )
                logger.info(f"Processing nested folder: {nested_path}")

                # Process this subfolder
                await self._process_folder_recursively(
                    child_folder_id,
                    nested_path,
                    folder_mapping,
                    thread_ids,
                    max_total_docs,
                    source_url,
                )

                # Stop if we've reached the limit
                if len(thread_ids) >= max_total_docs:
                    break

    async def _resolve_folder_name(self, child: dict, folder_id: str) -> str:
        """Resolve folder name from child data, cache, or fallback."""
        name = None

        if "folder" in child and child["folder"].get("title"):
            name = child["folder"]["title"]
        elif "thread" in child and child["thread"].get("title"):
            name = child["thread"]["title"]
        elif folder_id in self.folder_name_cache:
            name = self.folder_name_cache[folder_id]
        else:
            name = await self._get_folder_name(folder_id)
            if not name:
                name = f"Folder-{folder_id[:8]}"

        return re.sub(INVALID_FOLDER_CHARS_REGEX, "-", name)

    async def _get_thread_title(self, thread_id: str) -> str:
        """Get just the title of a thread with caching"""
        # Check cache first
        if thread_id in self.thread_title_cache:
            return self.thread_title_cache[thread_id]

        try:
            async with self.semaphore:
                response = await self.client.get(
                    f"{self.base_api_url}/threads/{thread_id}", headers=self.headers
                )
                if response.status_code == 200:
                    thread = response.json()

                    # Try to get title from thread metadata
                    title = thread.get("thread", {}).get("title", "")

                    # If no title in metadata, try to extract from HTML content
                    if not title and "html" in thread:
                        try:
                            soup = BeautifulSoup(thread["html"], "html.parser")

                            # Try to find title in this order: h1, h2, h3, first paragraph
                            for tag in ["h1", "h2", "h3"]:
                                element = soup.find(tag)
                                if element and element.text.strip():
                                    title = element.text.strip()
                                    break

                            # If no headings, try first paragraph
                            if not title:
                                p = soup.find("p")
                                if p and p.text.strip():
                                    text = p.text.strip()
                                    # Limit paragraph text to reasonable length
                                    if len(text) > 40:
                                        title = text[:40] + "..."
                                    else:
                                        title = text
                        except Exception as e:  # pylint: disable=broad-exception-caught
                            logger.error(f"Error extracting title from HTML: {str(e)}")

                    # Store in cache if we found a title
                    if title:
                        self.thread_title_cache[thread_id] = title

                    return title
                return ""
        except Exception as e:  # pylint: disable=broad-exception-caught
            logger.error(f"Error getting thread title for {thread_id}: {str(e)}")
            return ""

    async def _get_thread_content(
        self, thread_id, folder_path="", thread_title="", source_url=""
    ):
        """Get the content of a thread with rate limiting"""
        try:
            # Check if we already have the title in cache
            if not thread_title and thread_id in self.thread_title_cache:
                thread_title = self.thread_title_cache[thread_id]

            async with self.semaphore:
                response = await self.client.get(
                    f"{self.base_api_url}/threads/{thread_id}", headers=self.headers
                )
                if response.status_code != 200:
                    logger.warning(
                        f"Failed to fetch thread {thread_id}: {response.status_code}"
                    )
                    return None

                thread = response.json()
                content = thread.get("html", "")
                if not content.strip():
                    logger.warning(f"Empty content for thread {thread_id}")
                    return None

                # Use provided thread title or get from response
                doc_title = thread_title
                if not doc_title:
                    # Try to get title from thread metadata
                    doc_title = thread.get("thread", {}).get("title", "")

                    # If no title in metadata, try to extract from HTML content
                    if not doc_title and content:
                        try:
                            soup = BeautifulSoup(content, "html.parser")
                            for tag in ["h1", "h2", "h3"]:
                                element = soup.find(tag)
                                if element and element.text.strip():
                                    doc_title = element.text.strip()
                                    break
                        except Exception as e:  # pylint: disable=broad-exception-caught
                            logger.error(f"Error extracting title from HTML: {str(e)}")

                # Use fallback if still no title - include thread ID for uniqueness
                if not doc_title:
                    doc_title = f"Document-{thread_id[:8]}"

                # Clean up filename to be safe for filesystems
                safe_filename = re.sub(INVALID_FOLDER_CHARS_REGEX, "-", doc_title)
                # Remove HTML tags if any remain in the title
                safe_filename = re.sub(r"<[^>]*>", "", safe_filename)
                # Ensure filename is not too long
                if len(safe_filename) > 100:
                    safe_filename = safe_filename[:97] + "..."

                path = f"https://quip.com/{thread_id}"

                return {
                    "path": path,
                    "content": content,
                    "version_tag": thread.get("thread", {}).get("updated_usec"),
                    "provider_item_id": thread_id,
                    "checksum": hashlib.sha256(content.encode()).hexdigest(),
                    "uuid": str(uuid.uuid4()),
                    "kind": SourceItemKind.file.value,
                    "source_url": source_url,  # Include the original URL
                }
        except Exception as e:  # pylint: disable=broad-exception-caught
            logger.error(f"Error processing thread {thread_id}: {str(e)}")
            return None

    async def is_project_public(self) -> bool:
        """Check if Quip content is public."""
        # Quip content is never public without authentication
        return False

    async def validate_credentials(self) -> dict:
        """Validate the Quip access token."""
        result = {"is_valid": False, "message": "Failed to validate Quip credentials"}

        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_api_url}/users/current", headers=self.headers
                )
                if response.status_code == 200:
                    result["is_valid"] = True
                    result["message"] = "Token is valid"
                    logger.info("Quip credentials validated successfully")
                else:
                    result["message"] = "Failed to authenticate Quip"
                    logger.warning(f"Quip authentication failed: {response.text}")
                return result
        except Exception as e:  # pylint: disable=broad-exception-caught
            result["message"] = str(e)
            logger.error(f"Error validating Quip credentials: {str(e)}")
            return result
