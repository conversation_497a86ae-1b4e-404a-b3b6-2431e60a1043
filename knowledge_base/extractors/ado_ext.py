import re
from base64 import b64encode
from urllib.parse import urlparse

import httpx

from config.logging import logger
from knowledge_base.constants import (
    INVALID_REPO_URL_FORMAT,
    REPO_URL_REQUIRED,
    ALL_CRED_ARE_VALID,
    BRANCH_NAME_REQUIRED,
)
from knowledge_base.extractors.base import GitRepoExtractor


class AzureDevopsRepoExtractor(GitRepoExtractor):
    async def _validate_url(self):
        """Validates the format of the Azure DevOps repository URL.

        Raises:
            ValueError: If the URL format is invalid.
        """

        pattern = r"^https://[^@]+@dev\.azure\.com/.+/.+/_git/.+$"
        if not re.match(pattern, self.repo_url):
            raise ValueError(f"Invalid Azure DevOps repository URL")

    async def _get_clone_url(self) -> str:
        """Constructs the clone URL for Azure DevOps using the personal access token.

        Returns:
            str: Authenticated HTTPS clone URL.

        Raises:
            ValueError: If the repository URL is malformed.
        """

        try:
            return f"https://{self.pat}@{self.repo_url.split('@')[1]}"
        except IndexError:
            raise ValueError(
                "Malformed Azure DevOps URL, missing '@' in the expected format"
            )

    async def is_project_public(self) -> bool:
        """Check if Azure DevOps project is public."""
        try:
            parsed = urlparse("https://" + self.repo_url.split("@")[1])
            path_parts = [p for p in parsed.path.strip("/").split("/") if p]
            if len(path_parts) < 2:
                logger.error(INVALID_REPO_URL_FORMAT)
                return False

            organization, project = path_parts[0], path_parts[1]
            url = (
                f"https://dev.azure.com/{organization}/_apis/projects/"
                f"{project}?api-version=6.0"
            )

            async with httpx.AsyncClient() as client:
                response = await client.get(url)
                if response.status_code == 200:
                    project_data = response.json()
                    visibility = project_data.get("visibility", "").lower()
                    return visibility == "public"
                elif response.status_code == 404:
                    logger.warning(f"Project not found: {organization}/{project}")
                    return False
                else:
                    logger.warning(
                        f"Failed to fetch project visibility: {response.status_code} - {response.text}"
                    )
                    return False
        except Exception as e:  # pylint: disable=broad-exception-caught
            logger.error(f"Error checking project visibility: {e}")
            return False

    async def validate_credentials(self) -> dict:
        """Validate URL, PAT, and branch existence.

        Returns a dictionary with validation status and messages.
        """
        result = {
            "is_valid": False,
            "url_valid": False,
            "token_valid": False,
            "branch_valid": False,
            "message": "",
        }

        try:
            # Validate URL format and parse components
            if not self.repo_url:
                result["message"] = REPO_URL_REQUIRED
                return result

            try:
                parsed = urlparse("https://" + self.repo_url.split("@")[1])
                path_parts = [p for p in parsed.path.strip("/").split("/") if p]
                organization, project = path_parts[0], path_parts[1]
                repo_name = path_parts[-1]
                result["url_valid"] = True
            except (IndexError, AttributeError):
                result["message"] = INVALID_REPO_URL_FORMAT
                return result

            # Prepare headers for API calls
            headers = {}
            if self.pat:  # Use pat instead of pat
                auth_str = b64encode(f":{self.pat}".encode()).decode()
                headers["Authorization"] = f"Basic {auth_str}"

            async with httpx.AsyncClient() as client:
                # Check repository existence and PAT validity
                repo_url = (
                    f"https://dev.azure.com/{organization}/{project}/"
                    f"_apis/git/repositories/{repo_name}?api-version=6.0"
                )
                response = await client.get(repo_url, headers=headers)

                if response.status_code == 302:
                    result["message"] = "Invalid access token"
                    return result
                elif response.status_code == 404:
                    result["message"] = (
                        f"Repository '{repo_name}' not found in {organization}/{project}"
                    )
                    return result
                elif response.status_code != 200:
                    result["message"] = f"Failed to access repository."
                    return result

                result["token_valid"] = True

                # Check branch existence
                if self.branch_name:
                    branch_url = (
                        f"https://dev.azure.com/{organization}/{project}/"
                        f"_apis/git/repositories/{repo_name}/refs?"
                        f"filter=heads/{self.branch_name}&api-version=6.0"
                    )
                    branch_response = await client.get(branch_url, headers=headers)

                    if branch_response.status_code != 200:
                        status_code = branch_response.status_code
                        result["message"] = (
                            f"Failed to verify branch. Status code: {status_code}"
                        )
                        return result

                    branches = branch_response.json().get("value", [])
                    if not branches:
                        result["message"] = (
                            f"Branch '{self.branch_name}' not found in repository"
                        )
                        return result

                    result["branch_valid"] = True
                else:
                    result["message"] = BRANCH_NAME_REQUIRED
                    return result

                # All validations passed
                result["is_valid"] = True
                result["message"] = ALL_CRED_ARE_VALID
                return result

        except Exception as e:  # pylint: disable=broad-exception-caught
            result["message"] = f"Error validating credentials: {str(e)}"
            return result
