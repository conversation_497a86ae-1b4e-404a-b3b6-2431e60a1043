import asyncio
import hashlib
import os
import shutil
import subprocess
import uuid
from abc import abstractmethod, ABC

from code_indexing.file_utils import is_ignored
from config.logging import logger
from knowledge_base.models import SourceItemKind
from utils.exceptions import CustomException


class DataExtractor(ABC):
    @abstractmethod
    async def extract(self, **kwargs) -> list[dict]:
        """Fetch data from the source and return a list of records."""
        pass

    @abstractmethod
    async def is_project_public(self) -> bool:
        """Checks whether the data source is publicly accessible.

        Returns:
            bool: True if public, False otherwise.
        """

        pass

    @abstractmethod
    async def validate_credentials(self) -> bool | dict:
        """Validates the credentials for the data source.

        Returns:
            Union[bool, dict]: True if valid or a dictionary with validation details.
        """
        pass


class GitRepoExtractor(DataExtractor):
    """Base class for Git-based repository extractors."""

    def __init__(self, repo_url, branch_name=None, pat=None):
        """Initializes base Git repository extractor with repo URL, branch, and optional token.

        Args:
            repo_url (str): The URL of the Git repository.
            branch_name (str, optional): The name of the branch to extract.
            pat (str, optional): Personal access token for authentication.
        """

        super().__init__()
        self.repo_url = repo_url
        self.branch_name = branch_name
        self.pat = pat
        self.temp_folder_path = os.path.expanduser("~/repo_temp")

        # Store initialization parameters but don't validate yet
        self._url_validated = False
        self.clone_url = None

    async def async_init(self):
        """Async initialization to be called after constructor"""
        if not self._url_validated:
            # Validate URL and set clone URL in child classes
            await self._validate_url()
            self.clone_url = await self._get_clone_url()
            self._url_validated = True
        return self

    @abstractmethod
    async def _validate_url(self):
        """Validate the repository URL format."""
        logger.info(f"Validating repository URL")

    @abstractmethod
    async def _get_clone_url(self) -> str:
        """Get the clone URL with authentication."""
        logger.info(f"Getting clone URL for repository")

    async def clone_repo(self, repo_path=None, repo_name=None) -> str:
        """Clones the Git repository to a temporary local path.

        Args:
            repo_path (str, optional): Specific path to clone into.
            repo_name (str, optional): Optional repo folder name.

        Returns:
            str: Path to the cloned repository.

        Raises:
            CustomException: If cloning fails.
        """

        try:
            if not repo_name:
                repo_name = str(uuid.uuid4())
            if not repo_path:
                repo_path = os.path.join(self.temp_folder_path, repo_name)

            process = await asyncio.create_subprocess_exec(
                "git",
                "clone",
                "--depth",
                "1",
                "-b",
                self.branch_name,
                self.clone_url,
                repo_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )
            stdout, stderr = await process.communicate()
            if process.returncode != 0:
                raise CustomException(f"Git clone failed with error: {stderr.decode()}")
        except Exception as e:  # pylint: disable=broad-exception-caught
            raise CustomException(f"Error cloning repository: {e}")
        return repo_path

    async def get_commit_sha(self, repo_path: str) -> str:
        """Gets the current commit SHA from the cloned Git repository.

        Args:
            repo_path (str): Path to the local repository.

        Returns:
            str: The latest commit SHA or an empty string if not found.
        """

        result = subprocess.run(
            ["git", "-C", repo_path, "rev-parse", "HEAD"],
            capture_output=True,
            text=True,
        )
        if result.returncode == 0:
            return result.stdout.strip()
        return ""

    async def get_git_blob_sha(self, repo_path: str, file_path: str) -> str:
        """Gets the Git blob SHA for a specific file in the repository.

        Args:
            repo_path (str): Path to the cloned repository.
            file_path (str): Full path to the file.

        Returns:
            str: Blob SHA or empty string if lookup fails.
        """

        """Returns blob SHA for a file in a Git repo"""
        rel_path = os.path.relpath(file_path, repo_path)
        result = subprocess.run(
            ["git", "-C", repo_path, "ls-tree", "HEAD", rel_path],
            capture_output=True,
            text=True,
        )
        if result.returncode == 0 and result.stdout:
            return result.stdout.strip().split()[2]  # blob SHA
        return ""

    async def extract(self, **kwargs) -> list[dict]:
        """Clones the repo and extracts files, returning list of file content records with metadata."""
        logger.info(f"Extracting data from Git repo: {self.repo_url}")
        cloned_repo_path = None
        file_content_dict = []
        try:
            cloned_repo_path = await self.clone_repo()
            commit_sha = await self.get_commit_sha(cloned_repo_path)

            for root, _, repo_files in os.walk(cloned_repo_path):
                for repo_file in repo_files:
                    file_path = os.path.join(root, repo_file)
                    if is_ignored(file_path):
                        continue
                    relative_path = os.path.relpath(file_path, cloned_repo_path)
                    with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                        content = f.read()
                    if content.strip():
                        blob_sha = await self.get_git_blob_sha(
                            cloned_repo_path, file_path
                        )
                        file_content_dict.append(
                            {
                                "path": relative_path,
                                "content": content,
                                "version_tag": commit_sha,
                                "provider_item_id": blob_sha,
                                "checksum": hashlib.sha256(
                                    content.encode()
                                ).hexdigest(),
                                "uuid": str(uuid.uuid4()),
                                "kind": SourceItemKind.file.value,
                            }
                        )
            logger.info(f"Extracted {len(file_content_dict)} records from Git repo.")
            return file_content_dict
        except Exception as e:  # pylint: disable=broad-exception-caught
            raise CustomException(f"Error extracting repo contents: {e}")
        finally:
            if cloned_repo_path:
                shutil.rmtree(cloned_repo_path)
