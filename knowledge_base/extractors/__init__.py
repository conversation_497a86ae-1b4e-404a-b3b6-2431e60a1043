"""
Data extractors for knowledge base documents.

This module provides extractor classes for fetching and processing data from 
various external sources like version control systems and document platforms.
"""

from .base import DataExtractor, GitRepoExtractor
from .ado_ext import AzureDevopsRepoExtractor
from .github_ext import GitHubRepoExtractor
from .gitlab_ext import GitLabRepoExtractor
from .google_docs import GoogleDocsExtractor
from .quip import QuipExtractor

__all__ = [
    "DataExtractor",
    "GitRepoExtractor", 
    "AzureDevopsRepoExtractor",
    "GitHubRepoExtractor",
    "GitLabRepoExtractor",
    "GoogleDocsExtractor",
    "QuipExtractor",
]
