import hashlib
import json
import uuid

import httpx
from urllib.parse import urlparse

from config.logging import logger
from knowledge_base.extractors.base import DataExtractor
from knowledge_base.models import SourceItemKind


class GoogleDocsExtractor(DataExtractor):

    def __init__(self, docs: list, token: str):
        """Initializes the GoogleDocsExtractor with a list of documents and an access token.

        Args:
            docs (list): List of document metadata dictionaries with keys like 'id' and 'url'.
            token (str): OAuth2 bearer token to authorize access to Google Docs API.
        """

        super().__init__()
        self.docs = docs
        self.token = token

    async def extract(self, **kwargs) -> list[dict]:
        """Extracts metadata and content from the list of Google Docs provided.

        Returns:
            list[dict]: A list of structured document records, each containing:
                - path
                - content (title + body)
                - version_tag (revisionId)
                - provider_item_id
                - checksum
                - uuid
                - kind
                - source_url

        Notes:
            Documents that fail to extract are skipped with error logging.
        """

        records = []
        for doc in self.docs:
            try:
                parsed_url = urlparse(doc.get("url"))
                response = await self.get_document_content(doc.get("id"))
                response_json = response.json()
                content = {
                    "title": response_json.get("title"),
                    "body": response_json.get("body", {}).get("content", []),
                }
                content_dump = json.dumps(content)

                record = {
                    "path": parsed_url.path,
                    "content": content,
                    "version_tag": response_json.get("revisionId"),
                    "provider_item_id": doc.get("id"),
                    "checksum": hashlib.sha256(content_dump.encode()).hexdigest(),
                    "uuid": str(uuid.uuid4()),
                    "kind": SourceItemKind.document.value,
                    "source_url": doc.get("url"),  # Include the original URL
                }
                records.append(record)
            except Exception as e:  # pylint: disable=broad-exception-caught
                logger.info(
                    f"Error while extracting google doc {doc.get('id')} - {doc.get('url')} : {e}"
                )
        return records

    async def get_document_content(self, document_id: str):
        """Fetches the raw content of a Google Doc by its document ID.

        Args:
            document_id (str): The unique ID of the Google Document.

        Returns:
            httpx.Response: The HTTP response object containing the document content.
        """

        async with httpx.AsyncClient() as client:
            url = f"https://docs.googleapis.com/v1/documents/{document_id}"
            headers = {
                "Accept": "application/json",
                "Authorization": f"Bearer {self.token}",
            }
            return await client.get(url, headers=headers)

    async def is_project_public(self) -> bool:
        """Checks whether the document is publicly accessible.

        Note:
            This is currently not implemented and always returns None or raises NotImplementedError.
        """

        # not implemented
        pass

    async def validate_credentials(self) -> dict:
        """Validates access to the first selected Google Doc using the bearer token.

        Returns:
            dict: Validation result with:
                - is_valid (bool): Whether the token is valid for the given document.
                - message (str): Description of the validation outcome.
        """

        result = {"is_valid": False, "message": ""}
        try:
            if self.docs:
                response = await self.get_document_content(self.docs[0]["id"])
                if response.status_code == 200:
                    result.update({"is_valid": True})
                else:
                    result.update(
                        {"message": "You don't have access to the documents."}
                    )
            else:
                result.update({"message": "You haven't selected any document."})
        except Exception as e:  # pylint: disable=broad-exception-caught
            logger.info(
                f"Exception caught to get google document in validate_credentials() : {e}"
            )
        return result
