import re

import httpx

from config.logging import logger
from knowledge_base.constants import (
    INVALID_REPO_URL_FORMAT,
    REPO_URL_REQUIRED,
    BRANCH_NAME_REQUIRED,
    ALL_CRED_ARE_VALID,
)
from knowledge_base.extractors.base import GitRepoExtractor


class GitLabRepoExtractor(GitRepoExtractor):
    async def _validate_url(self):
        """Validates the format of the GitLab repository URL.

        Raises:
            ValueError: If the URL format is invalid.
        """

        if not re.match(r"^https://gitlab\.com/[^/]+/[^/]+$", self.repo_url):
            raise ValueError(f"Invalid GitLab repository URL")

    async def _get_clone_url(self) -> str:
        """Constructs the clone URL for the GitLab repository using OAuth token.

        Returns:
            str: The authenticated HTTPS clone URL.
        """

        return f"https://oauth2:{self.pat}@gitlab.com/{self.repo_url.split('gitlab.com/')[1]}"

    async def is_project_public(self) -> bool:
        """Check if GitLab repository is public."""
        try:
            # Extract owner and repo name from URL
            path_parts = self.repo_url.split("gitlab.com/")[1].split("/")
            project_path = "/".join(path_parts)

            url = (
                f"https://gitlab.com/api/v4/projects/{project_path.replace('/', '%2F')}"
            )

            async with httpx.AsyncClient() as client:
                response = await client.get(url)
                if response.status_code == 200:
                    project_data = response.json()
                    return project_data.get("visibility", "") == "public"
                elif response.status_code == 404:
                    logger.warning(f"Project not found: {project_path}")
                    return False
                else:
                    logger.warning(
                        f"Failed to fetch project visibility: {response.status_code} - {response.text}"
                    )
                    return False
        except Exception as e:  # pylint: disable=broad-exception-caught
            logger.error(f"Error checking project visibility: {e}")
            return False

    async def validate_credentials(self) -> dict:
        """
        Validate URL, access token, and branch existence.
        Returns a dictionary with validation status and messages.
        """
        result = {
            "is_valid": False,
            "url_valid": False,
            "token_valid": False,
            "branch_valid": False,
            "message": "",
        }

        try:
            # Validate URL format and parse components
            if not self.repo_url:
                result["message"] = REPO_URL_REQUIRED
                return result

            try:
                path_parts = self.repo_url.split("gitlab.com/")[1].split("/")
                project_path = "/".join(path_parts)
                result["url_valid"] = True
            except (IndexError, AttributeError):
                result["message"] = INVALID_REPO_URL_FORMAT
                return result

            # Prepare headers for API calls
            headers = {}
            if self.pat:
                headers["Authorization"] = f"Bearer {self.pat}"

            # Check repository existence and token validity
            async with httpx.AsyncClient() as client:
                repo_url = f"https://gitlab.com/api/v4/projects/{project_path.replace('/', '%2F')}"
                response = await client.get(repo_url, headers=headers)

                if response.status_code == 401:
                    result["message"] = (
                        "Invalid access token or insufficient permissions"
                    )
                    return result
                elif response.status_code == 404:
                    result["message"] = f"Repository '{project_path}' not found"
                    return result
                elif response.status_code != 200:
                    result["message"] = (
                        f"Failed to access repository: {response.status_code}"
                    )
                    return result

                result["token_valid"] = True

                # Check branch existence
                if self.branch_name:
                    project_encoded = project_path.replace("/", "%2F")
                    branch_url = (
                        f"https://gitlab.com/api/v4/projects/{project_encoded}/"
                        f"repository/branches/{self.branch_name}"
                    )
                    branch_response = await client.get(branch_url, headers=headers)

                    if branch_response.status_code != 200:
                        result["message"] = (
                            f"Branch '{self.branch_name}' not found in repository"
                        )
                        return result

                    result["branch_valid"] = True
                else:
                    result["message"] = BRANCH_NAME_REQUIRED
                    return result

                # All validations passed
                result["is_valid"] = True
                result["message"] = ALL_CRED_ARE_VALID
                return result

        except Exception as e:  # pylint: disable=broad-exception-caught
            result["message"] = f"Error validating credentials: {str(e)}"
            return result
