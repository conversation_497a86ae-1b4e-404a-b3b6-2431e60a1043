import re

import httpx

from config.logging import logger
from knowledge_base.constants import (
    INVALID_REPO_URL_FORMAT,
    REPO_URL_REQUIRED,
    BRANCH_NAME_REQUIRED,
    ALL_CRED_ARE_VALID,
)
from knowledge_base.extractors.base import GitRepoExtractor


class GitHubRepoExtractor(GitRepoExtractor):
    async def _validate_url(self):
        """Validates the format of the GitHub repository URL.

        Raises:
            ValueError: If the URL format is invalid.
        """

        if not re.match(r"^https://github\.com/[^/]+/[^/]+$", self.repo_url):
            raise ValueError(f"Invalid GitHub repository URL")

    async def _get_clone_url(self) -> str:
        """Constructs the clone URL for the GitHub repository.

        Returns:
            str: The HTTPS clone URL, with personal access token if provided.
        """

        # For public repos, we can use the regular HTTPS URL if no token is provided
        if not self.pat:
            return self.repo_url
        return f"https://{self.pat}@github.com/{self.repo_url.split('github.com/')[1]}"

    async def is_project_public(self) -> bool:
        """Check if GitHub repository is public."""
        try:
            # Extract owner and repo name from URL
            path_parts = self.repo_url.split("github.com/")[1].split("/")
            owner, repo = path_parts[0], path_parts[1]

            url = f"https://api.github.com/repos/{owner}/{repo}"

            headers = {}
            if self.pat:
                headers["Authorization"] = f"token {self.pat}"

            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers)
                if response.status_code == 200:
                    repo_data = response.json()
                    return not repo_data.get("private", True)
                elif response.status_code == 404:
                    logger.warning(f"Repository not found: {owner}/{repo}")
                    return False
                else:
                    logger.warning(
                        f"Failed to fetch repository visibility: {response.status_code} - {response.text}"
                    )
                    return False
        except Exception as e:  # pylint: disable=broad-exception-caught
            logger.error(f"Error checking repository visibility: {e}")
            return False

    async def validate_credentials(self) -> dict:
        """Validate URL, access token, and branch existence.

        Returns a dictionary with validation status and messages.
        """
        result = {
            "is_valid": False,
            "url_valid": False,
            "token_valid": False,
            "branch_valid": False,
            "message": "",
        }

        try:
            # Validate URL format and parse components
            if not self.repo_url:
                result["message"] = REPO_URL_REQUIRED
                return result

            try:
                path_parts = self.repo_url.split("github.com/")[1].split("/")
                owner, repo = path_parts[0], path_parts[1]
                result["url_valid"] = True
            except (IndexError, AttributeError):
                result["message"] = INVALID_REPO_URL_FORMAT
                return result

            # Prepare headers for API calls
            headers = {}
            if self.pat:
                headers["Authorization"] = f"token {self.pat}"

            # Check repository existence and if it's public
            async with httpx.AsyncClient() as client:
                repo_url = f"https://api.github.com/repos/{owner}/{repo}"
                response = await client.get(repo_url, headers=headers)

                # Debug information
                print(f"API Response: {response.status_code}")
                if response.status_code != 200:
                    print(f"Error response: {response.text}")

                if response.status_code == 404:
                    result["message"] = f"Repository '{owner}/{repo}' not found"
                    return result
                elif response.status_code == 401:
                    result["message"] = "Invalid access token"
                    return result
                elif response.status_code != 200:
                    error_text = f"{response.status_code} - {response.text}"
                    result["message"] = f"Failed to access repository: {error_text}"
                    return result

                # Check if repo is public
                repo_data = response.json()
                is_public = not repo_data.get("private", True)

                # For private repos, we need a valid token
                if not is_public:
                    if not self.pat:
                        result["message"] = (
                            "Private repository requires an access token"
                        )
                        return result
                    result["token_valid"] = True
                else:
                    # Public repo doesn't need token validation
                    result["token_valid"] = True

                # Check branch existence
                if self.branch_name:
                    branch_url = (
                        f"https://api.github.com/repos/{owner}/{repo}/"
                        f"branches/{self.branch_name}"
                    )
                    branch_response = await client.get(branch_url, headers=headers)

                    if branch_response.status_code != 200:
                        result["message"] = (
                            f"Branch '{self.branch_name}' not found in repository"
                        )
                        return result

                    result["branch_valid"] = True
                else:
                    result["message"] = BRANCH_NAME_REQUIRED
                    return result

                # All validations passed
                result["is_valid"] = True
                result["message"] = ALL_CRED_ARE_VALID
                return result

        except Exception as e:  # pylint: disable=broad-exception-caught
            result["message"] = f"Error validating credentials: {str(e)}"
            return result
