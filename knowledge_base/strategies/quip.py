from typing import Optional

from clerk_integration.utils import UserData

from code_indexing.serializers import ResponseData

from knowledge_base.factory import KnowledgeBaseFactory
from knowledge_base.models import KBState, Integration
from knowledge_base.serializers import Provider<PERSON>ontext
from knowledge_base.strategies.base import ProviderStrategy
from utils.connection_handler import ConnectionHandler


class QuipStrategy(ProviderStrategy):
    """Strategy for handling Quip documents"""

    async def start_loading(
        self,
        provider_context: ProviderContext,
        team_id: Optional[str],
        connection_handler: ConnectionHandler,
        user_data: UserData,
        integration: Integration,
    ) -> ResponseData:
        """Handles the ingestion process for Quip documents.

        Validates access token, checks for existing KB or creates a new one, sets ingestion state,
        and emits a Kafka event to begin background ingestion.

        Args:
            provider_context (ProviderContext): Context with credentials, KB metadata, and provider name.
            team_id (Optional[str]): Optional team ID for sharing context.
            connection_handler (ConnectionHandler): DB session handler.
            user_data (UserData): Authenticated user initiating the request.
            integration (Integration): Integration metadata from the database.

        Returns:
            ResponseData: Result of the initialization process, including status and validation result.
        """

        try:
            # Extract credentials
            pat_token = (
                provider_context.credentials.get("pat")
                if provider_context.credentials
                else None
            )
            urls = (
                provider_context.credentials.get("urls", [])
                if provider_context.credentials
                else []
            )

            # Handle existing vs new KB
            if provider_context.kb_id:
                kb = await self.knowledge_base_dao.get_by_id(
                    int(provider_context.kb_id)
                )
                if not kb:
                    raise ValueError(
                        f"Knowledge base with id {provider_context.kb_id} does not exist."
                    )

                # Use KB credentials if not provided in context
                pat_token = pat_token or kb.settings_json["credentials"].get("pat")
                urls = urls or kb.settings_json["credentials"]["urls"]
            else:
                if not pat_token:
                    raise RuntimeError("No PAT available")

                source_identifier = "|".join(sorted(urls))
                existing_response = await self.check_existing_kb(
                    source_identifier,
                    user_data,
                    "You already have a knowledge base for these Quip documents",
                )
                if existing_response:
                    return existing_response

                kb = await self.create_knowledge_base(
                    integration,
                    provider_context,
                    source_identifier,
                    "quip",
                    team_id,
                    user_data,
                )

            # Validate credentials
            extractor = await KnowledgeBaseFactory.get_extractor(
                provider_context.provider.value, pat=pat_token, urls=urls
            )

            validation_result = await extractor.validate_credentials()
            if not validation_result["is_valid"]:
                if provider_context.kb_id:
                    await self.knowledge_base_dao.update_state_by_id(
                        kb.id, KBState.token_error
                    )
                return ResponseData.model_construct(
                    success=False, message=validation_result["message"]
                )

            # Set updating state and create ingestion run
            await self.knowledge_base_dao.update_state_by_id(kb.id, KBState.updating)
            ingestion_run = await self.create_ingestion_run(kb.id, "Quip")

            # Emit Kafka event
            event = {
                "payload": {
                    "provider": str(provider_context.provider.value),
                    "knowledge_base_id": kb.id,
                    "pat": pat_token,
                    "urls": urls,
                    "ingestion_run_id": ingestion_run.id,
                    "mode": "UPDATE" if provider_context.kb_id else "ADD",
                }
            }
            await self.emit_kafka_event(event, connection_handler)

            # Update KB metadata
            url_string = "|".join(sorted(urls))
            await self.knowledge_base_dao.update_kb_name_and_pat_url_branch(
                kb_id=kb.id,
                name=provider_context.kb_name or kb.name,
                url=url_string,
                commit=True,
                state=KBState.updating,
            )

            await self.knowledge_base_dao.update_kb_settings(
                kb_id=kb.id,
                kb_name=provider_context.kb_name or kb.name,
                kb_descp=provider_context.description,
                new_settings={"credentials": {"pat": pat_token, "urls": urls}},
                commit=True,
            )

            await connection_handler.session.commit()
            return self.build_response_data(kb, validation_result)

        except Exception as e:  # pylint: disable=broad-exception-caught
            return await self.handle_transaction_error(connection_handler, "Quip", e)
