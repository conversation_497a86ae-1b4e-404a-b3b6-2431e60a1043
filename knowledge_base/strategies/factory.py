from knowledge_base.dao import KnowledgeBaseDao, IngestionRunDao
from knowledge_base.models import IntegrationType
from knowledge_base.strategies import (
    ProviderStrategy,
    BigQueryStrategy,
    GoogleDocsStrategy,
    QuipStrategy,
    VersionControlStrategy,
)


class ProviderStrategyFactory:
    """Factory for creating provider strategies"""

    _STRATEGY_MAPPING = {
        "azure_devops": VersionControlStrategy,
        "github": VersionControlStrategy,
        "gitlab": VersionControlStrategy,
        IntegrationType.google_docs.value: GoogleDocsStrategy,
        "quip": QuipStrategy,
        IntegrationType.bigquery.value: BigQueryStrategy,
    }

    @staticmethod
    def get_strategy(
        provider_type: str,
        knowledge_base_dao: KnowledgeBaseDao,
        ingestion_run_dao: IngestionRunDao,
    ) -> ProviderStrategy:
        """Get the appropriate strategy for the provider type"""
        strategy_class = ProviderStrategyFactory._STRATEGY_MAPPING.get(provider_type)
        if not strategy_class:
            raise ValueError(f"Unsupported provider type: {provider_type}")

        return strategy_class(knowledge_base_dao, ingestion_run_dao)
