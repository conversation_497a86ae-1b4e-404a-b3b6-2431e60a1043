import copy
from abc import ABC, abstractmethod
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List

from clerk_integration.utils import UserData

from code_indexing.serializers import ResponseData
from config.logging import logger
from knowledge_base.dao import KnowledgeBaseDao, IngestionRunDao
from knowledge_base.locksmith_calls import _call_grant_data_source_access
from knowledge_base.models import KBState, Integration
from knowledge_base.serializers import ProviderContext
from utils.connection_handler import ConnectionHandler
from utils.kafka.constants import (
    KAFKA_SERVICE_CONFIG_MAPPING,
    KafkaServices,
    ETL_EXTERNAL_DATA,
)
from utils.kafka.kafka_utils import almanac_partitioner


class ProviderStrategy(ABC):
    """Base strategy interface for handling different provider types"""

    def __init__(
        self, knowledge_base_dao: KnowledgeBaseDao, ingestion_run_dao: IngestionRunDao
    ):
        """Initializes the provider strategy with required DAOs.

        Args:
            knowledge_base_dao (KnowledgeBaseDao): DAO for managing knowledge base records.
            ingestion_run_dao (IngestionRunDao): DAO for tracking ingestion runs.
        """

        self.knowledge_base_dao = knowledge_base_dao
        self.ingestion_run_dao = ingestion_run_dao

    @abstractmethod
    async def start_loading(
        self,
        provider_context: ProviderContext,
        team_id: Optional[str],
        connection_handler: ConnectionHandler,
        user_data: UserData,
        integration: Integration,
    ) -> ResponseData:
        """Start loading data from the provider"""
        pass

    async def check_existing_kb(
        self, source_identifier: str, user_data: UserData, error_message: str
    ) -> Optional[ResponseData]:
        """Check if knowledge base already exists for the user and source"""
        existing_kb = await self.knowledge_base_dao.get_by_source_and_user(
            source_identifier=source_identifier,
            user_id=user_data.userId if user_data else None,
            org_id=user_data.orgId if user_data else None,
        )

        if existing_kb:
            logger.info(
                f"User already has a knowledge base for this source: {existing_kb.id}"
            )
            return ResponseData.model_construct(
                success=False,
                message=error_message,
                data={"existing_kb_id": existing_kb.id},
            )
        return None

    async def create_knowledge_base(
        self,
        integration: Integration,
        provider_context: ProviderContext,
        source_identifier: str,
        kb_type: str,
        team_id: Optional[str],
        user_data: UserData,
    ):
        """Create a new knowledge base with common settings"""
        settings_json = {
            "provider": provider_context.provider.value,
            "credentials": copy.deepcopy(provider_context.credentials),
            "user_id": user_data.userId if user_data else None,
            "org_id": user_data.orgId if user_data else None,
        }

        kb = await self.knowledge_base_dao.create_knowledge_base(
            integration_id=integration.id,
            name=provider_context.kb_name,
            description=provider_context.description,
            source_identifier=source_identifier,
            kb_type=kb_type,
            state=KBState.indexing,
            team_id=team_id if team_id else None,
            last_indexed_at=datetime.now(timezone.utc),
            created_by=user_data.userId if user_data else None,
            settings_json=settings_json,
            org_id=user_data.orgId if user_data else None,
            is_updatable=(
                provider_context.is_updatable
                if provider_context.is_updatable
                else False
            ),
        )

        await self._grant_access(kb.id, team_id, user_data)
        return kb

    @staticmethod
    async def _grant_access(kb_id: int, team_id: Optional[str], user_data: UserData):
        """Grant access to knowledge base for team and user"""
        if team_id:
            await _call_grant_data_source_access(
                team_id, knowledge_base_id=kb_id, user_data=None
            )
        await _call_grant_data_source_access(
            knowledge_base_id=kb_id,
            user_id=user_data.userId,
            user_data=None,
            org_id=None,
            team_id=None,
        )

    async def create_ingestion_run(self, kb_id: int, provider_name: str):
        """Create an ingestion run for the knowledge base"""
        ingestion_run = await self.ingestion_run_dao.create_ingestion_run(
            kb_id=kb_id, status="running"
        )
        logger.info(
            f"[{provider_name}] IngestionRun created with ID: {ingestion_run.id}"
        )
        return ingestion_run

    @staticmethod
    async def emit_kafka_event(
        event: Dict[str, Any], connection_handler: ConnectionHandler
    ):
        """Emit event to Kafka"""
        logger.info(f"Event to be emitted: {event}")
        await connection_handler.event_emitter.emit(
            topics=KAFKA_SERVICE_CONFIG_MAPPING[KafkaServices.almanac][
                ETL_EXTERNAL_DATA
            ]["topics"],
            partition_value=str(almanac_partitioner.partition()),
            event=event,
        )

    @staticmethod
    def build_response_data(
        kb, validation_result: Dict[str, Any], **extra_data
    ) -> ResponseData:
        """Build successful response data"""
        data = {
            "kb": {
                "id": kb.id,
                "name": kb.name,
                "source_identifier": kb.source_identifier,
                "kb_type": kb.kb_type,
                "state": kb.state.value if kb.state else None,
            },
            "is_valid_cred": validation_result,
            **extra_data,
        }
        return ResponseData.model_construct(success=True, data=data)

    @staticmethod
    async def handle_transaction_error(
        connection_handler: ConnectionHandler,
        provider_name: str,
        error: Exception,
    ) -> ResponseData:
        """Handle transaction errors"""
        await connection_handler.session.rollback()
        logger.error(f"Failed to start loading {provider_name}: {str(error)}")
        return ResponseData.model_construct(
            success=False, message=f"Transaction failed: {str(error)}"
        )
