from typing import Optional

from clerk_integration.utils import UserData

from code_indexing.serializers import ResponseData
from knowledge_base.factory import KnowledgeBaseFactory
from knowledge_base.models import KBState, Integration, IntegrationType
from knowledge_base.serializers import Provider<PERSON>ontext
from knowledge_base.strategies.base import ProviderStrategy
from utils.connection_handler import ConnectionHandler


class GoogleDocsStrategy(ProviderStrategy):
    """Strategy for handling Google Docs"""

    async def start_loading(
        self,
        provider_context: ProviderContext,
        team_id: Optional[str],
        connection_handler: ConnectionHandler,
        user_data: UserData,
        integration: Integration,
    ) -> ResponseData:
        """Handles the Google Docs ingestion process.

        Validates credentials, checks for existing knowledge base (if needed), creates or updates the KB,
        generates an ingestion run, and emits a Kafka event to start processing.

        Args:
            provider_context (ProviderContext): Incoming request data including credentials and KB metadata.
            team_id (Optional[str]): Optional team ID for access control.
            connection_handler (ConnectionHandler): Manages DB session and commit lifecycle.
            user_data (UserData): Authenticated user metadata.
            integration (Integration): The integration definition from the DB.

        Returns:
            ResponseData: Contains success status, KB info, and validation result.
        """

        try:
            # Extract credentials
            docs = provider_context.credentials.get("docs", [])
            token = provider_context.credentials.get("token", "")

            # Validate credentials
            extractor = await KnowledgeBaseFactory.get_extractor(
                name=IntegrationType.google_docs.value, docs=docs, token=token
            )

            validation_result = await extractor.validate_credentials()
            if not validation_result["is_valid"]:
                return ResponseData.model_construct(
                    success=False, message=validation_result["message"]
                )

            # Generate unique source identifier
            doc_ids = [doc["id"] for doc in docs]
            source_identifier = "|".join(sorted(doc_ids))

            # Handle existing vs new KB
            if provider_context.kb_id:
                kb = await self.knowledge_base_dao.get_by_id(
                    int(provider_context.kb_id)
                )
                if not kb:
                    raise ValueError(
                        f"Knowledge base with id {provider_context.kb_id} does not exist."
                    )

                await self.knowledge_base_dao.update_kb_credentials(
                    kb_id=kb.id,
                    new_credentials=provider_context.credentials,
                    user_data=(
                        {"userId": user_data.userId, "orgId": user_data.orgId}
                        if user_data
                        else None
                    ),
                    new_state=KBState.indexing,
                    commit=False,
                )

                await self.knowledge_base_dao.update_kb_name_descp(
                    kb_id=kb.id,
                    name=provider_context.kb_name,
                    description=provider_context.description,
                    commit=True,
                )
            else:
                existing_response = await self.check_existing_kb(
                    source_identifier,
                    user_data,
                    "You already have a knowledge base for this document(s)",
                )
                if existing_response:
                    return existing_response

                kb = await self.create_knowledge_base(
                    integration,
                    provider_context,
                    source_identifier,
                    "google_docs",
                    team_id,
                    user_data,
                )

            # Create ingestion run and emit event
            ingestion_run = await self.create_ingestion_run(kb.id, "Google Docs")

            event = {
                "payload": {
                    "provider": str(provider_context.provider.value),
                    "knowledge_base_id": kb.id,
                    "token": token,
                    "docs": docs,
                    "ingestion_run_id": ingestion_run.id,
                    "mode": "UPDATE" if provider_context.kb_id else "ADD",
                }
            }

            await self.emit_kafka_event(event, connection_handler)
            await connection_handler.session.commit()

            return self.build_response_data(kb, validation_result)

        except Exception as e:  # pylint: disable=broad-exception-caught
            return await self.handle_transaction_error(
                connection_handler, "Google Docs", e
            )
