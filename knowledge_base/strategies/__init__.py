"""
Strategy classes for handling different provider types in knowledge base ingestion.

This module provides concrete implementations of provider strategies for various
external data sources including repositories, documents, and databases.
"""

from .base import ProviderStrategy
from .factory import ProviderStrategyFactory
from .google_bigquery import BigQueryStrategy
from .quip import QuipStrategy
from .google_docs import GoogleDocsStrategy
from .version_control import VersionControlStrategy

__all__ = [
    "ProviderStrategy",
    "ProviderStrategyFactory",
    "BigQueryStrategy",
    "QuipStrategy",
    "GoogleDocsStrategy",
    "VersionControlStrategy",
]
