from typing import Optional, Dict, Any

from clerk_integration.utils import UserData

from code_indexing.serializers import ResponseData
from knowledge_base.factory import KnowledgeBaseFactory
from knowledge_base.models import KBState, Integration
from knowledge_base.serializers import Provider<PERSON>ontext
from knowledge_base.strategies.base import ProviderStrategy
from utils.connection_handler import ConnectionHandler


class VersionControlStrategy(ProviderStrategy):
    """Generic strategy for version control providers (Azure DevOps, GitHub, GitLab)"""

    async def start_loading(
        self,
        provider_context: ProviderContext,
        team_id: Optional[str],
        connection_handler: ConnectionHandler,
        user_data: UserData,
        integration: Integration,
    ) -> ResponseData:
        """Starts the ingestion flow for a version control repository.

        Args:
            provider_context (ProviderContext): Context containing repo metadata and credentials.
            team_id (Optional[str]): Optional team ID for sharing access.
            connection_handler (ConnectionHandler): Handles DB session and Kafka emitter.
            user_data (UserData): The user initiating the ingestion.
            integration (Integration): Integration configuration for the provider.

        Returns:
            ResponseData: Result containing status and metadata about the triggered ingestion.
        """

        try:
            pat_token = (
                provider_context.credentials.get("pat")
                if provider_context.credentials
                else None
            )

            if not provider_context.kb_id:
                # New knowledge base creation
                kb, validation_result = await self._create_new_kb(
                    provider_context, pat_token, integration, team_id, user_data
                )
                if not kb:
                    return ResponseData.model_construct(
                        success=False, message=validation_result["message"]
                    )
            else:
                # Existing knowledge base update
                kb, pat_token, validation_result = await self._update_existing_kb(
                    provider_context, pat_token
                )
                if not kb:
                    return ResponseData.model_construct(
                        success=False, message=validation_result["message"]
                    )

            # Create ingestion run and emit event
            ingestion_run = await self.create_ingestion_run(
                kb.id, provider_context.provider.value
            )

            await self._emit_repository_event(
                provider_context, kb, pat_token, ingestion_run.id, connection_handler
            )

            await connection_handler.session.commit()
            return self.build_response_data(kb, validation_result)

        except Exception as e:  # pylint: disable=broad-exception-caught
            return await self.handle_transaction_error(
                connection_handler, provider_context.provider.value, e
            )

    async def _create_new_kb(
        self,
        provider_context: ProviderContext,
        pat_token: Optional[str],
        integration: Integration,
        team_id: Optional[str],
        user_data: UserData,
    ) -> tuple[Optional[Any], Dict[str, Any]]:
        """Create a new knowledge base for the repository"""

        extractor = await KnowledgeBaseFactory.get_extractor(
            provider_context.provider.value,
            repo_url=provider_context.credentials["url"],
            branch_name=provider_context.credentials["branch_name"],
            pat=pat_token,
        )

        is_public, validation_result = await self._validate_repository_access(extractor)

        if not is_public and not validation_result["is_valid"]:
            return None, validation_result

        # Check for existing KB
        existing_response = await self.check_existing_kb(
            provider_context.credentials["url"],
            user_data,
            "You already have a knowledge base for this repository",
        )
        if existing_response:
            return None, {"message": "Knowledge base already exists"}

        kb = await self.create_knowledge_base(
            integration,
            provider_context,
            provider_context.credentials["url"],
            "repo",
            team_id,
            user_data,
        )

        return kb, validation_result

    async def _update_existing_kb(
        self, provider_context: ProviderContext, pat_token: Optional[str]
    ) -> tuple[Optional[Any], Optional[str], Dict[str, Any]]:
        """Update an existing knowledge base"""

        kb = await self.knowledge_base_dao.get_by_id(int(provider_context.kb_id))
        if not kb:
            return (
                None,
                None,
                {
                    "message": f"Knowledge base with id {provider_context.kb_id} does not exist."
                },
            )

        # Get credentials from context or existing KB
        credentials = provider_context.credentials or {}
        if credentials and not pat_token:
            pat_token = kb.settings_json["credentials"].get("pat")

        repo_url = credentials.get("url") or kb.settings_json["credentials"].get("url")
        branch_name = credentials.get("branch_name") or kb.settings_json[
            "credentials"
        ].get("branch_name")

        # Validate repository access
        extractor = await KnowledgeBaseFactory.get_extractor(
            provider_context.provider.value,
            repo_url=repo_url,
            branch_name=branch_name,
            pat=pat_token,
        )

        is_public, validation_result = await self._validate_repository_access(extractor)

        if not is_public and not validation_result["is_valid"]:
            if not validation_result.get("token_valid", True):
                await self.knowledge_base_dao.update_state_by_id(
                    kb.id, KBState.token_error
                )
            return None, None, validation_result

        if pat_token is None and not is_public:
            return None, None, validation_result

        # Update KB state and details
        await self.knowledge_base_dao.update_state_by_id(kb.id, KBState.updating)
        await self._update_kb_metadata(kb, provider_context, pat_token)

        return kb, pat_token, validation_result

    async def _validate_repository_access(
        self, extractor
    ) -> tuple[bool, Dict[str, Any]]:
        """Validate credentials and check if repository is public"""
        is_public = await extractor.is_project_public()
        validation_result = {}

        if not is_public:
            validation_result = await extractor.validate_credentials()
            if not validation_result["is_valid"]:
                return is_public, validation_result

        return is_public, validation_result

    async def _update_kb_metadata(
        self, kb, provider_context: ProviderContext, pat_token: str
    ):
        """Update knowledge base metadata"""
        credentials = (
            provider_context.credentials
            if provider_context.credentials
            else kb.settings_json.get("credentials", {})
        )

        await self.knowledge_base_dao.update_kb_name_and_pat_url_branch(
            name=provider_context.kb_name or kb.name,
            kb_id=kb.id,
            pat=pat_token,
            url=credentials.get("url"),
            branch_name=credentials.get("branch_name"),
            state=KBState.updating,
        )

        await self.knowledge_base_dao.update_kb_name_descp(
            kb_id=kb.id,
            name=provider_context.kb_name,
            description=provider_context.description,
            commit=True,
        )

    async def _emit_repository_event(
        self,
        provider_context: ProviderContext,
        kb,
        pat_token: str,
        ingestion_run_id: int,
        connection_handler: ConnectionHandler,
    ):
        """Emit Kafka event for repository ingestion"""
        credentials = (
            provider_context.credentials
            if provider_context.credentials
            else kb.settings_json.get("credentials", {})
        )

        event = {
            "payload": {
                "provider": str(provider_context.provider.value),
                "knowledge_base_id": kb.id,
                "pat": pat_token,
                "url": credentials.get("url"),
                "branch_name": credentials.get("branch_name"),
                "ingestion_run_id": ingestion_run_id,
                "mode": "UPDATE" if provider_context.kb_id else "ADD",
            }
        }

        await self.emit_kafka_event(event, connection_handler)
