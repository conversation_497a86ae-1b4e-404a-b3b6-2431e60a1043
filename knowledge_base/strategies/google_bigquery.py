from typing import Optional, Dict, Any, List

from clerk_integration.utils import UserData

from bigquery_integration.bigquery_client import BigQueryClientStrategy
from code_indexing.serializers import ResponseData
from knowledge_base.models import Integration
from knowledge_base.serializers import Provider<PERSON>ontext
from knowledge_base.strategies.base import ProviderStrategy
from knowledge_base.utils import transform_tables_data
from utils.base_database_client import DatabaseContext
from utils.connection_handler import ConnectionHandler
from utils.vector_db.elastic_sql_adapter import ElasticsearchSQLAdapter


class BigQueryStrategy(ProviderStrategy):
    """Strategy for handling BigQuery"""

    async def start_loading(
        self,
        provider_context: ProviderContext,
        team_id: Optional[str],
        connection_handler: ConnectionHandler,
        user_data: UserData,
        integration: Integration,
    ) -> ResponseData:
        """Handles the loading process for BigQuery integrations.

        Validates credentials, retrieves table schemas, and optionally merges with existing document metadata.

        Args:
            provider_context (ProviderContext): Request context including credentials, kb_id, and db name.
            team_id (Optional[str]): Team identifier, if applicable.
            connection_handler (ConnectionHandler): Handler for DB sessions and transactions.
            user_data (UserData): Authenticated user info.
            integration (Integration): Integration metadata from the DB.

        Returns:
            ResponseData: Response indicating success, data, and credential validity.
        """

        try:
            # Get credentials from context or existing KB
            if provider_context.kb_id:
                kb = await self.knowledge_base_dao.get_by_id(
                    kb_id=provider_context.kb_id
                )
                if not kb:
                    raise ValueError(
                        f"Knowledge base with id {provider_context.kb_id} does not exist."
                    )
                credentials_json = provider_context.credentials or kb.settings_json.get(
                    "credentials", {}
                )
            else:
                credentials_json = provider_context.credentials

            # Validate credentials and get table schemas
            bigquery_strategy = BigQueryClientStrategy(credentials_json)

            if not await bigquery_strategy.validate_credentials():
                return ResponseData.model_construct(
                    success=False, message="Failed to validate BigQuery credentials"
                )

            db_context = DatabaseContext(strategy=bigquery_strategy)
            tables_and_columns = await db_context.get_schemas_for_tables_in(
                provider_context.database_name
            )

            # Get existing documents if KB exists
            documents = []
            if provider_context.kb_id:
                vectordb_for_sql = ElasticsearchSQLAdapter()
                documents = await vectordb_for_sql.get_documents_by_kb_id(
                    index_name="data_analyst_index", kb_id=provider_context.kb_id
                )

            transformed_tables = transform_tables_data(documents, tables_and_columns)

            return ResponseData.model_construct(
                success=True,
                data={"kb": {"data": transformed_tables}, "is_valid_cred": True},
            )

        except Exception as e:  # pylint: disable=broad-exception-caught
            return await self.handle_transaction_error(
                connection_handler, "BigQuery Tables", e
            )
