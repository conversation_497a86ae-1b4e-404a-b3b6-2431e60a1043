"""Serializers and data models for knowledge base management and operations.

This module contains Pydantic models and serializers for:
- Knowledge base creation, updates, and queries
- Vector search requests and responses
- Integration configurations and provider contexts
- File content and Elasticsearch document structures
- Various request/response models for knowledge base operations
"""
import hashlib
import uuid
from typing import Optional, Dict, List, Any, Union

from pydantic.class_validators import root_validator,validator
from pydantic.main import BaseModel
from pydantic import Field

from knowledge_base.models import IntegrationType, SourceItemKind


class UpdateKB(BaseModel):
    """Request model for updating knowledge base properties."""
    kb_id: int
    name: Optional[str] = None
    pat: Optional[str] = None
    url: Optional[str] = None
    branch_name: Optional[str] = None


class GetKbRequest(BaseModel):
    """Request model for retrieving knowledge bases with filtering and pagination."""
    search_term: Optional[str] = None  # search in kb name and source_identifier
    team_ids: List[Optional[str]] = Field(default_factory=list)
    org_ids: List[Optional[str]] = Field(default_factory=list)
    page: int = 1
    size: int = 10
    state: Optional[str] = None

class GetKbInternalRequest(BaseModel):
    """Internal request model for retrieving knowledge bases with user context."""
    search_term: Optional[str] = None
    team_ids: Optional[List[str]] = None
    org_ids: Optional[List[str]] = None
    page: int = 1
    size: int = 0 # bypass pagination for internal use
    state: Optional[str] = None
    user_id: str

class ShareKnowledgeBaseRequest(BaseModel):
    """Request model for sharing knowledge bases with teams, users, or organizations."""
    kb_ids: List[int]
    team_id: Optional[List[str]] = []
    user_id: Optional[List[str]] = []
    org_id: Optional[List[str]] = []


class LocalFileContext(BaseModel):
    """Context model for local file-based knowledge base operations."""
    kb_id: str


class ProviderContext(BaseModel):
    """Context model for provider-based knowledge base operations with credentials."""
    kb_id: Union[str, int]
    provider: IntegrationType
    description: Optional[str] = None
    credentials: Optional[Dict[str, Any]] = None
    kb_name: Optional[str] = None
    is_updatable: Optional[bool] = False
    database_name: Optional[str] = None

    @validator('kb_id', pre=True)
    def convert_kb_id_to_int(cls, v):
        """Convert kb_id to int if it's a string."""
        if isinstance(v, str):
            # Return empty string as-is
            if v == "":
                return v
            try:
                return int(v)
            except ValueError:
                raise ValueError(f"kb_id must be convertible to int, got: {v}")
        return v

    @root_validator(pre=True)
    def clean_github_url(cls, values):
        """Validator to strip trailing `.git` from GitHub/GitLab URLs inside credentials.

        Args:
            values (dict): Field values passed into the model.

        Returns:
            dict: Cleaned values with `.git` removed if applicable.
        """

        provider = values.get("provider")
        credentials = values.get("credentials", {})
        if not credentials:
            return values

        if provider == IntegrationType.github.value or provider == IntegrationType.gitlab.value:
            url = credentials.get("url")
            if isinstance(url, str) and url.endswith(".git"):
                credentials["url"] = url[:-4]  # Remove ".git"
                values["credentials"] = credentials

        return values


class KnowledgeBaseRequest(BaseModel):
    """Request model for knowledge base creation or update operations."""
    add_context_through_local_files: Optional[LocalFileContext] = None
    add_context_through_provider: Optional[ProviderContext] = None
    team_id: Optional[str] = None
    context: str  # "add_context_through_provider" or "add_context_through_local_files


class GetKBStatusRequest(BaseModel):
    """Request model for retrieving knowledge base status information."""
    team_id: Optional[str] = None
    kb_ids: Optional[List[int]] = None


class DeleteKnowledgeBasesRequest(BaseModel):
    """Request model for deleting multiple knowledge bases."""
    kb_ids: List[int]


class GetVectorSearchRequest(BaseModel):
    """Request model for vector-based search operations on knowledge bases."""
    query: str
    knowledge_base_id: list[int]
    type: Optional[str] = None
    matching_percentage: float
    top_answer_count: int
    team_id: Optional[str] = ""
    user_id: Optional[str] = ""
    org_id: Optional[str] = ""


class KnowledgeBaseElasticDocument:
    def __init__(
        self, id, title, embedding, content, chunk_references, source, is_chunked, 
        is_public, description, knowledge_base_id, metadata, chunk_checksum=None
    ):
        """Initializes a document object to be indexed in Elasticsearch.

        Args:
            id (str): Unique chunk ID (with optional suffix).
            title (str): Document or chunk title.
            embedding (list[float] or None): Vector embedding of the chunk content.
            content (str): Text content of the chunk.
            chunk_references (list[str]): List of all chunk IDs in the same document.
            source (str): Origin system (e.g., 'quip', 'github').
            is_chunked (bool): Whether the document is chunked.
            is_public (bool): Indicates if the document is public.
            description (str): Optional LLM-generated description.
            knowledge_base_id (int or str): Associated knowledge base ID.
            metadata (dict): Additional metadata for filtering or attribution.
            chunk_checksum (str, optional): SHA256 checksum of the chunk text.
        """

        self.id = id
        self.title = title
        self.embedding = embedding
        self.content = content
        self.chunk_references = chunk_references
        self.source = source
        self.is_chunked = is_chunked
        self.is_public = is_public
        self.description = description  # for future optimization - want to store 
                                       # llm generated description for reuse
        self.knowledge_base_id = knowledge_base_id
        self.metadata = metadata
        self.chunk_checksum = chunk_checksum  # ✅ new field

    def to_dict(self):
        """Convert the object to a dictionary for serialization."""
        return {
            "id": self.id,
            "title": self.title,
            "embedding": self.embedding,
            "content": self.content,
            "chunk_references": self.chunk_references,
            "source": self.source,
            "is_chunked": self.is_chunked,
            "is_public": self.is_public,
            "description": self.description,
            "knowledge_base_id": self.knowledge_base_id,
            "metadata": self.metadata if self.metadata else None,
            "chunk_checksum": self.chunk_checksum  # ✅ include in dict
        }


class FileContent:
    def __init__(
            self,
            path: str,
            content: str,
            version_tag: str,
            provider_item_id: str,
            checksum: str = None,
            uuid_str: str = None,
            kind: str = SourceItemKind.file.value
    ):
        """Initializes a FileContent object used to represent a source file in memory.

        Args:
            path (str): Relative file path.
            content (str): File content.
            version_tag (str): Version identifier (e.g., commit SHA).
            provider_item_id (str): Provider-specific ID for the file (e.g., blob SHA).
            checksum (str, optional): Optional precomputed checksum; if not provided, 
                calculated from content.
            uuid_str (str, optional): Optional UUID to assign; generated if not provided.
            kind (str): Type of content (defaults to 'file').
        """

        self.path = path
        self.content = content
        self.version_tag = version_tag
        self.provider_item_id = provider_item_id
        self.checksum = checksum or hashlib.sha256(content.encode()).hexdigest()
        self.uuid = uuid_str or str(uuid.uuid4())
        self.kind = kind

    def to_dict(self) -> dict:
        """Converts the FileContent object to a dictionary for downstream use.

        Returns:
            dict: Dictionary with file content metadata and identifiers.
        """

        return {
            "path": self.path,
            "content": self.content,
            "version_tag": self.version_tag,
            "provider_item_id": self.provider_item_id,
            "checksum": self.checksum,
            "uuid": self.uuid,
            "kind": self.kind
        }


class GetKnowledgeBaseRequest(BaseModel):
    team_id: Optional[str]
    user_id: Optional[str]
    org_id: Optional[str]


class GetSettingsJsonCredentials(BaseModel):
    url: Optional[str] = None
    urls: Optional[List[str]] = None
    branch_name: Optional[str] = None


class GetSettingsJsonEdit(BaseModel):
    credentials: Optional[GetSettingsJsonCredentials] = None
    database_name: Optional[str] = None


class Integration(BaseModel):
    type: IntegrationType
    name: str
    credentials_json: Optional[dict] = None
    image_name: Optional[str] = None
    integration_help_url: Optional[str] = None

    class Config:
        from_attributes = True


class GetKnowledgeBaseUpdate(BaseModel):
    id: int
    name: str
    description:Optional[str]=None
    settings_json: Optional[GetSettingsJsonEdit] = None
    integration: Optional[Integration]

    class Config:
        from_attributes = True


class GetFolderStructureRequest(BaseModel):
    kb_ids: List[int] = Field(..., description="List of knowledge base IDs")
    folder_path: Optional[str] = Field(
        None, description="Optional folder path to get structure for"
    )


class GetFileContentRequest(BaseModel):
    kb_id: int = Field(..., description="Knowledge base ID")
    file_path: str = Field(
        ..., min_length=1, description="Path to the file whose content is requested"
    )
