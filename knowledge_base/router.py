from fastapi import APIRouter

from app.routing import CustomRequestRoute
from knowledge_base.views import (
    start_loading,
    get_vector_search,
    get_integrations,
    get_indexing_status,
    delete_knowledge_bases,
    share_knowledge_bases,
    get_knowledge_bases,
    get_knowledge_bases_internal,
    revoke_knowledge_base_access,
    is_url_public,
    update_kb,
    load_selected_tables_for_sql,
    get_folder_structure,
    get_file_content,
    get_knowledge_base,
    get_knowledge_base_for_edit
)


router = APIRouter(prefix="/kb", tags=["KnowledgeBase"], route_class=CustomRequestRoute)


router.add_api_route(
    "/start-loading",
    endpoint=start_loading,
    methods=["POST"],
    description="Trigger the ingestion of a knowledge base"
)


router.add_api_route(
    "/vector-search",
    endpoint=get_vector_search,
    methods=["POST"],
    description="Perform vector search over knowledge base content"
)


router.add_api_route(
    "/get-integrations",
    endpoint=get_integrations,
    methods=["GET"],
    description="Get integrations available to the user"
)


router.add_api_route(
    "/status",
    endpoint=get_indexing_status,
    methods=["POST"],
    description="Check indexing status of knowledge base"
)


router.add_api_route(
    "/delete-kbs",
    endpoint=delete_knowledge_bases,
    methods=["DELETE"],
    description="Delete one or more knowledge bases"
)


router.add_api_route(
    "/share",
    endpoint=share_knowledge_bases,
    methods=["POST"],
    description="Share a knowledge base with user/team/org"
)


router.add_api_route(
    "/filter",
    endpoint=get_knowledge_bases,
    methods=["POST"],
    description="Get filtered knowledge bases by user context"
)


router.add_api_route(
    "/get_kbs",
    endpoint=get_knowledge_bases_internal,
    methods=["POST"],
    description="Get internal list of knowledge bases for given user/org"
)


router.add_api_route(
    "/revoke",
    endpoint=revoke_knowledge_base_access,
    methods=["POST"],
    description="Revoke knowledge base access"
)


router.add_api_route(
    "/is-url-public/{provider}",
    endpoint=is_url_public,
    methods=["GET"],
    description="Check if URL is publicly accessible for a provider"
)


router.add_api_route(
    "/update",
    endpoint=update_kb,
    methods=["POST"],
    description="Update knowledge base metadata or permissions"
)


router.add_api_route(
    "/sql/load-selected",
    endpoint=load_selected_tables_for_sql,
    methods=["POST"],
    description="Load selected tables from SQL database into knowledge base"
)


router.add_api_route(
    "/folder-structure",
    endpoint=get_folder_structure,
    methods=["POST"],
    description="Get folder structure of files linked to knowledge base"
)


router.add_api_route(
    "/file-content",
    endpoint=get_file_content,
    methods=["POST"],
    description="Get content of specific file in the knowledge base"
)


router.add_api_route(
    "/{kb_id}",
    endpoint=get_knowledge_base,
    methods=["POST"],
    description="Retrieve a specific knowledge base by ID"
)


router.add_api_route(
    "/{kb_id}/edit",
    endpoint=get_knowledge_base_for_edit,
    methods=["GET"],
    description="Retrieve a specific knowledge base with edit context"
)
