from config.logging import logger
from knowledge_base.models import IntegrationType
from knowledge_base.serializers import KnowledgeBaseElasticDocument
from knowledge_base.data_transformers.base import DataTransformer
from utils.vector_db.embeddings import EmbeddingGenerator


class GoogleDocsTransformer(DataTransformer):
    """Transforms Google Docs records into structured format with embeddings."""

    SOURCE_TYPE = IntegrationType.google_docs.value

    def _extract_content(self, record: dict) -> str:
        """Extracts text content from Google Docs record."""
        lines = []
        for block in record["content"]["body"]:
            paragraph = block.get("paragraph")
            if paragraph:
                elements = paragraph.get("elements", [])
                text_runs = [
                    el.get("textRun", {}).get("content", "") for el in elements
                ]
                paragraph_text = "".join(text_runs).strip()
                if paragraph_text:
                    lines.append(paragraph_text)

        return "\n".join(lines)

    def _get_document_title(self, record: dict) -> str:
        """Gets the document title from Google Docs record."""
        return record["content"]["title"]

    async def _convert(
        self, record: dict, embedder: EmbeddingGenerator, **kwargs
    ) -> list[KnowledgeBaseElasticDocument]:
        """Converts Google Docs content to a list of structured and embedded document chunks."""
        logger.info(f"Processing Google Doc: {record['path']}")

        text_content = self._extract_content(record)
        chunks, line_offsets = self._tokenize_and_chunk(text_content)

        return await self._build_documents(
            record, embedder, chunks, line_offsets, **kwargs
        )
