import asyncio
from abc import ABC, abstractmethod
from typing import List, Tuple

from config.logging import logger
from config.settings import loaded_config
from knowledge_base.data_transformers import utils as transformer_utils
from knowledge_base.serializers import KnowledgeBaseElasticDocument
from utils.vector_db.embeddings import EmbeddingGenerator


class DataTransformer(ABC):
    """Abstract base class for transforming records into a structured document format."""

    # Class-level configuration variables that subclasses should override
    SOURCE_TYPE: str = None  # Must be overridden in subclasses

    async def transform(
        self, records: list[dict], embedder: EmbeddingGenerator, **kwargs
    ) -> list[dict]:
        """Transforms raw records into structured documents with embeddings.

        Args:
            records (list[dict]): List of raw input records.
            embedder (EmbeddingGenerator): Embedding generator instance.
            **kwargs: Additional parameters.

        Returns:
            list[dict]: List of transformed document dictionaries.
        """
        logger.info(f"Transforming {len(records)} {self.__class__.__name__} records...")
        tasks = [self._convert(record, embedder, **kwargs) for record in records]
        results = await asyncio.gather(*tasks)
        return [doc.to_dict() for sublist in results for doc in sublist]

    @abstractmethod
    async def _convert(
        self, record: dict, embedder: EmbeddingGenerator, **kwargs
    ) -> list[KnowledgeBaseElasticDocument]:
        """Converts a single record into structured document chunks.

        Args:
            record (dict): Input record.
            embedder (EmbeddingGenerator): Embedding generator instance.
            **kwargs: Additional metadata and options.

        Returns:
            list[KnowledgeBaseElasticDocument]: Transformed documents.
        """
        pass

    @abstractmethod
    def _extract_content(self, record: dict) -> str:
        """Extracts text content from a record.

        Args:
            record (dict): Input record.

        Returns:
            str: Extracted text content.
        """
        pass

    def _get_document_title(self, record: dict) -> str:
        """Gets the document title from a record. Can be overridden in subclasses.

        Args:
            record (dict): Input record.

        Returns:
            str: Document title.
        """
        return record.get("path", "")

    @staticmethod
    def _tokenize_and_chunk(text: str) -> Tuple[List[str], List[Tuple[int, int]]]:
        """Tokenizes and chunks text content.

        Args:
            text (str): Input text content.

        Returns:
            Tuple[List[str], List[Tuple[int, int]]]: Chunks and their line offsets.
        """
        _, encoding = transformer_utils.tokenize(text)
        lines = text.splitlines()
        token_line_indices = transformer_utils.compute_line_token_indices(
            lines, encoding
        )

        chunks, line_offsets = transformer_utils.chunk_content(
            text=text,
            chunk_token_limit=int(loaded_config.chunk_token_limit),
            encoding=encoding,
            token_line_indices=token_line_indices,
        )

        return chunks, line_offsets

    @staticmethod
    def _generate_chunk_ids(record: dict, num_chunks: int, **kwargs) -> List[str]:
        """Generates chunk IDs for a record.

        Args:
            record (dict): Input record.
            num_chunks (int): Number of chunks.
            **kwargs: Additional parameters (may contain 'old_uuid').

        Returns:
            List[str]: List of chunk IDs.
        """
        uuid = kwargs.get("old_uuid", record["uuid"])
        return [f"{uuid}#{i + 1}" for i in range(num_chunks)]

    def _generate_chunk_comment(
        self,
        record: dict,
        chunk_index: int,
        total_chunks: int,
        start_line: int,
        end_line: int,
    ) -> str:
        """Generates a contextual comment for a chunk.

        Args:
            record (dict): Input record.
            chunk_index (int): Current chunk index (0-based).
            total_chunks (int): Total number of chunks.
            start_line (int): Starting line number.
            end_line (int): Ending line number.

        Returns:
            str: Generated comment.
        """
        return (
            f"The following content belongs to the {self.SOURCE_TYPE} document at path: {record['path']}.\n"
            f"This is chunk {chunk_index + 1} out of {total_chunks} total chunks.\n"
            f"The chunk spans lines {start_line} to {end_line}."
        )

    async def _build_documents(
        self,
        record: dict,
        embedder: EmbeddingGenerator,
        chunks: List[str],
        line_offsets: List[Tuple[int, int]],
        **kwargs,
    ) -> List[KnowledgeBaseElasticDocument]:
        """Builds document objects with metadata and embeddings from text chunks.

        Args:
            record (dict): Original source record.
            embedder (EmbeddingGenerator): Embedding generator instance.
            chunks (List[str]): Text segments.
            line_offsets (List[Tuple[int, int]]): Start and end line info for each chunk.
            **kwargs: Additional metadata.

        Returns:
            List[KnowledgeBaseElasticDocument]: Final chunked documents.
        """
        chunk_ids = self._generate_chunk_ids(record, len(chunks), **kwargs)
        documents = []
        generate_embeddings = kwargs.get("generate_embeddings", True)

        for i, chunk in enumerate(chunks):
            start_line, end_line = line_offsets[i]
            comment = self._generate_chunk_comment(
                record, i, len(chunks), start_line, end_line
            )

            full_text = f"{comment}\n{chunk}"
            embedding = None
            if generate_embeddings:
                embedding = await embedder.generate_embedding(full_text)

            documents.append(
                KnowledgeBaseElasticDocument(
                    id=chunk_ids[i],
                    title=self._get_document_title(record),
                    embedding=embedding,
                    content=full_text,
                    chunk_references=chunk_ids,
                    source=self.SOURCE_TYPE,
                    is_chunked=len(chunks) > 1,
                    is_public=False,
                    description="",
                    knowledge_base_id=kwargs.get("knowledge_base_id"),
                    metadata={
                        "chunk_number": i + 1,
                        "total_chunks": len(chunks),
                        "path": record["path"],
                        "start_line": start_line,
                        "end_line": end_line,
                        "source_url": record.get("source_url"),
                    },
                )
            )

        return documents
