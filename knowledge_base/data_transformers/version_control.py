from config.logging import logger
from knowledge_base.data_transformers.base import DataTransformer
from knowledge_base.serializers import KnowledgeBaseElasticDocument
from utils.vector_db.embeddings import EmbeddingGenerator


class RepoTransformer(DataTransformer):
    """Transforms Azure DevOps repository data into structured format with embeddings."""

    SOURCE_TYPE = "azure_devops"

    def _extract_content(self, record: dict) -> str:
        """Extracts text content from repository record."""
        return record["content"]

    def _generate_chunk_comment(
        self,
        record: dict,
        chunk_index: int,
        total_chunks: int,
        start_line: int,
        end_line: int,
    ) -> str:
        """Generates a contextual comment for a repository file chunk."""
        return (
            f"The following content belongs to the file at path: {record['path']}.\n"
            f"This is chunk {chunk_index + 1} out of {total_chunks} total chunks.\n"
            f"The chunk spans lines {start_line} to {end_line}."
        )

    async def _convert(
        self, record: dict, embedder: EmbeddingGenerator, **kwargs
    ) -> list[KnowledgeBaseElasticDocument]:
        """Converts a repo file into structured chunked documents with optional embeddings."""
        logger.info(f"Processing repo file: {record['path']}")

        text_content = self._extract_content(record)
        chunks, line_offsets = self._tokenize_and_chunk(text_content)

        return await self._build_documents(
            record, embedder, chunks, line_offsets, **kwargs
        )
