import re

from bs4 import BeautifulSoup

from config.logging import logger
from knowledge_base.data_transformers.base import DataTransformer
from knowledge_base.serializers import KnowledgeBaseElasticDocument
from utils.vector_db.embeddings import EmbeddingGenerator


class QuipTransformer(DataTransformer):
    """Transforms Quip HTML documents into structured format with embeddings."""

    SOURCE_TYPE = "quip"

    def _extract_content(self, record: dict) -> str:
        """Extracts text content from Quip HTML record."""
        soup = self._clean_html(record["content"])
        return self._extract_text(soup)

    async def _convert(
        self, record: dict, embedder: EmbeddingGenerator, **kwargs
    ) -> list[KnowledgeBaseElasticDocument]:
        """Cleans and chunks Quip HTML content, then builds document objects with embeddings."""
        logger.info(f"Processing Quip record: {record['path']}")

        text_content = self._extract_content(record)
        chunks, line_offsets = self._tokenize_and_chunk(text_content)

        return await self._build_documents(
            record, embedder, chunks, line_offsets, **kwargs
        )

    @staticmethod
    def _clean_html(html: str) -> BeautifulSoup:
        """Cleans up raw Quip HTML, removing invisible characters and formatting tags."""
        soup = BeautifulSoup(html, "html.parser")

        # Remove invisible characters
        for element in soup.find_all(
            string=lambda text: isinstance(text, str)
            and ("\u200b" in text or "\u00a0" in text)
        ):
            element.replace_with(" " if "\u00a0" in element else "")

        for li in soup.find_all("li"):
            li.insert(0, "• ")
        for bold in soup.find_all("b"):
            bold.replace_with(f"**{bold.get_text()}**")

        return soup

    @staticmethod
    def _extract_text(soup: BeautifulSoup) -> str:
        """Extracts readable text from cleaned HTML content."""
        raw_text = soup.get_text(separator="\n").strip()
        return re.sub(r"\n\s*\n", "\n\n", raw_text)
