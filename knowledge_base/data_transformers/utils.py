import tiktoken
from typing import List, <PERSON><PERSON>


def tokenize(text: str) -> <PERSON><PERSON>[List[int], tiktoken.Encoding]:
    encoding = tiktoken.get_encoding("cl100k_base")
    return encoding.encode(text), encoding


def compute_line_token_indices(
    lines: List[str], encoding: tiktoken.Encoding
) -> List[int]:
    indices = []
    for idx, line in enumerate(lines):
        token_count = len(encoding.encode(line + "\n"))
        indices.extend([idx + 1] * token_count)
    return indices


def chunk_content(
    text: str,
    chunk_token_limit: int,
    encoding: tiktoken.Encoding,
    token_line_indices: List[int],
) -> Tuple[List[str], List[Tuple[int, int]]]:
    tokens = encoding.encode(text)
    chunks, line_offsets = [], []
    start = 0

    while start < len(tokens):
        end = min(start + chunk_token_limit, len(tokens))
        chunk_text = encoding.decode(tokens[start:end])
        chunks.append(chunk_text)

        start_line = (
            token_line_indices[start] if start < len(token_line_indices) else None
        )
        end_line = (
            token_line_indices[end - 1] if end - 1 < len(token_line_indices) else None
        )
        line_offsets.append((start_line, end_line))

        start = end

    return chunks, line_offsets
