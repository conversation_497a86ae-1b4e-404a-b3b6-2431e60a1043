from datetime import datetime

from sqlalchemy import select
from sqlalchemy import update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from config.logging import logger
from knowledge_base.models import IngestionRun, IngestionError
from utils.dao import BaseDao


class IngestionRunDao(BaseDao):
    def __init__(self, session: AsyncSession):
        """Initializes the IngestionRunDao with the IngestionRun model.

        Args:
            session (AsyncSession): SQLAlchemy async session.
        """

        super().__init__(session=session, db_model=IngestionRun)

    async def create_ingestion_run(self, **kwargs) -> IngestionRun:
        """Creates a new ingestion run, optionally initializing stats.

        Returns:
            IngestionRun: The created ingestion run.
        """

        if kwargs.pop("is_default_stats", False):
            kwargs["stats_json"] = {
                "total_source_item": 0,
                "total_chunks": 0,
                "failed_chunk_elastic_doc_id": [],
            }

        ingestion_run = IngestionRun(**kwargs)
        self.session.add(ingestion_run)
        await self.session.flush()
        await self.session.commit()
        return ingestion_run

    async def update_run_summary(
        self,
        run_id: int,
        status: str,
        total_source_item: int,
        total_chunks: int,
        failed_chunk_elastic_doc_id: list[str],
    ):
        """Updates summary information (chunks, stats, status) for an ingestion run.

        Args:
            run_id (int): ID of the run to update.
            status (str): New run status (e.g. 'completed', 'failed').
            total_source_item (int): Count of source items.
            total_chunks (int): Count of chunks.
            failed_chunk_elastic_doc_id (list[str]): Failed chunk doc IDs.
        """

        stmt = (
            update(IngestionRun)
            .where(IngestionRun.id == run_id)
            .values(
                finished_at=datetime.utcnow(),
                status=status,
                stats_json={
                    "total_source_item": total_source_item,
                    "total_chunks": total_chunks,
                    "failed_chunk_elastic_doc_id": failed_chunk_elastic_doc_id,
                },
            )
        )

        await self.session.execute(stmt)
        logger.info(f"Updated run summary {total_chunks} and {total_source_item}")
        await self.session.commit()

    async def get_latest_stats_json_by_kb_id(self, kb_id: int) -> dict:
        """Fetches the latest stats JSON for a KB.

        Args:
            kb_id (int): Knowledge base ID.

        Returns:
            dict: Stats JSON or None.
        """

        stmt = (
            select(IngestionRun.stats_json)
            .where(IngestionRun.kb_id == kb_id)
            .order_by(IngestionRun.started_at.desc())
            .limit(1)
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_latest_ingestion_run_by_kb_id(
        self, kb_id: int
    ) -> IngestionRun | None:
        """Fetches the most recent ingestion run for a KB.

        Args:
            kb_id (int): Knowledge base ID.

        Returns:
            IngestionRun | None: Latest run or None.
        """

        stmt = (
            select(IngestionRun)
            .where(IngestionRun.kb_id == kb_id)
            .order_by(IngestionRun.started_at.desc())
            .limit(1)
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()


class IngestionErrorDao(BaseDao):
    def __init__(self, session: AsyncSession):
        """Initializes the IngestionErrorDao with the IngestionError model.

        Args:
            session (AsyncSession): SQLAlchemy session.
        """

        super().__init__(session=session, db_model=IngestionError)

    async def create_ingestion_error(
        self, commit: bool = True, **kwargs
    ) -> IngestionError:
        """Creates a new ingestion error record in the database.

        Args:
            commit (bool): Whether to commit the transaction immediately. Defaults to True.
            **kwargs: Fields to populate the IngestionError record (e.g., run_id, error_message, etc.).

        Returns:
            IngestionError: The newly created ingestion error record.
        """

        error = IngestionError(**kwargs)
        self.session.add(error)
        await self.session.flush()

        if commit:
            await self.session.commit()
        return error
