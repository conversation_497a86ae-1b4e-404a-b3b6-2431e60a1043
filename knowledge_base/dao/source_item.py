from typing import List

from sqlalchemy import select
from sqlalchemy import delete, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload
from knowledge_base.models import SourceItem, Chunk
from utils.dao import BaseDao
from utils.exceptions import CustomException


class SourceItemDao(BaseDao):
    def __init__(self, session: AsyncSession):
        """Initializes the SourceItemDao with the SourceItem model.

        Args:
            session (AsyncSession): SQLAlchemy session.
        """

        super().__init__(session=session, db_model=SourceItem)

    async def bulk_insert(self, source_items: list[dict]):
        """Bulk inserts multiple source item records into the database.

        Args:
            source_items (list[dict]): List of source item dicts.

        Raises:
            CustomException: If insertion fails.
        """

        try:
            # Convert dictionaries to SourceItem objects
            items_to_insert = [SourceItem(**item) for item in source_items]

            # Add the items to the session in bulk
            self.session.add_all(items_to_insert)

            # Commit the transaction to persist the data
            await self.session.commit()
        except Exception as e:
            await self.session.rollback()
            raise CustomException(
                f"Error occurred while bulk inserting source items: {str(e)}"
            )

    async def delete_by_id(self, id: str) -> None:
        """Delete source item by ID"""
        try:
            stmt = delete(self.db_model).where(self.db_model.id == id)
            await self.session.execute(stmt)
            await self.session.commit()
        except Exception as e:
            await self.session.rollback()
            raise CustomException(f"Error deleting source item: {str(e)}")

    async def get_all_by_kb_id(self, kb_id: int) -> List[SourceItem]:
        """Get all source items for a specific knowledge base ID"""
        stmt = select(self.db_model).where(self.db_model.kb_id == kb_id)
        result = await self.session.execute(stmt)
        return result.scalars().all()

    async def delete_by_kb_id_logical_path(self, kb_id: int, path: str) -> list[str]:
        """Deletes all source items (and their chunks) by KB ID and file path.

        Args:
            kb_id (int): Knowledge base ID.
            path (str): Logical file path.

        Returns:
            list[str]: List of deleted source item IDs.
        """

        # Step 1: Fetch SourceItems with their Chunks
        result = await self.session.execute(
            select(SourceItem)
            .options(joinedload(SourceItem.chunks))
            .where(SourceItem.kb_id == kb_id, SourceItem.logical_path == path)
        )
        source_items = result.unique().scalars().all()

        if not source_items:
            return []

        # Step 2: Collect SourceItem IDs and all associated Chunks
        source_item_ids = [item.id for item in source_items]

        # Step 3: Delete SourceItems (cascades to delete Chunks)
        for item in source_items:
            await self.session.delete(item)

        await self.session.flush()
        await self.session.commit()

        return source_item_ids

    async def get_by_kb_id_logical_path(
        self, kb_id: int, path: str
    ) -> list[SourceItem]:
        """
        Fetch source items and their chunks based on knowledge base ID and logical path.
        """
        result = await self.session.execute(
            select(SourceItem)
            .options(joinedload(SourceItem.chunks))
            .where(SourceItem.kb_id == kb_id, SourceItem.logical_path == path)
        )
        return result.unique().scalars().all()

    async def get_kb_source_and_chunk_counts(self, kb_id: int) -> dict:
        """
        Get the count of source items and their associated chunks for a specific knowledge base,
        along with a list of all logical paths.

        Args:
            kb_id: Knowledge base ID

        Returns:
            Dictionary containing source_count, chunk_count, and paths
        """
        # Count source items
        source_count_query = (
            select(func.count())
            .select_from(self.db_model)
            .where(self.db_model.kb_id == kb_id)
        )
        source_count_result = await self.session.execute(source_count_query)
        source_count = source_count_result.scalar_one_or_none() or 0

        # Count chunks associated with this KB's source items
        chunk_count_query = (
            select(func.count(Chunk.id))
            .select_from(Chunk)
            .join(self.db_model, Chunk.source_item_id == self.db_model.id)
            .where(self.db_model.kb_id == kb_id)
        )
        chunk_count_result = await self.session.execute(chunk_count_query)
        chunk_count = chunk_count_result.scalar_one_or_none() or 0

        # Get all logical paths
        paths_query = select(self.db_model.logical_path).where(
            self.db_model.kb_id == kb_id
        )
        paths_result = await self.session.execute(paths_query)
        paths = [path for (path,) in paths_result if path]

        return {
            "source_count": source_count,
            "chunk_count": chunk_count,
            "paths": paths,
        }
