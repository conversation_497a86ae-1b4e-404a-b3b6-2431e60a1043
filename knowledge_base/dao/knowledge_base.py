from datetime import datetime, timezone
from typing import List
from typing import Optional

from sqlalchemy import select
from sqlalchemy import update, delete, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload

from config.logging import logger
from knowledge_base.models import (
    Integration,
    IntegrationType,
    KnowledgeBase,
    IngestionRun,
    KBState,
)
from knowledge_base.serializers import GetKbRequest
from utils.dao import BaseDao


class KnowledgeBaseDao(BaseDao):
    def __init__(self, session: AsyncSession):
        """Initializes the KnowledgeBaseDao with the KnowledgeBase model.

        Args:
            session (AsyncSession): SQLAlchemy async session instance.
        """

        super().__init__(session=session, db_model=KnowledgeBase)

    async def create_knowledge_base(self, commit=False, **kwargs) -> KnowledgeBase:
        """Creates a new knowledge base record.

        Args:
            commit (bool): Whether to commit the transaction.
            **kwargs: Fields to populate the knowledge base.

        Returns:
            KnowledgeBase: The newly created instance.
        """

        kb = KnowledgeBase(**kwargs)
        self.session.add(kb)
        await self.session.flush()
        if commit:
            await self.session.commit()
        return kb

    async def update_state_by_id(
        self, kb_id: int, new_state: KBState, commit: bool = True
    ) -> None:
        """Updates the state of a knowledge base.

        Args:
            kb_id (int): Knowledge base ID.
            new_state (KBState): New state to apply.
            commit (bool): Whether to commit the transaction.
        """

        stmt = (
            update(KnowledgeBase)
            .where(KnowledgeBase.id == kb_id)
            .values(state=new_state, last_indexed_at=datetime.now(timezone.utc))
        )
        await self.session.execute(stmt)
        if commit:
            await self.session.commit()

    async def update_kb_credentials(
        self,
        kb_id: int,
        new_credentials: dict,
        user_data: dict = None,
        new_state: KBState = None,
        commit: bool = True,
    ) -> None:
        """
        Update credentials inside settings_json and optionally update state

        Args:
            kb_id: Knowledge base ID
            new_credentials: New credentials to store in settings_json
            user_data: Optional user data to update in settings_json
            new_state: Optional new state for the knowledge base
            commit: Whether to commit the transaction
        """
        # First get the current settings_json
        kb = await self.get_by_id(kb_id)
        if not kb:
            raise ValueError(f"Knowledge base with id {kb_id} not found")

        # Create updated settings_json
        updated_settings_json = kb.settings_json.copy() if kb.settings_json else {}
        updated_settings_json["credentials"] = new_credentials

        # Update user info if provided
        if user_data:
            updated_settings_json["user_id"] = user_data.get("userId")
            updated_settings_json["org_id"] = user_data.get("orgId")

        # Prepare update values
        update_values = {"settings_json": updated_settings_json}
        if new_state:
            update_values["state"] = new_state

        # Update the knowledge base
        stmt = (
            update(KnowledgeBase)
            .where(KnowledgeBase.id == kb_id)
            .values(**update_values)
        )
        await self.session.execute(stmt)
        if commit:
            await self.session.commit()

    async def update_kb_settings(
        self,
        kb_id: int,
        new_settings: dict,
        kb_name: str = None,
        kb_descp: Optional[str] = None,
        user_data: dict = None,
        new_state: KBState = None,
        commit: bool = True,
    ) -> None:
        """Updates the knowledge base settings_json and optionally state or name.

        Args:
            kb_id (int): Knowledge base ID.
            new_settings (dict): Settings to merge into settings_json.
            kb_name (str, optional): New name of the KB.
            kb_descp (str,optional): New descp of the KB
            user_data (dict, optional): User metadata.
            new_state (KBState, optional): Optional state update.
            commit (bool): Whether to commit the transaction.
        """

        # First get the current settings_json
        kb = await self.get_by_id(kb_id)
        if not kb:
            raise ValueError(f"Knowledge base with id {kb_id} not found")

        # Create updated settings_json
        updated_settings_json = kb.settings_json.copy() if kb.settings_json else {}
        updated_settings_json.update(new_settings)

        # Update user info if provided
        if user_data:
            updated_settings_json["user_id"] = user_data.get("userId")
            updated_settings_json["org_id"] = user_data.get("orgId")

        # Prepare update values
        update_values = {"settings_json": updated_settings_json}
        if new_state:
            update_values["state"] = new_state

        if kb_name:
            update_values["name"] = kb_name

        if kb_descp:
            update_values["description"] = kb_descp

        # Update the knowledge base
        stmt = (
            update(KnowledgeBase)
            .where(KnowledgeBase.id == kb_id)
            .values(**update_values)
        )
        await self.session.execute(stmt)
        if commit:
            await self.session.commit()

    async def update_kb_name_descp(
        self, kb_id: int, name: str, description: str, commit: bool = True
    ):

        update_values = {"name": name, "description": description}
        stmt = (
            update(KnowledgeBase)
            .where(KnowledgeBase.id == kb_id)
            .values(**update_values)
        )
        await self.session.execute(stmt)
        if commit:
            await self.session.commit()

    async def get_indexing_status(
        self, data: dict[str, list[int]], user_id: str, org_id: str = None
    ) -> dict[str, list[dict]]:
        """Fetches indexing status and integration details for each KB in all categories.

        Args:
            data (dict): Dict with keys like 'personal', 'team', and lists of KB IDs.
            user_id (str): ID of the requesting user.
            org_id (str, optional): Org ID context.

        Returns:
            dict[str, list[dict]]: Indexed KB info per category.
        """

        result = {key: [] for key in data.keys()}

        for category, kb_ids in data.items():
            if not kb_ids:
                continue

            # Get knowledge bases with their integration details
            kb_query = (
                select(KnowledgeBase, Integration)
                .join(Integration, KnowledgeBase.integration_id == Integration.id)
                .where(KnowledgeBase.id.in_(kb_ids))
            )
            if org_id is None:
                kb_query = kb_query.where(KnowledgeBase.org_id.is_(None))
            else:
                kb_query = kb_query.where(KnowledgeBase.org_id == org_id)

            kb_result = await self.session.execute(kb_query)

            for kb, integration in kb_result:
                # For each KB, get the latest ingestion run
                latest_run_query = (
                    select(IngestionRun)
                    .where(IngestionRun.kb_id == kb.id)
                    .order_by(IngestionRun.started_at.desc())
                    .limit(1)
                )
                latest_run_result = await self.session.execute(latest_run_query)
                latest_run = latest_run_result.scalar()

                kb_status = {
                    "kb_id": kb.id,
                    "name": kb.name,
                    "state": kb.state.value,
                    "update_token": True if kb.state.value == "token_error" else False,
                    "created_at": kb.created_at,
                    "last_indexed_at": kb.last_indexed_at,
                    "ingestion_status": None,
                    "success_percentage": 0.00,
                    "is_enabled": kb.state != KBState.disabled,
                    "is_creator": kb.created_by == user_id,
                    "is_updatable": kb.is_updatable,
                    "integration": {
                        "id": integration.id,
                        "type": integration.type.name,
                        "name": integration.name,
                        "image_name": integration.image_name,
                        "integration_help_url": integration.integration_help_url,
                    },
                }

                if latest_run and latest_run.stats_json:
                    total_chunks = latest_run.stats_json.get("total_chunks", 0)
                    failed_chunks = latest_run.stats_json.get(
                        "failed_chunk_elastic_doc_id", []
                    )
                    failed_count = len(failed_chunks) if failed_chunks else 0

                    if (
                        kb_status["integration"]["type"]
                        == IntegrationType.fynix_extension.value
                    ):
                        total_source = latest_run.stats_json.get("total_source_item", 0)
                        success_percentage = (
                            round((total_source / kb.total_files) * 100, 2)
                            if kb.total_files != 0
                            else 0.00
                        )

                    else:
                        success_percentage = 0.00
                        if total_chunks > 0:
                            success_percentage = round(
                                100 - (failed_count / total_chunks * 100), 2
                            )

                    kb_status.update(
                        {
                            "ingestion_status": latest_run.status,
                            "success_percentage": success_percentage,
                        }
                    )

                result[category].append(kb_status)

        return result

    async def delete_knowledge_bases(self, kb_ids: List[int]) -> None:
        """
        Delete knowledge bases. Cascading delete will automatically remove related records.
        Transaction handling should be done at service level.
        """
        stmt = delete(KnowledgeBase).where(KnowledgeBase.id.in_(kb_ids))
        await self.session.execute(stmt)

    async def get_by_id(self, kb_id: int):
        """Fetch a Knowledge Base by its ID."""
        query = await self.session.execute(
            select(KnowledgeBase).where(KnowledgeBase.id == kb_id)
        )
        return query.scalar_one_or_none()

    async def get_kb_by_id_with_integration(self, kb_id: int):
        """Fetch a Knowledge Base by its ID along with its integration details."""
        query = await self.session.execute(
            select(KnowledgeBase)
            .options(joinedload(KnowledgeBase.integration))
            .where(KnowledgeBase.id == kb_id)
        )
        return query.unique().scalar_one_or_none()

    async def get_knowledge_bases_for_cron(self) -> List[KnowledgeBase]:
        """Returns a list of active knowledge bases eligible for cron-based reindexing.

        Returns:
            List[KnowledgeBase]: Filtered list of KBs.
        """

        stmt = (
            select(KnowledgeBase)
            .join(Integration, KnowledgeBase.integration_id == Integration.id)
            .where(
                KnowledgeBase.state.not_in(
                    [KBState.updating, KBState.disabled, KBState.indexing]
                ),
                Integration.type.not_in(
                    [IntegrationType.bigquery, IntegrationType.fynix_extension]
                ),
            )
        )
        result = await self.session.execute(stmt)
        return result.scalars().all()

    async def get_by_source_and_user(
        self, source_identifier: str, user_id: str, org_id: str
    ) -> Optional[KnowledgeBase]:
        """
        Get a knowledge base by source identifier and user ID.

        Args:
            source_identifier: The source identifier (e.g., repository URL)
            user_id: The user ID stored in settings_json
            org_id: The organization ID

        Returns:
            The knowledge base if found, None otherwise
        """
        try:
            query = select(KnowledgeBase).where(
                KnowledgeBase.source_identifier == source_identifier,
                KnowledgeBase.created_by == user_id,
                KnowledgeBase.org_id == org_id,
            )

            result = await self.session.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error in get_by_source_and_user: {str(e)}")
            raise e

    async def update_memory_key_by_id(
        self, kb_id: int, key: str, value: dict, commit: bool = False
    ):
        """
        Updates a top-level key in the memory JSONB field with a dict value.
        Overwrites the existing key if it already exists.
        """
        stmt = (
            update(KnowledgeBase)
            .where(KnowledgeBase.id == kb_id)
            .values(memory=KnowledgeBase.memory.op("||")({key: value}))
            .execution_options(synchronize_session="fetch")
        )
        await self.session.execute(stmt)
        if commit:
            await self.session.commit()

    async def get_knowledge_bases_with_pagination(
        self, kb_ids: dict[str, list[int]], filter_params: GetKbRequest
    ) -> dict:
        """
        Get knowledge bases with pagination and filtering.

        Args:
            kb_ids: Dictionary containing KB IDs categorized by access type
            filter_params: Filter parameters from GetKbRequest

        Returns:
            Dictionary with knowledge bases and pagination information
        """
        # Combine all KB IDs into a single list
        all_kb_ids = []
        for category in kb_ids.values():
            all_kb_ids.extend(category)

        # Remove duplicates
        all_kb_ids = list(set(all_kb_ids))

        if not all_kb_ids:
            return {
                "knowledge_bases": [],
                "pagination": {
                    "total_count": 0,
                    "total_pages": 0,
                    "current_page": filter_params.page,
                    "page_size": filter_params.size,
                    "has_next": False,
                    "has_previous": False,
                },
            }

        # Build base query
        base_query = select(KnowledgeBase).where(KnowledgeBase.id.in_(all_kb_ids))

        # Apply state filter if provided
        if filter_params.state:
            base_query = base_query.where(KnowledgeBase.state == filter_params.state)

        # Apply search term filter if provided
        if filter_params.search_term:
            search_term = f"%{filter_params.search_term}%"
            base_query = base_query.where(
                or_(
                    KnowledgeBase.name.ilike(search_term),
                    KnowledgeBase.source_identifier.ilike(search_term),
                )
            )

        # Get total count for pagination
        count_query = select(func.count()).select_from(base_query.subquery())
        total_count_result = await self.session.execute(count_query)
        total_count = total_count_result.scalar()

        # Apply pagination
        page_size = filter_params.size
        page = filter_params.page

        if page_size > 0:
            base_query = base_query.offset((page - 1) * page_size).limit(page_size)

        # Add ordering by creation date (newest first)
        base_query = base_query.order_by(KnowledgeBase.created_at.desc())

        # Execute query
        result = await self.session.execute(base_query)
        knowledge_bases = result.scalars().all()

        # Calculate pagination info
        total_pages = (total_count + page_size - 1) // page_size if page_size > 0 else 1
        has_next = page < total_pages
        has_previous = page > 1

        # Get integration details for each knowledge base
        kb_with_integrations = []
        for kb in knowledge_bases:
            # Get integration details
            integration_query = select(Integration).where(
                Integration.id == kb.integration_id
            )
            integration_result = await self.session.execute(integration_query)
            integration = integration_result.scalar_one_or_none()

            kb_data = {
                "id": kb.id,
                "name": kb.name,
                "state": kb.state.value,
                "description": kb.description,
                "created_at": kb.created_at,
                "last_indexed_at": kb.last_indexed_at,
                "source_identifier": kb.source_identifier,
                "created_by": kb.created_by,
                "integration": None,
                "is_updatable": kb.is_updatable,
            }

            if integration:
                kb_data["integration"] = {
                    "id": integration.id,
                    "type": integration.type.name,
                    "name": integration.name,
                    "image_name": integration.image_name,
                    "integration_help_url": integration.integration_help_url,
                }

            kb_with_integrations.append(kb_data)

        return {
            "knowledge_bases": kb_with_integrations,
            "pagination": {
                "total_count": total_count,
                "total_pages": total_pages,
                "current_page": page,
                "page_size": page_size,
                "has_next": has_next,
                "has_previous": has_previous,
            },
        }

    async def update_kb_name_and_pat_url_branch(
        self,
        kb_id: int,
        name: Optional[str] = None,
        pat: Optional[str] = None,
        branch_name: Optional[str] = None,
        url: Optional[str] = None,
        commit: bool = True,
        state: Optional[KBState] = KBState.ready,
    ) -> None:
        """
        Update knowledge base name, PAT, branch name, and URL in settings_json.

        Args:
            kb_id: Knowledge base ID
            name: Optional new name for the knowledge base
            pat: Optional new Personal Access Token to store in settings_json
            branch_name: Optional new branch name to store in settings_json
            url: Optional new repository URL to store in settings_json
            commit: Whether to commit the transaction
            state: state of KB
        """
        # First get the current settings_json
        kb = await self.get_by_id(kb_id)
        if not kb:
            raise ValueError(f"Knowledge base with id {kb_id} not found")

        # Prepare update values
        update_values = {}

        # Update name if provided
        if name is not None:
            update_values["name"] = name

        # Update settings_json if any credential fields are provided
        if any(param is not None for param in [pat, branch_name, url]):
            updated_settings_json = kb.settings_json.copy() if kb.settings_json else {}

            # Ensure credentials dict exists
            if "credentials" not in updated_settings_json:
                updated_settings_json["credentials"] = {}

            # Update credentials with provided values
            if pat is not None:
                updated_settings_json["credentials"]["pat"] = pat

            if branch_name is not None:
                updated_settings_json["credentials"]["branch_name"] = branch_name

            if url is not None:
                updated_settings_json["credentials"]["url"] = url
                # Also update source_identifier if URL is changing
                update_values["source_identifier"] = url

            update_values["settings_json"] = updated_settings_json

        update_values["state"] = state
        # Only proceed if there are values to update
        if update_values:
            stmt = (
                update(KnowledgeBase)
                .where(KnowledgeBase.id == kb_id)
                .values(**update_values)
            )
            await self.session.execute(stmt)
            if commit:
                await self.session.commit()

    async def exist_by_graph_id(self, graph_id: str) -> Optional[KnowledgeBase]:
        """
        Check if a KnowledgeBase exists with the given graph_id.

        Args:
            graph_id: The graph ID to look up.

        Returns:
            The KnowledgeBase object if found, else None.
        """
        stmt = select(KnowledgeBase).where(KnowledgeBase.graph_id == graph_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def update_graph_details(self, graph_id: str, update_data: dict) -> None:
        """Updates any fields in a knowledge base identified by its graph ID.

        Args:
            graph_id (str): Unique graph ID of the KB.
            update_data (dict): Fields to update.
        """

        await self.session.execute(
            update(KnowledgeBase)
            .where(KnowledgeBase.graph_id == graph_id)
            .values(**update_data)
        )
        await self.session.flush()  # Ensures SQLAlchemy is aware of changes before commit
        await self.session.commit()
