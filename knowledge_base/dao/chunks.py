from typing import List

from sqlalchemy import select
from sqlalchemy import update, delete, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from knowledge_base.models import Chunk
from utils.dao import BaseDao
from utils.exceptions import CustomException


class ChunkDao(BaseDao):
    def __init__(self, session: AsyncSession):
        """Initializes the ChunkDao with the Chunk model.

        Args:
            session (AsyncSession): SQLAlchemy session.
        """

        super().__init__(session=session, db_model=Chunk)

    async def bulk_insert(self, chunks: list[dict]):
        """Bulk inserts chunk records into the database.

        Args:
            chunks (list[dict]): List of chunk dicts.

        Raises:
            CustomException: If insertion fails.
        """

        try:
            # Convert dictionaries to SourceItem objects
            items_to_insert = [Chunk(**item) for item in chunks]

            # Add the items to the session in bulk
            self.session.add_all(items_to_insert)

            # Commit the transaction to persist the data
            await self.session.commit()
        except Exception as e:
            await self.session.rollback()
            raise CustomException(
                f"Error occurred while bulk inserting source items: {str(e)}"
            )

    async def get_count_by_source_item_ids(self, source_item_ids: List[str]) -> int:
        """Get total count of chunks for given source item IDs"""
        stmt = (
            select(func.count())
            .select_from(self.db_model)
            .where(self.db_model.source_item_id.in_(source_item_ids))
        )
        result = await self.session.execute(stmt)
        return result.scalar_one()

    async def delete_by_id(self, id: str) -> None:
        """Delete source item by ID"""
        try:
            stmt = delete(self.db_model).where(self.db_model.id == id)
            await self.session.execute(stmt)
            await self.session.commit()
        except Exception as e:
            await self.session.rollback()
            raise CustomException(f"Error deleting source item: {str(e)}")
