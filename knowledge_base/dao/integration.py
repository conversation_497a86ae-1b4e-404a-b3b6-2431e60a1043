from typing import List, Optional

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from config.settings import loaded_config
from knowledge_base.models import Integration, IntegrationType
from utils.dao import BaseDao


class IntegrationDao(BaseDao):
    def __init__(self, session: AsyncSession):
        """Initializes the IntegrationDao with the Integration model.

        Args:
            session (AsyncSession): SQLAlchemy async session instance.
        """

        super().__init__(session=session, db_model=Integration)

    async def get_by_type(
        self, integration_type: IntegrationType
    ) -> Optional[Integration]:
        """Fetch a single integration by its type.

        Args:
            integration_type (IntegrationType): The integration type enum.

        Returns:
            Optional[Integration]: The matching integration or None.
        """

        stmt = select(self.db_model).where(
            self.db_model.type == integration_type,
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_all_integrations(self) -> List[dict]:
        """Returns all integrations excluding 'fynix_extension', with caching.

        Returns:
            List[dict]: A list of serialized integration objects.
        """

        # Check cache first - if already populated, return cached value
        if loaded_config.ingestion_integrations:
            return loaded_config.ingestion_integrations

        stmt = select(self.db_model).where(
            self.db_model.type.not_in([IntegrationType.fynix_extension])
        )
        result = await self.session.execute(stmt)
        integrations = result.scalars().all()

        # Populate cache with fresh data
        loaded_config.ingestion_integrations = [
            {
                "id": integration.id,
                "type": integration.type.name,
                "name": integration.name,
                "credentials_json": integration.credentials_json,
                "image_name": integration.image_name,
                "integration_help_url": integration.integration_help_url,
            }
            for integration in integrations
        ]

        return loaded_config.ingestion_integrations
