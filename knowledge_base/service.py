from datetime import datetime, timezone
from typing import Optional, List, Any, Coroutine

from clerk_integration.utils import UserData
from sqlalchemy.ext.asyncio import AsyncSession

from bigquery_integration.schemas import DatabaseSchema
from code_indexing.serializers import ResponseData
from config.logging import logger
from knowledge_base.constants import USER_DO_NOT_HAVE_ACCESS_TO_KB
from knowledge_base.dao import IntegrationDao, KnowledgeBaseDao, IngestionRunDao
from knowledge_base.factory import KnowledgeBaseFactory
from knowledge_base.locksmith_calls import (
    _call_grant_data_source_access,
    _call_revoke_data_source_access,
    _call_check_data_source_access,
    _call_get_accessible_datasources_by_user,
    _call_get_org_accessible_datasources,
    _call_get_team_accessible_datasources,
    _call_get_datasource_access_details,
    _call_revoke_specific_data_source_access
)
from knowledge_base.models import KBState
from knowledge_base.serializers import KnowledgeBaseRequest, GetVectorSearchRequest, LocalFileContext, ProviderContext, \
    GetKBStatusRequest, ShareKnowledgeBaseRequest, GetKbRequest, UpdateKB, GetKnowledgeBaseRequest, \
    GetKnowledgeBaseUpdate
from knowledge_base.strategies.factory import ProviderStrategyFactory
from utils.connection_handler import ConnectionHandler
from utils.exceptions import ApiException, CustomException
from utils.kafka.constants import KAFKA_SERVICE_CONFIG_MAPPING, KafkaServices, ETL_EXTERNAL_DATA
from utils.kafka.kafka_utils import almanac_partitioner
from utils.vector_db import ElasticSearchAdapter
from utils.vector_db.elastic_sql_adapter import ElasticsearchSQLAdapter


class KnowledgeBaseService:
    def __init__(self, session: AsyncSession):
        """Initializes the KnowledgeBaseService with DAOs and vector DB adapters.

        Args:
            session (AsyncSession): SQLAlchemy async session for database operations.
        """

        self.integration_dao = IntegrationDao(session)
        self.knowledge_base_dao = KnowledgeBaseDao(session)
        self.ingestion_run_dao = IngestionRunDao(session)
        self.vector_db = ElasticSearchAdapter()
        self.vectordb_for_sql = ElasticsearchSQLAdapter()


    async def start_loading(
            self,
            request: KnowledgeBaseRequest,
            connection_handler: ConnectionHandler,
            user_data: UserData
    ) -> ResponseData:
        """Initiates loading process based on the context (provider or local file).

        Args:
            request (KnowledgeBaseRequest): Request payload with context and metadata.
            connection_handler (ConnectionHandler): Connection handler for DB/session.
            user_data (UserData): Authenticated user info.

        Returns:
            ResponseData: Result of the loading operation.
        """

        logger.info("start_loading method invoked")

        if request.context == "add_context_through_provider":
            return await self._handle_provider_context(
                request.add_context_through_provider,
                request.team_id,
                connection_handler,
                user_data
            )

        elif request.context == "add_context_through_local_files":
            return await self._handle_local_file_context(request.add_context_through_local_files)

        else:
            logger.error(f"Unsupported context type: {request.context}")
            return ResponseData.model_construct(success=False, message=f"Unsupported context: {request.context}")


    async def _handle_provider_context(
            self,
            provider_context: ProviderContext,
            team_id: Optional[str],
            connection_handler: ConnectionHandler,
            user_data: UserData
    ) -> ResponseData | None:
        """Handles loading data into a knowledge base via a third-party provider integration.

        Args:
            provider_context (ProviderContext): Contains provider type, credentials, and KB metadata.
            team_id (Optional[str]): The team ID associated with the knowledge base, if any.
            connection_handler (ConnectionHandler): Handler for DB/session management.
            user_data (UserData): The user initiating the load request.

        Returns:
            ResponseData: Result of the provider context ingestion process.
        """

        if not provider_context:
            logger.error("Provider context data missing")
            return ResponseData.model_construct(success=False, message="Provider context missing")

        integration = await self.integration_dao.get_by_type(provider_context.provider.value)
        if not integration:
            logger.error(
                f"Integration not found for type: {provider_context.provider.value} and org_id: {user_data.orgId}")
            return ResponseData.model_construct(success=False, message="Integration not found")

        if not provider_context.kb_id:
            expected_fields = integration.credentials_json or {}
            missing_fields = [
                key for key in expected_fields.keys()
                if not provider_context.credentials or key not in provider_context.credentials
            ]
            if missing_fields:
                logger.error(f"Missing credential fields: {missing_fields}")
                return ResponseData.model_construct(success=False,
                                                    message=f"Missing credential fields: {missing_fields}")

        try:
            # Get the appropriate strategy for the provider type
            strategy = ProviderStrategyFactory.get_strategy(
                provider_context.provider.value,
                self.knowledge_base_dao,
                self.ingestion_run_dao
            )

            # Use the strategy to handle the provider
            return await strategy.start_loading(
                provider_context,
                team_id,
                connection_handler,
                user_data,
                integration
            )
        except ValueError:
            logger.error(f"Unsupported provider: {provider_context.provider}")
            return None

    async def _handle_local_file_context(self, local_context: LocalFileContext) -> ResponseData:
        """Processes local file ingestion requests for a knowledge base.

        Args:
            local_context (LocalFileContext): Metadata and file context provided by the user.

        Returns:
            ResponseData: Success acknowledgment or error if context is missing.
        """

        if not local_context:
            logger.error("Local file context missing")
            return ResponseData.model_construct(success=False, message="Local file context missing")

        logger.info(f"Received local file context: {local_context}")
        # Right now no action for local files
        return ResponseData.model_construct(
            success=True,
            message="Local file context received successfully. No action performed yet."
        )


    async def get_vector_search(self, request: GetVectorSearchRequest) -> ResponseData:
        """Performs vector search on the appropriate knowledge base depending on user request.

        Args:
            request (GetVectorSearchRequest): Search request payload.

        Returns:
            ResponseData: Search results or access error.
        """

        response_data = ResponseData.model_construct(success=False)
        logger.info(f"get_vector_search method invoked")

        try:
            for i in request.knowledge_base_id:
                has_access = await _call_check_data_source_access(i, request.user_id, request.org_id, request.team_id)
                if not has_access:
                    return ResponseData.model_construct(success=False,
                                                        message=USER_DO_NOT_HAVE_ACCESS_TO_KB)

            if request.type == "sql_data_analyst":
                await self.vectordb_for_sql.connect()
                result = await self.vectordb_for_sql.knn_similarity_search(
                    index_name="data_analyst_index",
                    request=request
                )
            else:
                await self.vector_db.connect()

                # Call search_and_fetch_content with the correct index name and data source string
                result = await self.vector_db.search_and_fetch_content(
                    request=request,
                    index_name="knowledge_base_index"
                )

            response_data.success = True
            response_data.data = result
            logger.info(f"get_vector_search method fetched")
            return response_data

        except Exception as e: # pylint: disable=broad-exception-caught
            logger.error(f"Search failed for request: {request.dict()}. Error: {str(e)}")
            raise ApiException(f"Search operation failed: {str(e)}")

        finally:
            await self.vector_db.close()
            await self.vectordb_for_sql.close()


    async def get_integrations(self) -> ResponseData:
        """Retrieves all available integrations for knowledge base ingestion.

        Returns:
            ResponseData: List of integration metadata.
        """

        logger.info(f"get_integrations method invoked")
        return ResponseData.model_construct(success=True, data=await self.integration_dao.get_all_integrations())


    async def get_indexing_status(self, user_data: UserData, request: GetKBStatusRequest) -> ResponseData:
        """Retrieves indexing status of accessible knowledge bases for a given user/team/org.

        Args:
            user_data (UserData): Current user's metadata.
            request (GetKBStatusRequest): Filtering parameters.

        Returns:
            ResponseData: Indexing status grouped by category.
        """

        try:
            logger.info("Fetching indexing status")
            data = {}

            # Step 1: If team_id is provided, filter only KBs belonging to that team
            if request.team_id:
                team_kbs = await _call_get_team_accessible_datasources(request.team_id)
                data = {
                    "personal": [],
                    "team": team_kbs,
                    "organization": []
                }

            else:
                data = await _call_get_accessible_datasources_by_user(user_data)

            # Step 2: If kb_ids are provided, filter all categories
            if request.kb_ids:
                for category in data:
                    data[category] = [kb_id for kb_id in data[category] if kb_id in request.kb_ids]

            # Step 3: Get indexing status
            status_list = await self.knowledge_base_dao.get_indexing_status(data, user_data.userId, user_data.orgId)

            return ResponseData.model_construct(
                success=True,
                data={"knowledge_bases": status_list}
            )

        except Exception as e: # pylint: disable=broad-exception-caught
            logger.error(f"Failed to fetch indexing status: {str(e)}")
            return ResponseData.model_construct(
                success=False,
                message=f"Failed to fetch indexing status: {str(e)}"
            )


    async def delete_knowledge_bases(self, kb_ids: List[int], connection_handler: ConnectionHandler,
                                     user_data: UserData) -> ResponseData:
        """
        Delete knowledge bases and their associated documents from both Elasticsearch and database.

        Args:
            kb_ids: List of knowledge base IDs to delete
            connection_handler: Database connection handler for transaction management

        Returns:
            ResponseData with success/failure status and message
        """
        try:
            logger.info("Starting deletion of knowledge bases", extra={"kb_ids": kb_ids})

            for kb_id in kb_ids:
                kb = await self.knowledge_base_dao.get_by_id(kb_id)

                if kb.created_by != user_data.userId:
                    logger.error("User does not have permission to delete this knowledge base", extra={
                        "kb_id": kb_id,
                        "user_id": user_data.userId,
                        "created_by": kb.created_by
                    })
                    raise CustomException(f"Do not have access to delete knowledge base: {kb.name}")

            # Delete from Elasticsearch first
            await self.vector_db.connect()
            try:

                for kb_id in kb_ids:
                    await _call_revoke_data_source_access(kb_id)

                    logger.debug("Deleting documents from Elasticsearch", extra={
                        "kb_id": kb_id,
                        "index": "knowledge_base_index"
                    })
                    result = await self.vector_db.delete_documents_by_kb_id(
                        index_name="knowledge_base_index",
                        kb_id=kb_id,
                        id_field="knowledge_base_id"
                    )
                    logger.info("Successfully deleted documents from Elasticsearch", extra={
                        "kb_id": kb_id,
                        "deleted_count": result["deleted"],
                        "failed_count": result["failed"]
                    })
            except Exception as e: # pylint: disable=broad-exception-caught
                logger.error("Failed to delete documents from Elasticsearch", extra={
                    "kb_ids": kb_ids,
                    "error": str(e)
                })
                raise
            finally:
                await self.vector_db.close()

            logger.debug("Deleting knowledge bases from database", extra={"kb_ids": kb_ids})
            await self.knowledge_base_dao.delete_knowledge_bases(kb_ids)

            await connection_handler.session.commit()

            logger.info("Successfully completed knowledge base deletion", extra={"kb_ids": kb_ids})
            return ResponseData.model_construct(
                success=True,
                data={
                    "message": f"Successfully deleted {len(kb_ids)} knowledge bases and their associated data",
                    "deleted_kb_ids": kb_ids
                }
            )

        except Exception as e: # pylint: disable=broad-exception-caught
            await connection_handler.session.rollback()
            logger.error("Failed to delete knowledge bases", extra={
                "kb_ids": kb_ids,
                "error": str(e),
                "error_type": type(e).__name__
            })
            raise CustomException(e.__str__())


    async def share_knowledge_bases(self, request: ShareKnowledgeBaseRequest, user_data: UserData) -> ResponseData:
        """Updates sharing permissions for specified knowledge bases.

        Args:
            request (ShareKnowledgeBaseRequest): Target users, teams, and orgs to share with.
            user_data (UserData): Current user performing the action.

        Returns:
            ResponseData: Sharing update result.
        """

        try:
            logger.info("Sharing knowledge bases", extra={"kb_ids": request.kb_ids, "team_id": request.team_id})

            for kb_id in request.kb_ids:
                kb = await self.knowledge_base_dao.get_by_id(kb_id)

                if kb.created_by != user_data.userId:
                    logger.error("User does not have permission to share this knowledge base", extra={
                        "kb_id": kb_id,
                        "user_id": user_data.userId,
                        "created_by": kb.created_by
                    })
                    raise CustomException(f"Do not have access to share knowledge base: {kb.name}")

            for kb_id in request.kb_ids:
                access = await _call_get_datasource_access_details(kb_id)
                current = {
                    "users": access.get("users", []),
                    "teams": access.get("teams", []),
                    "orgs": access.get("organizations", [])
                }

                requested = {
                    "users": request.user_id or [],
                    "teams": request.team_id or [],
                    "orgs": request.org_id or []
                }

                to_revoke = {
                    "users": [uid for uid in current["users"] if uid not in requested["users"]],
                    "teams": [tid for tid in current["teams"] if tid not in requested["teams"]],
                    "orgs": [oid for oid in current["orgs"] if oid not in requested["orgs"]],
                }

                to_grant = {
                    "users": [uid for uid in requested["users"] if uid not in current["users"]],
                    "teams": [tid for tid in requested["teams"] if tid not in current["teams"]],
                    "orgs": [oid for oid in requested["orgs"] if oid not in current["orgs"]],
                }

                logger.info(f"Access changes for KB {kb_id}", extra={
                    "current": current,
                    "to_revoke": to_revoke,
                    "to_grant": to_grant
                })

                # Revoke access
                for user_id in to_revoke["users"]:
                    if user_id:
                        await _call_revoke_specific_data_source_access(kb_id, user_id=user_id)
                        logger.info("Revoked access for user", extra={"kb_id": kb_id, "user_id": user_id})

                for team_id in to_revoke["teams"]:
                    if team_id:
                        await _call_revoke_specific_data_source_access(kb_id, team_id=team_id)
                        logger.info("Revoked access for team", extra={"kb_id": kb_id, "team_id": team_id})

                for org_id in to_revoke["orgs"]:
                    if org_id:
                        await _call_revoke_specific_data_source_access(kb_id, org_id=org_id)
                        logger.info("Revoked access for organization", extra={"kb_id": kb_id, "org_id": org_id})

                # Grant access
                for user_id in to_grant["users"]:
                    if user_id:
                        await _call_grant_data_source_access(
                            team_id=None,
                            knowledge_base_id=kb_id,
                            user_data=None,
                            user_id=user_id,
                            org_id=None
                        )
                        logger.info("Granted access to user", extra={"kb_id": kb_id, "user_id": user_id})

                for team_id in to_grant["teams"]:
                    if team_id:
                        await _call_grant_data_source_access(
                            team_id=team_id,
                            knowledge_base_id=kb_id,
                            user_data=None,
                            user_id=None,
                            org_id=None
                        )
                        logger.info("Granted access to team", extra={"kb_id": kb_id, "team_id": team_id})

                for org_id in to_grant["orgs"]:
                    if org_id:
                        await _call_grant_data_source_access(
                            team_id=None,
                            knowledge_base_id=kb_id,
                            user_data=None,
                            user_id=None,
                            org_id=org_id
                        )
                        logger.info("Granted access to organization", extra={"kb_id": kb_id, "org_id": org_id})

            return ResponseData.model_construct(
                success=True,
                data={"message": f"Successfully updated sharing for knowledge base"}
            )

        except Exception as e: # pylint: disable=broad-exception-caught
            # logger.error("Failed to share knowledge base", exc_info=True)
            raise CustomException(e.__str__())


    async def get_knowledge_bases(self, filter: GetKbRequest, user_data: UserData) -> ResponseData:
        """Retrieves and categorizes knowledge bases accessible to the user.

        Args:
            filter (GetKbRequest): Request filters like team/org.
            user_data (UserData): Current user's metadata.

        Returns:
            ResponseData: Categorized KB list and pagination info.
        """

        try:
            data = await _call_get_accessible_datasources_by_user(user_data)
            kb_ids = {
                "personal_kb_ids": data.get("personal", []),
                "team_kb_ids": data.get("team", []),
                "org_kb_ids": data.get("organization", [])
            }

            # If team_ids are provided, intersect with KBs accessible to those teams
            if filter.team_ids:
                scoped_team_kb_ids = set()
                for team_id in filter.team_ids:
                    team_kbs = await _call_get_team_accessible_datasources(team_id)
                    scoped_team_kb_ids.update(team_kbs)
                kb_ids["team_kb_ids"] = list(set(kb_ids["team_kb_ids"]) & scoped_team_kb_ids)

            # If org_ids are provided, intersect with KBs accessible to those orgs
            if filter.org_ids:
                scoped_org_kb_ids = set()
                for org_id in filter.org_ids:
                    org_kbs = await _call_get_org_accessible_datasources(org_id)
                    scoped_org_kb_ids.update(org_kbs)
                kb_ids["org_kb_ids"] = list(set(kb_ids["org_kb_ids"]) & scoped_org_kb_ids)

            # Get knowledge bases with pagination and filtering
            result = await self.knowledge_base_dao.get_knowledge_bases_with_pagination(kb_ids, filter)

            # Organize knowledge bases by category (personal, team, org)
            personal_kbs = [
                kb for kb in result["knowledge_bases"]
                if kb["id"] in kb_ids["personal_kb_ids"]
            ]
            team_kbs = [
                kb for kb in result["knowledge_bases"]
                if kb["id"] in kb_ids["team_kb_ids"]
            ]
            org_kbs = [
                kb for kb in result["knowledge_bases"]
                if kb["id"] in kb_ids["org_kb_ids"]
            ]

            # Structure the response data
            knowledge_bases_data = {
                "personal": personal_kbs,
                "team": team_kbs,
                "organization": org_kbs
            }

            return ResponseData.model_construct(
                success=True,
                data=knowledge_bases_data,
                pagination=result["pagination"]
            )

        except Exception as e: # pylint: disable=broad-exception-caught
            logger.error(f"Failed to fetch knowledge bases: {str(e)}")
            return ResponseData.model_construct(
                success=False,
                message=f"Failed to fetch knowledge bases: {str(e)}"
            )


    async def revoke_knowledge_base_access(self, request: ShareKnowledgeBaseRequest,
                                           user_data: UserData) -> ResponseData | None:
        """Revokes access to the given knowledge bases for users/teams/orgs.

        Args:
            request (ShareKnowledgeBaseRequest): Contains IDs of users, teams, and orgs to revoke access from.
            user_data (UserData): User performing the operation.

        Returns:
            ResponseData: Revocation result.
        """

        try:
            for kb_id in request.kb_ids:
                for user_id in request.user_id:
                    if user_id:
                        await _call_revoke_specific_data_source_access(kb_id, user_id=user_id)
                        logger.info("Revoked access for user", extra={"kb_id": kb_id, "user_id": user_id})

                for team_id in request.team_id:
                    if team_id:
                        await _call_revoke_specific_data_source_access(kb_id, team_id=team_id)
                        logger.info("Revoked access for team", extra={"kb_id": kb_id, "team_id": team_id})

                for org_id in request.org_id:
                    if org_id:
                        await _call_revoke_specific_data_source_access(kb_id, org_id=org_id)
                        logger.info("Revoked access for organization", extra={"kb_id": kb_id, "org_id": org_id})

                return ResponseData.model_construct(
                    success=True,
                    data={"message": "Successfully revoked access for knowledge base"}
                )
            return None
        except Exception as e: # pylint: disable=broad-exception-caught
            raise CustomException(e.__str__())


    async def is_url_public(self, url: str, provider: str, user_data: UserData) -> ResponseData:
        """Checks whether a given repository URL is public for the specified provider.

        Args:
            url (str): Repository URL.
            provider (str): Provider name (e.g., GitHub).
            user_data (UserData): User making the request.

        Returns:
            ResponseData: Boolean flag indicating public visibility.
        """

        try:
            # if url contain `.git` in end remove it
            if url.endswith(".git"):
                url = url[:-4]
            extractor = await KnowledgeBaseFactory.get_extractor(provider, repo_url=url)
            is_public = await extractor.is_project_public()
            return ResponseData.model_construct(
                success=True,
                data={"is_public": is_public}
            )
        except Exception as e: # pylint: disable=broad-exception-caught
            raise CustomException(e.__str__())


    async def update_knowledge_bases(self, request: UpdateKB, user_data: UserData) -> ResponseData:
        """Updates knowledge base metadata such as name, credentials, URL, or branch.

        Args:
            request (UpdateKB): Update request payload.
            user_data (UserData): User performing the update.

        Returns:
            ResponseData: Update result with new metadata.
        """

        try:
            kb = await self.knowledge_base_dao.get_by_id(request.kb_id)

            if kb.created_by != user_data.userId:
                logger.error("User does not have permission to update this knowledge base", extra={
                    "kb_id": kb.id,
                    "user_id": user_data.userId,
                    "created_by": kb.created_by
                })
                raise CustomException(f"Do not have access to update knowledge base: {kb.name}")

            extractor = await KnowledgeBaseFactory.get_extractor(
                kb.settings_json["provider"],
                repo_url=kb.settings_json["credentials"].get("url") if not request.url else request.url,
                pat=request.pat if request.pat and request.pat.strip() else kb.settings_json["credentials"].get("pat"),
                branch_name=kb.settings_json["credentials"].get(
                    "branch_name") if not request.branch_name else request.branch_name
            )

            is_public = await extractor.is_project_public()
            if not is_public:
                validation_result = await extractor.validate_credentials()
                if not validation_result["is_valid"]:
                    raise CustomException(validation_result["message"])

            await self.knowledge_base_dao.update_kb_name_and_pat_url_branch(
                name=request.name if request.name and request.name.strip() else None,
                kb_id=kb.id,
                pat=request.pat if request.pat and request.pat.strip() else None,
                url=request.url if request.url and request.url.strip() else None,
                branch_name=request.branch_name if request.branch_name and request.branch_name.strip() else None
            )
            # name status
            return ResponseData.model_construct(
                success=True,
                data={
                    "message": "Successfully updated knowledge base",
                    "name": kb.name,
                    "status": kb.state.value,
                    "update_token": True if kb.state.value == "token_error" else False
                }
            )
        except Exception as e: # pylint: disable=broad-exception-caught
            raise CustomException(e.__str__())


    async def load_selected_tables_for_sql(self, request: DatabaseSchema, connection_handler: ConnectionHandler,
                                           user_data: UserData):
        """Handles loading of selected tables into a SQL-type knowledge base via ingestion pipeline.

        Args:
            request (DatabaseSchema): Request with DB connection info and selected tables.
            connection_handler (ConnectionHandler): DB session/producer manager.
            user_data (UserData): User initiating the load process.

        Returns:
            ResponseData: Knowledge base creation or update result.
        """

        try:
            if not request.knowledge_base_id:
                credentials_json = request.credentials
                source_identifier = f"{credentials_json['project_id']}_{credentials_json['client_id']}_{request.database_name}"
                settings_json = {
                    "provider": request.provider.value,
                    "credentials": credentials_json,
                    "user_id": user_data.userId if user_data else None,
                    "org_id": user_data.orgId if user_data else None,
                    "database_name": request.database_name
                }

                integration = await self.integration_dao.get_by_type(request.provider.value)
                if not integration:
                    logger.error(
                        f"Integration not found for type: {request.provider.value} and org_id: {user_data.orgId}")
                    return ResponseData.model_construct(success=False, message="Integration not found")

                kb = await self.knowledge_base_dao.create_knowledge_base(
                    integration_id=integration.id,
                    name=request.kb_name,
                    description=request.description,
                    source_identifier=source_identifier,
                    kb_type="sql_data_analyst",
                    state=KBState.indexing,
                    team_id=request.team_id if request.team_id else None,
                    last_indexed_at=datetime.now(timezone.utc),
                    created_by=user_data.userId if user_data else None,
                    settings_json=settings_json,
                    org_id=user_data.orgId if user_data else None
                )
            else:
                kb = await self.knowledge_base_dao.get_by_id(kb_id=request.knowledge_base_id)
                if not kb:
                    raise ValueError(f"Knowledge base with id {request.knowledge_base_id} does not exist.")
                credentials_json = request.credentials or kb.settings_json.get('credentials', {})
                database_name = request.database_name or kb.settings_json.get('database_name', '')
                await self.knowledge_base_dao.update_kb_settings(
                    kb_id=kb.id,
                    kb_name=request.kb_name,
                    new_settings={
                        "credentials": credentials_json,
                        "database_name": database_name
                    },
                    new_state=KBState.updating,
                    commit=True
                )
                await self.knowledge_base_dao.update_kb_name_descp(
                    kb_id=int(kb.id),
                    name=request.kb_name,
                    description=request.description,
                    commit=True)
                logger.info(f"Updated credentials and state for knowledge base {kb.id}")

            settings_json = {
                "provider": request.provider.value,
                "credentials": credentials_json,
                "user_id": user_data.userId if user_data else None,
                "org_id": user_data.orgId if user_data else None,
                "database_name": request.database_name
            }

            ingestion_run = await self.ingestion_run_dao.create_ingestion_run(
                kb_id=kb.id,
                status="running"
            )
            if request.team_id:
                await _call_grant_data_source_access(request.team_id, knowledge_base_id=kb.id, user_data=None)
            await _call_grant_data_source_access(knowledge_base_id=kb.id, user_id=user_data.userId, user_data=None,
                                                 org_id=None, team_id=None)

            tables_as_dicts = [table.model_dump(by_alias=True, exclude_none=True) for table in request.tables]

            event = {
                "payload": {
                    "provider": request.provider.value,
                    "type": "sql_data_analyst",
                    "knowledge_base_id": str(kb.id),
                    "mode": "ADD",
                    "tables": tables_as_dicts,
                    "database_name": settings_json["database_name"],
                    "ingestion_run_id": ingestion_run.id
                }
            }
            logger.info(f"Event to be emitted: {event}")

            await connection_handler.event_emitter.emit(
                topics=KAFKA_SERVICE_CONFIG_MAPPING[KafkaServices.almanac][ETL_EXTERNAL_DATA]["topics"],
                partition_value=str(almanac_partitioner.partition()),
                event=event
            )

            kb_details = {
                "id": kb.id,
                "name": kb.name,
                "source_identifier": kb.source_identifier,
                "kb_type": kb.kb_type,
                "state": kb.state.value if kb.state else None
            }

            return ResponseData.model_construct(
                success=True,
                data={
                    "kb": kb_details
                }
            )

        except Exception as e: # pylint: disable=broad-exception-caught
            await connection_handler.session.rollback()
            raise CustomException(e.__str__())


    async def get_knowledge_base(self, kb_id: int, request: GetKnowledgeBaseRequest):
        """Fetches metadata for a single knowledge base by ID if access is permitted.

        Args:
            kb_id (int): Knowledge base ID.
            request (GetKnowledgeBaseRequest): Request context for access check.

        Returns:
            ResponseData: Knowledge base details or access denial.
        """

        try:
            has_access = await _call_check_data_source_access(kb_id, request.user_id, request.org_id, request.team_id)
            if not has_access:
                return ResponseData.model_construct(success=False,
                                                    message=USER_DO_NOT_HAVE_ACCESS_TO_KB)

            kb_details = await self.knowledge_base_dao.get_by_id(kb_id=kb_id)
            if not kb_details:
                return ResponseData.model_construct(success=False,
                                                    message="Knowledge base not found")

            # Serialize the knowledge base object based on the model
            serialized_kb = {
                "id": kb_details.id,
                "integration_id": kb_details.integration_id,
                "name": kb_details.name,
                "source_identifier": kb_details.source_identifier,
                "kb_type": kb_details.kb_type,
                "settings_json": kb_details.settings_json,
                "memory": kb_details.memory,
                "state": kb_details.state.value,
                "created_at": kb_details.created_at,
                "last_indexed_at": kb_details.last_indexed_at,
                "team_id": kb_details.team_id,
                "created_by": kb_details.created_by,
                "org_id": kb_details.org_id,
                "is_updatable": kb_details.is_updatable,
            }

            return ResponseData.model_construct(
                success=True,
                data=serialized_kb
            )

        except Exception as e: # pylint: disable=broad-exception-caught
            raise CustomException(e.__str__())


    async def get_knowledge_base_for_edit(self, kb_id: int, user_data: UserData):
        """Fetches full editable knowledge base configuration for UI or update prefill.

        Args:
            kb_id (int): Knowledge base ID.
            user_data (UserData): Requesting user's info.

        Returns:
            ResponseData: Serialized KB config for edit view.
        """

        try:
            has_access = await _call_check_data_source_access(kb_id, user_data.userId, user_data.orgId)
            if not has_access:
                return ResponseData.model_construct(success=False,
                                                    message=USER_DO_NOT_HAVE_ACCESS_TO_KB)

            kb_details = await self.knowledge_base_dao.get_kb_by_id_with_integration(kb_id=kb_id)
            if not kb_details:
                return ResponseData.model_construct(success=False,
                                                    message="Knowledge base not found")

            # Serialize the knowledge base object based on the model
            serialized_kb = GetKnowledgeBaseUpdate.model_validate(kb_details).model_dump()

            return ResponseData.model_construct(
                success=True,
                data=serialized_kb
            )

        except Exception as e: # pylint: disable=broad-exception-caught
            raise CustomException(e.__str__())


    async def get_file_content(self, request) -> ResponseData:
        """
        Get file content for a specific knowledge base and file path.

        Args:
            request: Request containing kb_id and file_path

        Returns:
            ResponseData containing the file content
        """
        response_data = ResponseData.model_construct(success=False)
        try:
            # Validate knowledge base exists
            kb = await self.knowledge_base_dao.get_by_id(request.kb_id)
            if not kb:
                response_data.message = f"Knowledge base with id {request.kb_id} not found"
                return response_data

            await self.vector_db.connect()

            result = await self.vector_db.get_file_content(
                index_name="knowledge_base_index",
                file_path=request.file_path,
                kb_id=request.kb_id
            )
            if not result:
                response_data.message = f"File content not found for path: {request.file_path}"
                return response_data

            response_data.success = True
            response_data.data = {"content": result}
            response_data.message = "File content retrieved successfully"
            return response_data

        except Exception as e: # pylint: disable=broad-exception-caught
            logger.error(
                f"Get file content failed for request: kb_id={request.kb_id}, file_path={request.file_path}. Error: {str(e)}")
            raise CustomException(f"Get file content operation failed: {str(e)}")

        finally:
            await self.vector_db.close()


    async def get_folder_structure(self, kb_ids: List[int], folder_path: Optional[str] = None) -> ResponseData:
        """
        Get folder structure for multiple knowledge bases.
        
        Args:
            kb_ids: List of knowledge base IDs
            folder_path: Optional folder path to get structure for
            
        Returns:
            ResponseData containing the folder structures for each KB
        """
        try:
            response_data = ResponseData.model_construct(success=False)
            
            # Get all knowledge bases
            kbs = []
            for kb_id in kb_ids:
                kb = await self.knowledge_base_dao.get_by_id(kb_id)
                if kb:
                    kbs.append(kb)
            
            if not kbs:
                return response_data
                
            # Get folder structure for each KB separately
            kb_structures = {}
            
            for kb in kbs:
                memory = kb.memory or {}
                folder_structure = memory.get("folder_structure", {})
                
                if folder_path:
                    # Normalize and split the path into parts
                    parts = folder_path.strip("/").split("/")
                    current = folder_structure.get("")
                    
                    for part in parts:
                        if isinstance(current, dict) and part in current:
                            current = current[part]
                        else:
                            logger.warning(f"Folder path '{folder_path}' not found in memory for KB {kb.id}.")
                            current = None
                            break
                            
                    if current:
                        kb_structures[kb.id] = current
                else:
                    # Return full structure if no path specified
                    root_structure = folder_structure
                    if root_structure:
                        kb_structures[kb.id] = root_structure
            
            response_data.success = True
            response_data.data = {"folder_structures": kb_structures}
            return response_data
            
        except Exception as e: # pylint: disable=broad-exception-caught
            logger.error(f"Get folder structure failed for KBs: {kb_ids}. Error: {str(e)}")
            raise CustomException(f"Get folder structure operation failed: {str(e)}")
