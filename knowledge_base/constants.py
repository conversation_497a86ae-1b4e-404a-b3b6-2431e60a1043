"""Constants and configuration values for the knowledge base module.

This module contains constant values used throughout the knowledge base system including:
- Validation error messages for repository URLs and credentials
- Application configuration constants
- Database relationship strategies
- Access control messages
"""
REPO_URL_REQUIRED = "Repository URL is required"
INVALID_REPO_URL_FORMAT = "Invalid repository URL format"
BRANCH_NAME_REQUIRED = "Branch name is required"
ALL_CRED_ARE_VALID = "All credentials are valid"
INVALID_FOLDER_CHARS_REGEX = r'[\\/*?:"<>|]'
APP_SLASH_JSON = "application/json"
CASCADE_STRATEGY = "all, delete-orphan"
USER_DO_NOT_HAVE_ACCESS_TO_KB = "User does not have access to this knowledge base"