"""FastAPI view functions for knowledge base operations.

This module contains endpoint handlers for the knowledge base API including:
- Creating and loading knowledge bases from various sources
- Vector search and content retrieval
- Knowledge base management (update, delete, share)
- Integration and status management
- File and folder structure operations

All views use dependency injection for database connections and user authentication.
"""
from fastapi import Body, Depends

from clerk_integration.utils import UserData
from bigquery_integration.schemas import DatabaseSchema
from knowledge_base.serializers import (
    KnowledgeBaseRequest,
    GetVectorSearchRequest,
    DeleteKnowledgeBasesRequest,
    GetKBStatusRequest,
    ShareKnowledgeBaseRequest,
    GetKbRequest,
    UpdateKB,
    GetKnowledgeBaseRequest,
    GetFolderStructureRequest,
    GetFileContentRequest,
    GetKbInternalRequest
)
from knowledge_base.service import KnowledgeBaseService
from utils.common import get_user_data_from_request
from utils.connection_handler import get_connection_handler_for_app


async def start_loading(
    request: KnowledgeBaseRequest = Body(...),
    connection_handler = Depends(get_connection_handler_for_app),
    user_data: UserData = Depends(get_user_data_from_request),
):
    """Initiates the loading process for a new knowledge base."""

    service = KnowledgeBaseService(connection_handler.session)
    return await service.start_loading(request, connection_handler, user_data)


async def get_vector_search(
    request: GetVectorSearchRequest = Body(...),
    connection_handler = Depends(get_connection_handler_for_app),
):
    """Performs a vector search within knowledge bases."""

    service = KnowledgeBaseService(connection_handler.session)
    return await service.get_vector_search(request)


async def get_integrations(
    user_data: UserData = Depends(get_user_data_from_request),
    connection_handler = Depends(get_connection_handler_for_app),
):
    """Fetches all available integration types."""

    service = KnowledgeBaseService(connection_handler.session)
    return await service.get_integrations()


async def get_indexing_status(
    request: GetKBStatusRequest = Body(...),
    user_data: UserData = Depends(get_user_data_from_request),
    connection_handler = Depends(get_connection_handler_for_app),
):
    """Returns indexing status for knowledge bases accessible to the user."""

    service = KnowledgeBaseService(connection_handler.session)
    return await service.get_indexing_status(user_data, request)


async def delete_knowledge_bases(
    request: DeleteKnowledgeBasesRequest = Body(...),
    connection_handler = Depends(get_connection_handler_for_app),
    user_data: UserData = Depends(get_user_data_from_request),
):
    """Deletes one or more knowledge bases and their associated data."""

    service = KnowledgeBaseService(connection_handler.session)
    return await service.delete_knowledge_bases(
        request.kb_ids, connection_handler, user_data
    )


async def share_knowledge_bases(
    request: ShareKnowledgeBaseRequest = Body(...),
    user_data: UserData = Depends(get_user_data_from_request),
    connection_handler = Depends(get_connection_handler_for_app),
):
    """Updates sharing settings for the specified knowledge bases."""

    service = KnowledgeBaseService(connection_handler.session)
    return await service.share_knowledge_bases(request, user_data)


async def get_knowledge_bases(
    filter: GetKbRequest = Body(...),
    user_data: UserData = Depends(get_user_data_from_request),
    connection_handler = Depends(get_connection_handler_for_app),
):
    """Returns a filtered, paginated list of knowledge bases accessible to the user."""

    service = KnowledgeBaseService(connection_handler.session)
    return await service.get_knowledge_bases(filter, user_data)


async def get_knowledge_bases_internal(
    filter: GetKbInternalRequest = Body(...),
    connection_handler = Depends(get_connection_handler_for_app),
):
    """Returns internal list of knowledge bases for a given user/org (admin use)."""

    user_id = filter.user_id
    org_id = filter.org_ids[0] if filter.org_ids else None
    user_data = UserData.model_construct(userId=user_id, orgId=org_id)
    service = KnowledgeBaseService(connection_handler.session)
    return await service.get_knowledge_bases(filter, user_data)


async def revoke_knowledge_base_access(
    request: ShareKnowledgeBaseRequest = Body(...),
    user_data: UserData = Depends(get_user_data_from_request),
    connection_handler = Depends(get_connection_handler_for_app),
):
    """Revokes user, team, or org access to specified knowledge bases."""

    service = KnowledgeBaseService(connection_handler.session)
    return await service.revoke_knowledge_base_access(request, user_data)


async def is_url_public(
    url: str,
    provider: str,
    user_data: UserData = Depends(get_user_data_from_request),
    connection_handler = Depends(get_connection_handler_for_app),
):
    """Checks if a repository URL is publicly accessible."""

    service = KnowledgeBaseService(connection_handler.session)
    return await service.is_url_public(url, provider, user_data)


async def update_kb(
    request: UpdateKB = Body(...),
    user_data: UserData = Depends(get_user_data_from_request),
    connection_handler = Depends(get_connection_handler_for_app),
):
    """Updates credentials or metadata for an existing knowledge base."""

    service = KnowledgeBaseService(connection_handler.session)
    return await service.update_knowledge_bases(request, user_data)


async def load_selected_tables_for_sql(
    request: DatabaseSchema = Body(...),
    connection_handler = Depends(get_connection_handler_for_app),
    user_data: UserData = Depends(get_user_data_from_request),
):
    """Triggers ingestion for selected SQL tables into the knowledge base."""

    service = KnowledgeBaseService(connection_handler.session)
    return await service.load_selected_tables_for_sql(
        request, connection_handler, user_data
    )


async def get_folder_structure(
    request: GetFolderStructureRequest = Body(...),
    connection_handler = Depends(get_connection_handler_for_app),
):
    """Retrieves the folder structure of a knowledge base."""

    service = KnowledgeBaseService(connection_handler.session)
    return await service.get_folder_structure(request.kb_ids, request.folder_path)


async def get_file_content(
    request: GetFileContentRequest = Body(...),
    connection_handler = Depends(get_connection_handler_for_app),
):
    """Returns the full content of a specific file in a knowledge base."""

    service = KnowledgeBaseService(connection_handler.session)
    return await service.get_file_content(request)


async def get_knowledge_base(
    kb_id: int,
    request: GetKnowledgeBaseRequest = Body(...),
    connection_handler = Depends(get_connection_handler_for_app),
):
    """Fetches details for a single knowledge base."""

    service = KnowledgeBaseService(connection_handler.session)
    return await service.get_knowledge_base(kb_id, request)


async def get_knowledge_base_for_edit(
    kb_id: int,
    user_data: UserData = Depends(get_user_data_from_request),
    connection_handler = Depends(get_connection_handler_for_app),
):
    """Returns knowledge base details for editing by the user."""

    service = KnowledgeBaseService(connection_handler.session)
    return await service.get_knowledge_base_for_edit(kb_id, user_data)
