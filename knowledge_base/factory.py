"""Factory class for creating knowledge base processing components.

This module provides a factory pattern implementation for creating instances of:
- Data extractors (GitHub, GitLab, Azure DevOps, Google Docs, Quip)
- Data data_transformers (Repository, Quip, Google Docs)
- Data loaders (Elasticsearch, Elasticsearch SQL)

The factory centralizes component creation and handles proper initialization
of async components where needed.
"""

from config.logging import logger
from knowledge_base.extractors import (
    DataExtractor,
    AzureDevopsRepoExtractor,
    GitRepoExtractor,
    GitHubRepoExtractor,
    GitLabRepoExtractor,
    GoogleDocsExtractor,
    QuipExtractor,
)
from knowledge_base.loaders import (
    DataLoader,
    GenericKnowledgeBaseLoader,
    SQLSchemaKnowledgeLoader,
)
from knowledge_base.data_transformers import (
    DataTransformer,
    RepoTransformer,
    QuipTransformer,
    GoogleDocsTransformer,
)


class KnowledgeBaseFactory:
    EXTRACTORS = {
        "azure_devops": AzureDevopsRepoExtractor,
        "google_docs": GoogleDocsExtractor,
        "gitlab": GitLabRepoExtractor,
        "github": GitHubRepoExtractor,
        "quip": QuipExtractor,
    }

    TRANSFORMERS = {
        "repo_transformer": RepoTransformer,
        "quip_transformer": QuipTransformer,
        "google_docs_transformer": GoogleDocsTransformer,
    }

    LOADERS = {
        "elasticsearch": GenericKnowledgeBaseLoader,
        "sql_schema_loader": SQLSchemaKnowledgeLoader,
    }

    @staticmethod
    async def get_extractor(name: str, **kwargs) -> DataExtractor:
        """Creates and returns an initialized extractor instance based on the given name.

        If the extractor is a Git-based extractor, its `async_init()` method is called.

        Args:
            name (str): Name of the extractor to create (e.g., 'github', 'quip').
            **kwargs: Arguments passed to the extractor's constructor.

        Returns:
            DataExtractor: An instance of the requested extractor.

        Raises:
            ValueError: If the extractor name is not recognized.
        """

        logger.info(f"Creating extractor for {name}")
        extractor_cls = KnowledgeBaseFactory.EXTRACTORS.get(name)
        if not extractor_cls:
            raise ValueError(f"Unknown extractor: {name}")
        extractor = extractor_cls(**kwargs)

        # Initialize async components if this is a GitRepoExtractor
        if isinstance(extractor, GitRepoExtractor):
            await extractor.async_init()

        return extractor

    @staticmethod
    async def get_transformer(name: str) -> DataTransformer:
        """Creates and returns a transformer instance based on the given name.

        Args:
            name (str): Name of the transformer to create (e.g., 'repo_transformer').

        Returns:
            DataTransformer: An instance of the requested transformer.

        Raises:
            ValueError: If the transformer name is not recognized.
        """

        logger.info(f"Creating transformer for {name}")
        transformer_cls = KnowledgeBaseFactory.TRANSFORMERS.get(name)
        if not transformer_cls:
            raise ValueError(f"Unknown transformer: {name}")
        return transformer_cls()

    @staticmethod
    async def get_loader(name: str, **kwargs) -> DataLoader:
        """Creates and returns a loader instance based on the given name.

        Args:
            name (str): Name of the loader to create (e.g., 'elasticsearch').
            **kwargs: Arguments passed to the loader's constructor.

        Returns:
            DataLoader: An instance of the requested loader.

        Raises:
            ValueError: If the loader name is not recognized.
        """

        logger.info(f"Creating loader for {name}")
        loader_cls = KnowledgeBaseFactory.LOADERS.get(name)
        if not loader_cls:
            raise ValueError(f"Unknown loader: {name}")
        return loader_cls(**kwargs)
