from abc import ABC, abstractmethod
from typing import Dict, Any


class DataLoader(ABC):
    def __init__(self, db_adapter):
        """Initializes the base DataLoader with a given database adapter.

        Args:
            db_adapter: The vector DB or SQL adapter used for loading data.
        """

        self.db_adapter = db_adapter

    @abstractmethod
    async def load(self, params: Dict[str, Any]):
        """Load data into the vector database.

        The params dict should contain index_name and other DB-specific fields.
        """
        pass
