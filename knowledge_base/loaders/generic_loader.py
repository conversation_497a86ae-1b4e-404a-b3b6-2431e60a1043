"""Data loader classes for loading processed content into vector databases.

This module provides loader implementations for different storage backends:
- DataLoader: Abstract base class for all loaders
- ElasticSearchLoader: Loads documents into Elasticsearch for general knowledge base operations
- ElasticSearchSQLLoader: Loads SQL schema and column metadata into Elasticsearch for data analysis

All loaders handle bulk operations, error handling, and proper database connection management.
"""

from typing import Any, Dict

from config.logging import logger
from knowledge_base.loaders.base import DataLoader


class GenericKnowledgeBaseLoader(DataLoader):
    KNOWLEDGE_BASE_INDEXING_SETTINGS = {
        "settings": {"number_of_shards": 5, "number_of_replicas": 2},
        "mappings": {
            "properties": {
                "id": {"type": "keyword"},
                "title": {
                    "type": "text",
                    "fields": {"keyword": {"type": "keyword", "ignore_above": 256}},
                },
                "content": {"type": "text", "index": True, "store": True},
                "embedding": {
                    "type": "dense_vector",
                    "dims": 1536,
                    "index": True,
                    "similarity": "cosine",
                },
                "chunk_references": {"type": "keyword"},
                "source": {"type": "keyword"},
                "is_chunked": {"type": "boolean"},
                "is_public": {"type": "boolean"},
                # LLM-generated description field
                "description": {"type": "text"},
                "knowledge_base_id": {"type": "keyword"},
                "metadata": {"type": "object", "dynamic": True},
            }
        },
    }

    async def load(self, params: Dict[str, Any]):
        """Loads data into Elasticsearch by creating an index and performing bulk insert in batches."""
        try:
            logger.info("Loading data into Elasticsearch...")
            await self.db_adapter.connect()

            index_name = params.get("index_name")
            documents = params.get("documents", [])
            settings = params.get("settings", self.KNOWLEDGE_BASE_INDEXING_SETTINGS)
            batch_size = params.get("batch_size", 50)

            if not index_name:
                logger.error("Missing 'index_name' in parameters.")
                raise ValueError("Missing 'index_name' in parameters.")

            # Function to split documents into smaller batches
            def chunks(lst, size):
                """Splits a list into smaller batches of a specified size.

                Args:
                    lst (list): The list to split.
                    size (int): The maximum size of each chunk.

                Yields:
                    list: Sliced sublists of the original list, each of size `size`
                          (except possibly the last).
                """

                for i in range(0, len(lst), size):
                    yield lst[i : i + size]

            total_success = 0
            all_failed = []

            # Loop through documents in batches
            for batch in chunks(documents, batch_size):
                # Construct bulk actions for the current batch
                actions = [
                    {"_index": index_name, "_source": doc, "_id": doc.get("id")}
                    for doc in batch
                ]

                # Perform bulk insert for the current batch
                result = await self.db_adapter.bulk_insert(
                    index_name, batch, settings, actions
                )

                # Accumulate successes and failures
                total_success += result.get("success", 0)
                all_failed.extend(result.get("failed", []))

                logger.info(
                    f"Batch loaded into Elasticsearch. Success: {result.get('success', 0)}, "
                    f"Failed: {len(result.get('failed', []))}"
                )

            final_result = {"success": total_success, "failed": all_failed}

            logger.info(f"Complete load result: {final_result}")
            return final_result

        except Exception as e:  # pylint: disable=broad-exception-caught
            logger.error(f"Error in load method: {e}")
            raise

        finally:
            await self.db_adapter.close()
