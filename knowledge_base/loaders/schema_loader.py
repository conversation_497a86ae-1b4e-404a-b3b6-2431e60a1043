from typing import Any, Dict

from config.logging import logger
from knowledge_base.loaders.base import DataLoader
from utils.vector_db.elastic_sql_adapter import ElasticsearchSQLAdapter


class SQLSchemaKnowledgeLoader(DataLoader):

    async def load(self, params: Dict[str, Any]):
        """Background task to process database schema and bulk insert into Elasticsearch."""
        try:
            await self.db_adapter.create_index(params["index_name"])
            delete_result = await self.db_adapter.delete_documents_by_kb_id(
                params["index_name"],
                params["knowledge_base_id"],
                id_field="knowledge_base_id",
            )
            delete_failures = delete_result.get("failures", [])
            if delete_failures:
                logger.error(f"Failed to update documents: {delete_failures}")

            logger.info(
                f"Starting bulk indexing for database: {params['database_name']}, "
                f"knowledge_base_id: {params['knowledge_base_id']} into index: {params['index_name']}"
            )

            documents_to_index = []
            processed_columns = 0
            failed_embeddings = 0

            for table in params["tables"]:
                table_desc = table.get("table_description", "")
                for column in table["columns"]:
                    try:
                        embedding = []  # Default to empty list if no description
                        column_desc = column.get("description")
                        column_name = column["column_name"]

                        # Build context-aware text for embedding
                        context_parts = []
                        if table_desc:
                            context_parts.append(f"Table Description: {table_desc}")
                        context_parts.append(f"Column: {column_name}")
                        if column_desc and column_desc.strip():
                            context_parts.append(f"Column Description: {column_desc}")

                        text_to_embed = " | ".join(context_parts)
                        embedding = await self.db_adapter.embedding_generator.generate_embedding(
                            text_to_embed
                        )

                        # Ensure embedding dimensions match the mapping
                        mapping = (
                            ElasticsearchSQLAdapter.INDEXING_DEFAULT_MAPPING_FOR_SQL
                        )
                        expected_dims = mapping["mappings"]["properties"][
                            "column_description_embedding"
                        ]["dims"]
                        if embedding and len(embedding) != expected_dims:
                            logger.error(
                                f"Embedding for column {column['column_name']} has {len(embedding)} dims, "
                                f"expected {expected_dims}. Skipping this column's embedding."
                            )
                            # Handle embedding dimension mismatch:
                            # If description exists but embedding fails or has wrong dimensions,
                            # we mark it as failed. Common approaches include:
                            # - Store a zero vector of correct dimensions
                            # - Skip the embedding field entirely
                            # - Raise an error for production systems
                            # For production safety, we'll use an empty list and count as failed
                            # to maintain backward compatibility while tracking the issue
                            if column_desc and column_desc.strip():
                                failed_embeddings += 1
                            embedding = []  # Reset to empty if problematic

                        doc = {
                            "database_name": params["database_name"],
                            "table_name": table["table_name"],
                            "table_description": table.get("table_description"),
                            "column_name": column["column_name"],
                            "column_description": column_desc,
                            "column_description_embedding": (
                                embedding if embedding else None
                            ),
                            "data_type": column["data_type"],
                            "knowledge_base_id": params["knowledge_base_id"],
                        }
                        # Filter out None values for embedding to avoid issues with Elasticsearch
                        # if None is not acceptable for dense_vector
                        if doc["column_description_embedding"] is None:
                            del doc["column_description_embedding"]

                        documents_to_index.append(doc)
                        processed_columns += 1
                    except Exception as e:  # pylint: disable=broad-exception-caught
                        failed_embeddings += 1
                        logger.error(
                            f"Failed to process column {column['column_name']} "
                            f"in table {table['table_name']}: {e}"
                        )

            if documents_to_index:
                logger.info(
                    f"Attempting to bulk insert {len(documents_to_index)} documents."
                )
                result = await self.db_adapter.bulk_insert_docs(
                    params["index_name"], documents_to_index
                )
                logger.info(f"Bulk insert result: {result}")
                if result.get("failed", 0) > 0:
                    logger.error(
                        f"Failed to insert {result['failed']} documents. "
                        f"Errors: {result.get('errors')}"
                    )
            else:
                logger.info("No documents prepared for indexing.")

            return {
                "processed_columns": processed_columns,
                "indexed_documents": len(documents_to_index) - failed_embeddings,
                "failed": failed_embeddings,
            }
        except Exception as e:  # pylint: disable=broad-exception-caught
            logger.error(f"Error in load method: {e}")
            raise
        finally:
            await self.db_adapter.close()
