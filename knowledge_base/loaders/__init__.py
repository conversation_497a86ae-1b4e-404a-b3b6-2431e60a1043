"""
Data loaders for knowledge base documents.

This module provides loader classes for retrieving data from various sources
and preparing them for processing by extractors and transformers.
"""

from .base import DataLoader
from .generic_loader import GenericKnowledgeBaseLoader
from .schema_loader import SQLSchemaKnowledgeLoader

__all__ = [
    "DataLoader",
    "GenericKnowledgeBaseLoader",
    "SQLSchemaKnowledgeLoader",
]
