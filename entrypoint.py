"""Application entrypoint that initializes different modes: server, consumer, or cron."""
from config.settings import loaded_config
import newrelic.agent

# Initialize New Relic programmatically without newrelic.ini
if (
    hasattr(loaded_config, 'new_relic_license_key') and loaded_config.new_relic_license_key and
    hasattr(loaded_config, 'new_relic_monitor_mode') and loaded_config.new_relic_monitor_mode
):
    # Initialize New Relic with programmatic config
    newrelic.agent.initialize()
    print("New Relic initialized programmatically for app")

if loaded_config.mode == "server":
    from app.main import main as server_main

    if __name__ == "__main__":
        server_main()
elif loaded_config.mode == "consumer":
    import asyncio
    from utils.kafka.consumer.consumer import main as consumer_main

    print("Starting Consumer")
    asyncio.run(consumer_main())
elif loaded_config.mode == "cron":
    from knowledge_base.cron import crons  # pylint: disable=unused-import
else:
    print('MODE not available')
