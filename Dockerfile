FROM ubuntu:22.04

ENV LC_ALL C.UTF-8
ENV LANG C.UTF-8


RUN apt-get update -y \
    && apt-get upgrade -y \
    && apt-get dist-upgrade -y \
    && apt-get install -y --no-install-recommends \
    wget \
    python3-pip \
    python3-setuptools \
    && cd /usr/local/bin \
    && pip3 --no-cache-dir install --upgrade pip \
    && DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends \
    curl \
    openssh-server \
    git \
    librdkafka-dev \
    bash \
    g++ \
    build-essential && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

RUN apt-get remove --purge -y linux-libc-dev


WORKDIR /srv/almanac

COPY ./requirements/requirements.txt .

# Adding Gitlab Repo Keyspip
ARG AZURE_PRIVATE_TOKEN_BASE64

RUN curl -s -H "Authorization: Basic $AZURE_PRIVATE_TOKEN_BASE64" "https://dev.azure.com/GoFynd/Infrastructure/_apis/git/repositories/kube-infrastructure/items?path=/pipeline/setup_key.sh&versionDescriptor.versionType=branch&versionDescriptor.version=master" 2>&1 | sh
RUN ssh-keyscan -t rsa gitlab.com >> ~/.ssh/known_hosts



RUN pip3 install --upgrade pip setuptools wheel && pip3 install -r ./requirements.txt

COPY . .
RUN git rev-parse HEAD > gitsha && rm -rf .git


WORKDIR /srv/almanac
RUN mkdir /srv/almanac/repos

COPY ./ci-test.sh .
EXPOSE 80
RUN chmod +x ci-test.sh

# Pre download required models
RUN python3 -m spacy download en_core_web_md

ENTRYPOINT ["python3", "entrypoint.py"]