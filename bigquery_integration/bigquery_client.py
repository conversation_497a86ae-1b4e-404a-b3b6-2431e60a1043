"""BigQuery client implementation for database operations."""
import asyncio

from google.cloud import bigquery
from google.oauth2 import service_account
import google.auth.exceptions

from config.logging import logger
from utils.base_database_client import DatabaseClientStrategy


class BigQueryClientStrategy(DatabaseClientStrategy):
    """
    A client to interact with Google BigQuery, allowing listing of datasets, tables, views,
    and fetching table schemas.
    """

    def __init__(self, credentials: dict):
        """
        Initializes the BigQueryClient with service account credentials.

        Args:
            credentials (dict): A dictionary containing the Google Cloud service account JSON key.
                                Must include 'project_id'.

        Raises:
            ValueError: If credentials are not provided, not a dictionary, or missing 'project_id'.
            google.auth.exceptions.DefaultCredentialsError: If credentials are invalid.
            Exception: For other potential errors during client initialization.
        """
        if not credentials or not isinstance(credentials, dict):
            raise ValueError("BigQuery credentials are required and must be a dictionary.")

        self.project_id = credentials.get("project_id")
        if not self.project_id:
            raise ValueError("The 'project_id' is missing from the BigQuery credentials.")

        super().__init__(credentials)

        try:
            gcp_credentials = service_account.Credentials.from_service_account_info(
                self.credentials
            )
            self.client = bigquery.Client(credentials=gcp_credentials, project=self.project_id)
            logger.info(
                f"Successfully initialized BigQuery client for project: {self.client.project}"
            )
        except google.auth.exceptions.RefreshError as e:
            logger.error(
                f"Error: Invalid or expired BigQuery credentials. Details: {e}"
            )
            raise google.auth.exceptions.DefaultCredentialsError(
                f"Invalid or expired BigQuery credentials: {e}"
            ) from e
        except Exception as e:
            logger.error(f"An unexpected error occurred during BigQuery client initialization: {e}")
            raise

    async def validate_credentials(self):
        """Validates the provided BigQuery credentials by attempting to list datasets.

        Returns:
            bool: True if credentials are valid, False otherwise.
        """

        try:
            self.client.list_datasets(max_results=1)
            return True
        except Exception as e:  # pylint: disable=broad-exception-caught
            logger.error(
                f"An unexpected error occurred during BigQuery client credentials validation: {e}"
            )
            return False


    def list_all_datasets_tables_and_views(self):
        """
        Lists all datasets in the project and, for each dataset, lists all tables and views.

        Returns:
            dict: A dictionary where keys are dataset IDs (strings) and values are dictionaries
                  containing two lists: 'tables' and 'views'.
                  Example:
                  {
                      'dataset_1': {
                          'tables': ['table_a', 'table_b'],
                          'views': ['view_x']
                      },
                      'dataset_2': {
                          'tables': ['table_c'],
                          'views': []
                      }
                  }
        Raises:
            RuntimeError: If the client was not initialized successfully.
        """
        if not self.client:
            raise RuntimeError(
                "BigQuery client is not initialized. Please check initialization errors."
            )

        all_data = {}
        try:
            datasets = list(self.client.list_datasets())
            if not datasets:
                return all_data

            for dataset_item in datasets:
                dataset_id = dataset_item.dataset_id
                all_data[dataset_id] = {'tables': [], 'views': []}

                tables_and_views = list(self.client.list_tables(dataset_id))

                if not tables_and_views:
                    continue

                for item in tables_and_views:
                    if item.table_type == "TABLE":
                        all_data[dataset_id]['tables'].append(item.table_id)
                    elif item.table_type == "VIEW":
                        all_data[dataset_id]['views'].append(item.table_id)

            return all_data

        except google.auth.exceptions.GoogleAuthError as e:
            raise e
        except Exception as e:
            logger.error(f"error occurred in list_all_datasets_tables_and_views: {e}")
            raise e


    async def get_table_schemas(self, database_or_schema_name: str):
        """
        Retrieves all tables across a dataset and returns their schemas in the desired format.
        Uses INFORMATION_SCHEMA for efficient schema fetch.
        """
        if not self.client:
            raise RuntimeError(
                "BigQuery client is not initialized. Please check initialization errors."
            )

        if not database_or_schema_name:
            return []

        query = f"""
        SELECT table_name, column_name, data_type
        FROM `{database_or_schema_name}.INFORMATION_SCHEMA.COLUMNS`
        """

        def fetch():
            """Executes the BigQuery query and returns the result iterator."""
            return self.client.query(query).result()

        try:
            result = await asyncio.to_thread(fetch)

            schema_map = {}
            for row in result:
                full_table_name = f"{database_or_schema_name}.{row.table_name}"
                if full_table_name not in schema_map:
                    schema_map[full_table_name] = {}
                schema_map[full_table_name][row.column_name] = row.data_type

            return [{table_name: columns} for table_name, columns in schema_map.items()]
        except google.auth.exceptions.GoogleAuthError as e:
            raise e
        except Exception as e:
            logger.error(f"error occurred in get_table_schema: {e}")
            raise e
