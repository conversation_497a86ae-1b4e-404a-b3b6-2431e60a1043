"""
Pydantic schemas for representing BigQuery integration metadata, 
including columns, tables, and database schema.
"""
from typing import List, Optional, Dict, Any, Union

from pydantic import BaseModel

from knowledge_base.models import IntegrationType


class ColumnMetadata(BaseModel):
    """Schema representing metadata for a single database column."""
    column_name: str
    description: Optional[str] = None
    data_type: str


class TableMetadata(BaseModel):
    """Schema representing metadata for a database table, including its columns."""
    table_name: str
    table_description: Optional[str] = None
    columns: List[ColumnMetadata]


class DatabaseSchema(BaseModel):
    """Schema representing the structure of a database, including tables and credentials."""
    provider: IntegrationType
    database_name: str
    tables: List[TableMetadata]
    knowledge_base_id: Optional[Union[int, str]] = None
    description: Optional[str] = None
    credentials: Optional[Dict[str, Any]] = None
    team_id: Optional[str] = None
    kb_name: str
