"""Database connection management for SQLAlchemy async engines and session handling."""
from asyncio import current_task

from sqlalchemy.ext.asyncio import create_async_engine, async_scoped_session, AsyncSession
from sqlalchemy.orm import sessionmaker


class ConnectionManager:
    """Manages database connections and provides async session
    factories for SQLAlchemy operations."""

    def __init__(self, db_url, db_echo, redis_url=None):  # pylint: disable=unused-argument
        """Initializes the ConnectionManager with database configuration and sets up the async
        engine and session factory.

        Args:
            db_url (str): Database URL for establishing the connection.
            db_echo (bool): Enables SQL query logging if True.
            redis_url (optional): Reserved for future Redis integration (not used currently).
        """

        self.db_url = db_url
        self.db_echo = db_echo
        self._db_engine, self._db_session_factory = self._setup_db()


    def get_session_factory(self):
        """Returns the async session factory for creating database sessions.

        Returns:
            async_scoped_session: A scoped session factory tied to the current async task.
        """

        return self._db_session_factory


    def _setup_db(self):
        """Creates and configures the SQLAlchemy async engine and scoped session factory.

        Returns:
            Tuple[AsyncEngine, async_scoped_session]: The initialized engine and session factory.
        """

        engine = create_async_engine(str(self.db_url), echo=self.db_echo)
        session_factory = async_scoped_session(
            sessionmaker(
                engine,
                expire_on_commit=False,
                class_=AsyncSession,
            ),
            scopefunc=current_task,
        )
        return engine, session_factory

    async def close_connections(self):
        """Disposes the SQLAlchemy async engine, closing all pooled database connections."""

        await self._db_engine.dispose()
