"""Vector database management module with adapters and
context managers for database operations."""
from .base import VectorDBAdapter
from .elastic_adapter import ElasticSearchAdapter

class VectorDBContext:
    """Context manager for vector database operations with
    automatic connection lifecycle management."""
    
    def __init__(self, adapter: VectorDBAdapter):
        """Initializes the VectorDBContext with the given adapter.

        Args:
            adapter (VectorDBAdapter): The vector database adapter to manage.
        """

        self.adapter = adapter
    
    async def __aenter__(self) -> VectorDBAdapter:
        """Asynchronously enters the context, connecting the vector
        database adapter.

        Returns:
            VectorDBAdapter: The connected vector database adapter.
        """

        await self.adapter.connect()
        return self.adapter
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Asynchronously exits the context, closing the vector database
        adapter connection.

        Args:
            exc_type: The type of exception (if any).
            exc_val: The exception instance (if any).
            exc_tb: The traceback object (if any).
        """

        await self.adapter.close()

def create_vector_db() -> VectorDBAdapter:
    """Factory method to create and return the default vector database adapter.

    Returns:
        VectorDBAdapter: An instance of ElasticSearchAdapter.
    """

    return ElasticSearchAdapter()
