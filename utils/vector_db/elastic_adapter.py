"""Elasticsearch adapter implementation for vector database operations and search functionality."""
import asyncio
import os  # pylint: disable=unused-import
from typing import Any, Dict, List, Optional

from elasticsearch import AsyncElasticsearch
from elasticsearch.helpers import async_bulk

from code_indexing.serializers import VectorSearchRequest, KeywordSearchRequest
from knowledge_base.serializers import GetVectorSearchRequest
from config.settings import loaded_config

from .base import VectorDBAdapter
from .embeddings import EmbeddingGenerator
from .exceptions import ConnectionError, SearchError  # pylint: disable=redefined-builtin
from ..exceptions import CustomException


class ElasticSearchAdapter(VectorDBAdapter):
    """Elasticsearch adapter for vector search operations."""

    TITLE_DOT_KEYWORD = "title.keyword"

    def __init__(self):
        """Initializes the Elasticsearch adapter and sets up the embedding generator."""

        self.client = None
        self.embedding_generator = EmbeddingGenerator(api_key=loaded_config.openai_gpt4o_api_key)

    async def connect(self, retries=3, delay=2) -> None:
        """Connect to Elasticsearch with retries."""
        for attempt in range(retries):
            try:
                self.client = AsyncElasticsearch(hosts=[loaded_config.elastic_search_url])
                await self.client.info()
                return
            except Exception as e:  # pylint: disable=broad-exception-caught
                if attempt < retries - 1:
                    await asyncio.sleep(delay * (2 ** attempt))  # Exponential backoff
                else:
                    # pylint: disable=raise-missing-from
                    raise ConnectionError(f"Failed to connect to Elasticsearch: {str(e)}")

    async def close(self) -> None:
        """Closes the Elasticsearch client connection if initialized."""

        if self.client:
            await self.client.close()

    async def create_index(self, index_name: str,
        settings: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Creates an index in Elasticsearch if it does not already exist.

        Args:
            index_name (str): The name of the index to create.
            settings (Optional[Dict[str, Any]]): Optional index configuration settings.

        Returns:
            Dict[str, Any]: Elasticsearch response indicating success or index existence.

        Raises:
            SearchError: If the index creation fails.
        """

        try:
            if await self.client.indices.exists(index=index_name):
                print(f"⚠️ Index '{index_name}' already exists. Skipping creation.")
                return {"acknowledged": False, "message": "Index already exists"}

            # Use provided settings or fallback to default mapping if available
            index_settings = settings or getattr(self, 'INDEXING_DEFAULT_MAPPING', {})
            return await self.client.indices.create(index=index_name, body=index_settings)
        except Exception as e:
            # pylint: disable=raise-missing-from
            raise SearchError(f"Failed to create index: {str(e)}")

    async def keyword_search_source_code(
        self, request: KeywordSearchRequest, index: str, kb_id: int = None
    ) -> Dict[str, Any]:
        """Performs keyword-based search over source code content using filters and match queries.

        Args:
            request (KeywordSearchRequest): Contains search keywords, paths, and workspace scope.
            index (str): Name of the Elasticsearch index.
            kb_id (int, optional): Knowledge base ID filter.

        Returns:
            Dict[str, Any]: Raw search results from Elasticsearch.

        Raises:
            SearchError: If search execution fails.
        """

        try:
            if not request.entire_workspace and not request.file_paths and not request.folder_paths:
                return {"hits": {"total": 0, "hits": []}}

            should_clauses = [{"match": {"content": keyword}} for keyword in request.keywords]

            filters = []
            if kb_id is not None:
                filters.append({"term": {"knowledge_base_id": kb_id}})

            if request.file_paths or request.folder_paths:
                path_filters = []

                if request.file_paths:
                    # Use title.keyword if title is text with keyword subfield
                    file_conditions = [
                        {"term": {self.TITLE_DOT_KEYWORD: file_path}}
                        for file_path in request.file_paths
                    ]
                    path_filters.append({"bool": {"should": file_conditions}})

                if request.folder_paths:
                    folder_conditions = [
                        {"wildcard": {self.TITLE_DOT_KEYWORD: f"{folder_path.rstrip('/')}/*"}}
                        for folder_path in request.folder_paths
                    ]
                    path_filters.append({"bool": {"should": folder_conditions}})

                filters.append({
                    "bool": {
                        "should": path_filters,
                        "minimum_should_match": 1
                    }
                })

            query = {
                "query": {
                    "bool": {
                        "should": should_clauses,
                        "minimum_should_match": 1,
                        "filter": filters
                    }
                },
                "size": request.max_results
            }

            return await self.client.search(index=index, body=query)

        except Exception as e:
            # pylint: disable=raise-missing-from
            raise SearchError(f"Failed to perform keyword search: {str(e)}")

    async def knn_similarity_search(
            self, request: VectorSearchRequest, index: str,  kb_id: int = None
    ) -> Dict[str, Any]:
        """Perform a KNN similarity search in Elasticsearch,
        filtering by knowledge_base_id and optional file/folder paths."""
        try:
            # Return early if nothing to search
            if (not request.entire_workspace and not request.file_paths and 
                not request.folder_paths):
                return {"hits": {"total": 0, "hits": []}}

            # Generate embedding vector
            query_vector = await self.embedding_generator.generate_embedding(request.query)

            # Set num_candidates based on max_results
            if request.max_results <= 10:
                num_candidates = request.max_results * 10
            elif request.max_results <= 25:
                num_candidates = request.max_results * 5
            else:
                num_candidates = max(100, request.max_results * 3)

            # Filter clause
            filters = []
            if kb_id is not None:
                filters.append({"term": {"knowledge_base_id": kb_id}})

            # Path-based filters
            if request.file_paths or request.folder_paths:
                path_filters = []

                if request.file_paths:
                    file_conditions = [
                        {"term": {self.TITLE_DOT_KEYWORD: file_path}} 
                        for file_path in request.file_paths
                    ]
                    path_filters.append({"bool": {"should": file_conditions}})

                if request.folder_paths:
                    folder_conditions = [
                        {"wildcard": {self.TITLE_DOT_KEYWORD: f"{folder_path.rstrip('/')}/*"}}
                        for folder_path in request.folder_paths
                    ]
                    path_filters.append({"bool": {"should": folder_conditions}})

                filters.append({
                    "bool": {
                        "should": path_filters,
                        "minimum_should_match": 1
                    }
                })

            # Construct KNN query
            body = {
                "size": request.max_results,
                "knn": {
                    "field": "embedding",
                    "query_vector": query_vector,
                    "k": request.max_results,
                    "num_candidates": num_candidates,
                    "filter": filters  # ← same structure as keyword search
                }
            }

            # Optional: log query
            import json  # pylint: disable=import-outside-toplevel
            print("Vector ES Query:\n", json.dumps(body, indent=2))

            return await self.client.search(index=index, body=body)

        except Exception as e:
            # pylint: disable=raise-missing-from
            raise SearchError(f"Failed to perform KNN similarity search: {str(e)}")

    async def bulk_insert(
        self, index_name: str, documents: list[dict], mapping: dict, actions: list
    ):
        """Ensures index exists and inserts multiple
        documents into Elasticsearch using async bulk API."""

        await self.create_index(index_name=index_name, settings=mapping)

        # Perform bulk indexing asynchronously
        success, failed = await async_bulk(self.client, actions, raise_on_error=False)
        print(f"Bulk insert completed: {success} succeeded, {failed} failed")

        # Extract failed _id values
        failed_ids = [failure['index']['_id'] for failure in failed if 'index' in failure]
        print(f"Failed _ids: {failed_ids}")

        return {"success": success, "failed": failed_ids}

    async def get_file_content(
        self, index_name: str, file_path: str, kb_id: int = None
    ) -> Optional[str]:
        """Fetches and reconstructs full content of a file by joining its document chunks in order.

        Args:
            index_name (str): Elasticsearch index name.
            file_path (str): File path to search for.
            kb_id (int, optional): Knowledge base ID to filter documents.

        Returns:
            Optional[str]: The full reconstructed file content or None if not found.
        """

        try:
            # Step 1: Find a document matching the path and kb_id to get chunk_references
            initial_query = {
                "query": {
                    "bool": {
                        "must": [
                            {"term": {self.TITLE_DOT_KEYWORD: file_path}},
                            {"term": {"knowledge_base_id": kb_id}}
                        ]
                    }
                },
                "_source": ["chunk_references"]
            }

            initial_response = await self.client.search(index=index_name, body=initial_query)

            hits = initial_response["hits"]["hits"]
            if not hits:
                return None

            chunk_references = hits[0]["_source"].get("chunk_references", [])
            if not chunk_references:
                return None

            # Extract just the doc IDs (before the "#")
            # doc_ids = [ref.split("#")[0] for ref in chunk_references]

            # Step 2: Fetch all documents by their IDs
            mget_body = {
                "ids": chunk_references
            }
            mget_response = await self.client.mget(index=index_name, body=mget_body)

            # Build a mapping from ID to content
            id_to_content = {
                doc["_id"]: doc["_source"].get("content", "")
                for doc in mget_response["docs"]
                if doc.get("found")
            }

            # Step 3: Concatenate content in the order of `chunk_references`
            concatenated_content = "\n".join(
                id_to_content.get(doc_id, "")
                for doc_id in chunk_references
            )

            return concatenated_content.strip()

        except Exception as e:  # pylint: disable=broad-exception-caught
            print(f"Error retrieving file content: {e}")
            return None

    # pylint: disable=too-many-locals
    async def search_and_fetch_content(
        self, request: GetVectorSearchRequest, index_name: str
    ) -> List[dict]:
        """
        Perform a KNN similarity search and return concatenated chunks (max 3) for each result,
        including chunk_num-1 and chunk_num+1 if available. Filters by knowledge_base_id.
        """
        try:
            # Generate query embedding
            query_vector = await self.embedding_generator.generate_embedding(request.query)

            if len(query_vector) != 1536:
                raise ValueError(
                    f"Query vector has incorrect dimensions: {len(query_vector)} (Expected: 1536)"
                )

            num_candidates = max(request.top_answer_count * 5, 100)

            # KNN Search query
            knn_query = {
                "size": request.top_answer_count,
                "knn": {
                    "field": "embedding",
                    "query_vector": query_vector,
                    "k": request.top_answer_count,
                    "num_candidates": num_candidates,
                    "filter": [
                        {"terms": {"knowledge_base_id": request.knowledge_base_id}}
                    ]
                },
                "_source": ["id", "content", "is_chunked", "title"]
            }

            response = await self.client.search(index=index_name, body=knn_query)
            hits = response["hits"]["hits"]

            results = []

            for hit in hits:
                doc = hit["_source"]
                doc_id = doc.get("id")
                is_chunked = doc.get("is_chunked", False)
                title = doc.get("title")

                if not is_chunked:
                    results.append({
                        "content": doc.get("content"),
                        "_score": hit["_score"],
                        "title": title
                    })
                    continue

                # Process chunked document
                if "#" not in doc_id:
                    continue  # skip invalid format

                logical_id, chunk_str = doc_id.rsplit("#", 1)

                try:
                    chunk_num = int(chunk_str)
                except ValueError:
                    continue  # skip if chunk number is not integer

                # Attempt to fetch chunk-1 and chunk+1
                adjacent_ids = [
                    f"{logical_id}#{chunk_num - 1}",
                    f"{logical_id}#{chunk_num + 1}"
                ]

                # Build query to fetch additional chunks
                chunk_query = {
                    "size": 2,
                    "query": {
                        "bool": {
                            "filter": [
                                {"terms": {"id": adjacent_ids}}
                            ]
                        }
                    },
                    "_source": ["id", "content", "title"]
                }

                adjacent_response = await self.client.search(index=index_name, body=chunk_query)
                adjacent_chunks = {
                    d["_source"]["id"]: d["_source"]["content"] for d in
                    adjacent_response["hits"]["hits"]
                }

                # Order the chunks correctly: [chunk-1, current, chunk+1]
                full_content = ""
                if f"{logical_id}#{chunk_num - 1}" in adjacent_chunks:
                    full_content += adjacent_chunks[f"{logical_id}#{chunk_num - 1}"] + "\n"
                full_content += doc.get("content", "")
                if f"{logical_id}#{chunk_num + 1}" in adjacent_chunks:
                    full_content += "\n" + adjacent_chunks[f"{logical_id}#{chunk_num + 1}"]

                results.append({
                    "content": full_content.strip(),
                    "_score": hit["_score"],
                    "title": title
                })

            return results

        except Exception as e:
            print(f"Error during KNN search: {e}")
            # pylint: disable=raise-missing-from
            raise CustomException(f"Failed to perform KNN search and fetch content: {str(e)}")

    async def delete_documents_by_kb_id(
        self, index_name: str, kb_id: int, id_field: str = "knowledge_base_id"
    ) -> Dict[str, Any]:
        """
        Deletes all documents in the given index where id_field matches kb_id.
        Uses the delete_by_query API for efficient bulk deletion.
        """
        try:
            delete_query = {
                "query": {
                    "term": {
                        id_field: kb_id
                    }
                }
            }

            response = await self.client.delete_by_query(
                index=index_name,
                body=delete_query,
                refresh=True  # Ensure the index is refreshed after deletion
            )

            return {
                "deleted": response["deleted"],
                "total": response["total"],
                "failed": len(response.get("failures", [])),
                "failures": response.get("failures", [])
            }

        except Exception as e:
            # pylint: disable=raise-missing-from
            raise SearchError(f"Failed to delete documents with {id_field}={kb_id}: {str(e)}")

    async def delete_document_by_id_prefix(
        self, index_name: str, doc_id_prefix: str
    ) -> Dict[str, Any]:
        """
        Deletes all documents in the given index where the ID starts with source_doc_id.
        This handles chunked documents where IDs are in the format:
        source_doc_id#1, source_doc_id#2, etc.
        """
        try:
            # Build a query that matches all documents where _id starts with source_doc_id
            delete_query = {
                "query": {
                    "prefix": {
                        "id": doc_id_prefix
                    }
                }
            }
            await self.connect()
            # Use delete_by_query to remove all matching documents
            response = await self.client.delete_by_query(
                index=index_name,
                body=delete_query,
                refresh=True  # Ensure the index is refreshed after deletion
            )

            return {
                "deleted": response["deleted"],
                "total": response["total"],
                "failures": response.get("failures", [])
            }

        except Exception as e:
            # pylint: disable=raise-missing-from
            raise SearchError(f"Failed to delete documents with ID prefix "
                              f"{doc_id_prefix}: {str(e)}")
        finally:
            await self.close()

    async def delete_document_by_id(self, index_name: str, doc_id: str) -> Dict[str, Any]:
        """
        Deletes a single document from the specified index using its document ID.

        Args:
            index_name: Name of the Elasticsearch index
            doc_id: The document ID to delete

        Returns:
            Dictionary containing the deletion result

        Raises:
            SearchError: If the deletion operation fails
        """
        try:
            await self.connect()
            response = await self.client.delete(
                index=index_name,
                id=doc_id,
                refresh=True  # Ensure the index is refreshed after deletion
            )

            return {
                "result": response["result"],
                "id": response["_id"],
                "index": response["_index"]
            }

        except Exception as e:
            # pylint: disable=raise-missing-from
            raise SearchError(f"Failed to delete document with ID {doc_id}: {str(e)}")
        finally:
            await self.close()