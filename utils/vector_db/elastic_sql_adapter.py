"""Elasticsearch adapter implementing the VectorSQLAdapter
interface for vector database operations."""
from typing import Optional, List, Dict, Any

from elasticsearch import AsyncElasticsearch, NotFoundError, ConflictError
from elasticsearch._async.helpers import async_bulk

from config.logging import logger
from config.settings import loaded_config
from knowledge_base.serializers import GetVectorSearchRequest
from utils.exceptions import CustomException
from utils.vector_db.base import VectorSQLAdapter
from utils.vector_db.embeddings import EmbeddingGenerator
from utils.vector_db.exceptions import SearchError


class ElasticsearchSQLAdapter(VectorSQLAdapter):
    """
    Elasticsearch adapter implementing the VectorSQLAdapter interface.
    """

    INDEXING_DEFAULT_MAPPING_FOR_SQL = {
        "settings": {
            "number_of_shards": 3,
            "number_of_replicas": 1
        },
        "mappings": {
            "properties": {
                "table_name": {
                    "type": "keyword"
                },
                "table_description": {
                    "type": "text"
                },
                "column_name": {
                    "type": "keyword"
                },
                "column_description": {
                    "type": "text",
                    "analyzer": "english"
                },
                "column_description_embedding": {
                    "type": "dense_vector",
                    "dims": 1536,
                    "index": True,
                    "similarity": "cosine"
                },
                "data_type": {
                    "type": "keyword"
                },
                "database_name": {
                    "type": "keyword"
                },
                "knowledge_base_id": {"type": "keyword"},
            }
        }
    }

    def __init__(self, **kwargs):
        """Initialize the Elasticsearch client.
        
        :param es_hosts: List of Elasticsearch host URLs (e.g., ["http://localhost:9200"]).
        :param kwargs: Other keyword arguments to pass to AsyncElasticsearch client.
        """
        self.client = AsyncElasticsearch(
            hosts=[loaded_config.elastic_search_url], **kwargs
        )
        self.embedding_generator = EmbeddingGenerator(
            api_key=loaded_config.openai_gpt4o_api_key
        )
        self._is_connected = False

    async def connect(self) -> None:
        """Establish a connection to Elasticsearch (actually, just pings to check)."""
        if not self._is_connected:
            if not await self.client.ping():
                raise ConnectionError("Failed to connect to Elasticsearch.")
            self._is_connected = True
            print("Successfully connected to Elasticsearch.")

    async def close(self) -> None:
        """Close the Elasticsearch client connection."""
        if self.client:
            await self.client.close()
            self._is_connected = False
            print("Elasticsearch connection closed.")

    async def create_index(
        self, index_name: str, schema: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create an Elasticsearch index.
        
        'table_name' is the name of the index.
        'schema' is expected to be an Elasticsearch mappings dictionary.
        Example schema (for the 'mappings' key in ES index creation):
        {
            "properties": {
                "column_name": {"type": "keyword"},
                "column_description_embedding": {"type": "dense_vector", "dims": 1536}
                // ... other properties
            }
        }
        """
        if await self.client.indices.exists(index=index_name):
            logger.info(f"Index '{index_name}' already exists. Skipping creation.")
            return {
                "acknowledged": False,
                "message": f"Index '{index_name}' already exists skipping creation."
            }
        try:
            response = await self.client.indices.create(
                index=index_name,
                body=self.INDEXING_DEFAULT_MAPPING_FOR_SQL
            )
            return response.body
        except Exception as e:  # pylint: disable=broad-exception-caught
            return {"acknowledged": False, "error": str(e)}

    async def delete_index(self, index_name: str) -> Dict[str, Any]:
        """Delete an Elasticsearch index."""
        if not await self.client.indices.exists(index=index_name):
            return {"acknowledged": False, "error": f"Index '{index_name}' not found."}
        try:
            response = await self.client.indices.delete(index=index_name)
            return response.body
        except NotFoundError:
            return {
                "acknowledged": False,
                "error": f"Index '{index_name}' not found during deletion."
            }
        except Exception as e:  # pylint: disable=broad-exception-caught
            return {"acknowledged": False, "error": str(e)}

    async def insert_doc(
        self, index_name: str, row_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Insert a single document into the specified Elasticsearch index.
        
        If 'id' or '_id' is in row_data, it will be used as the document ID.
        """
        doc_id = row_data.pop('_id', row_data.pop('id', None))
        try:
            if doc_id:
                response = await self.client.index(
                    index=index_name, id=doc_id, document=row_data
                )
            else:
                response = await self.client.index(index=index_name, document=row_data)
            return {
                "id": response.body['_id'], 
                "result": response.body['result'], 
                "index": response.body['_index']
            }
        # If ID exists and op_type='create' was implicitly used or
        # versioning conflict
        except ConflictError as e:
            return {
                "id": doc_id,
                "result": "conflict",
                "error": str(e.meta.explanation if e.meta else e)
            }
        except Exception as e:  # pylint: disable=broad-exception-caught
            return {"id": doc_id, "result": "error", "error": str(e)}

    async def bulk_insert_docs(
        self, index_name: str, rows_data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Insert multiple documents into Elasticsearch using the bulk helper."""
        actions = []
        for row in rows_data:
            action: Dict[str, Any] = {
                "_index": index_name,
                "_source": row
            }
            doc_id = row.pop('_id', row.pop('id', None))
            if doc_id:
                action["_id"] = doc_id
            actions.append(action)

        if not actions:
            return {"successful": 0, "failed": 0, "errors": []}

        try:
            successes, errors = await async_bulk(self.client, actions, raise_on_error=False)
            return {"successful": successes, "failed": len(errors), "errors": errors}
        except Exception as e:  # pylint: disable=broad-exception-caught
            return {"successful": 0, "failed": len(actions), "errors": [str(e)]}

    async def knn_similarity_search(
        self, index_name: str, request: GetVectorSearchRequest
    ):
        """Perform a KNN similarity search and return concatenated chunks (max 3) for each result.
        
        including chunk_num-1 and chunk_num+1 if available. Filters by knowledge_base_id.
        """
        try:
            # Generate query embedding
            query_vector = await self.embedding_generator.generate_embedding(request.query)

            if len(query_vector) != 1536:
                raise ValueError(
                    f"Query vector has incorrect dimensions: {len(query_vector)} (Expected: 1536)"
                )

            num_candidates = max(request.top_answer_count * 5, 100)

            # KNN Search query
            knn_query = {
                "size": request.top_answer_count,
                "knn": {
                    "field": "column_description_embedding",
                    "query_vector": query_vector,
                    "k": request.top_answer_count,
                    "num_candidates": num_candidates,
                    "filter": [
                        {
                            "terms": {
                                "knowledge_base_id": request.knowledge_base_id
                            }
                        }
                    ]
                },
                "_source": [
                    "id",
                    "table_name",
                    "table_description",
                    "column_name",
                    "column_description",
                    "data_type"
                ]
            }

            response = await self.client.search(index=index_name, body=knn_query)
            hits = response["hits"]["hits"]

            results = []

            for hit in hits:
                doc = hit["_source"]
                results.append({
                    "table_name": doc.get("table_name"),
                    "table_description": doc.get("table_description", ""),
                    "column_name": doc.get("column_name"),
                    "column_description": doc.get("column_description"),
                    "column_data_type": doc.get("data_type"),
                    "_score": hit["_score"]
                })

            return results
        except Exception as e:
            print(f"Error during KNN search: {e}")
            # pylint: disable=raise-missing-from
            raise CustomException(f"Failed to perform KNN search and fetch content: {str(e)}")

    async def delete_documents_by_kb_id(
        self, index_name: str, kb_id: int, id_field: str = "knowledge_base_id"
    ) -> Dict[str, Any]:
        """
        Deletes all documents in the given index where id_field matches kb_id.
        Uses the delete_by_query API for efficient bulk deletion.
        """
        try:
            delete_query = {
                "query": {
                    "term": {
                        id_field: kb_id
                    }
                }
            }

            response = await self.client.delete_by_query(
                index=index_name,
                body=delete_query,
                refresh=True  # Ensure the index is refreshed after deletion
            )

            return {
                "deleted": response["deleted"],
                "total": response["total"],
                "failed": len(response.get("failures", [])),
                "failures": response.get("failures", [])
            }

        except Exception as e:
            # pylint: disable=raise-missing-from
            raise SearchError(f"Failed to delete documents with {id_field}={kb_id}: {str(e)}")

    async def get_documents_by_kb_id(
        self, index_name: str, kb_id: int, id_field: str = "knowledge_base_id"
    ) -> List[any]:
        """
        Get all documents in the given index where id_field matches kb_id.
        Returns a dictionary containing the matching documents and metadata.
        """
        try:
            search_query = {
                "query": {
                    "term": {
                        id_field: kb_id
                    }
                },
                "size": 10000  # Set a large size to get all matching documents, adjust as needed
            }

            response = await self.client.search(
                index=index_name,
                body=search_query
            )

            hits = response["hits"]["hits"]
            results = []

            for hit in hits:
                doc = hit["_source"]
                results.append({
                    "table_name": doc.get("table_name"),
                    "table_description": doc.get("table_description", ""),
                    "column_name": doc.get("column_name"),
                    "column_description": doc.get("column_description"),
                    "column_data_type": doc.get("data_type"),
                    "_score": hit["_score"]
                })

            return results

        except Exception as e:
            # pylint: disable=raise-missing-from
            raise SearchError(f"Failed to get documents with {id_field}={kb_id}: {str(e)}")
