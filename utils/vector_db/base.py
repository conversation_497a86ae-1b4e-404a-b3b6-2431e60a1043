"""Abstract base classes for vector database adapters and SQL
vector database operations."""
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from code_indexing.serializers import VectorSearchRequest, KeywordSearchRequest
from knowledge_base.serializers import GetVectorSearchRequest


class VectorDBAdapter(ABC):
    """Abstract base class for vector database adapters providing
    standardized interface for vector operations."""
    
    @abstractmethod
    async def connect(self) -> None:
        """Establishes a connection to the underlying vector database."""

    @abstractmethod
    async def close(self) -> None:
        """Closes the connection to the vector database."""

    @abstractmethod
    async def create_index(self, index_name: str, settings: Dict[str, Any]) -> Dict[str, Any]:
        """Creates a new index in the vector database
        with the specified settings."""

    @abstractmethod
    async def keyword_search_source_code(
        self, request: KeywordSearchRequest, index: str, kb_id: int = None
    ) -> Dict[str, Any]:
        """Performs keyword-based search over source code content.

        Args:
            request (KeywordSearchRequest): Search request with keywords and filters.
            index (str): Index to search in.
            kb_id (int, optional): Optional knowledge base ID filter.

        Returns:
            Dict[str, Any]: Search results.
        """

    @abstractmethod
    async def knn_similarity_search(
        self, request: VectorSearchRequest, index: str, kb_id: int = None
    ) -> Dict[str, Any]:
        """Performs a KNN vector similarity search using the given request.

        Args:
            request (VectorSearchRequest): The vector search request
            with query and filters.
            index (str): The index to search in.
            kb_id (int, optional): Optional knowledge base ID filter.

        Returns:
            Dict[str, Any]: Search results.
        """

    @abstractmethod
    async def bulk_insert(self, index_name: str, documents: list[dict], mapping: dict, actions: list):
        """Inserts multiple documents into the specified index.

        Args:
            index_name (str): The name of the index.
            documents (list[dict]): Documents to insert.
            mapping (dict): Index mapping.
            actions (list): Bulk actions for Elasticsearch.

        Returns:
            Any: Result of the bulk operation.
        """

    @abstractmethod
    async def get_file_content(
        self, index_name: str, file_path: str, kb_id: int = None
    ) -> Optional[str]:
        """Retrieves full file content from document chunks by file path and knowledge base ID.

        Args:
            index_name (str): Index to search.
            file_path (str): File path to match.
            kb_id (int, optional): Knowledge base ID.

        Returns:
            Optional[str]: Concatenated file content or None.
        """

    @abstractmethod
    async def search_and_fetch_content(
        self, request: GetVectorSearchRequest, index_name: str
    ) -> List[dict]:
        """Performs a vector search and returns relevant content snippets from matching documents.

        Args:
            request (GetVectorSearchRequest): Request with query vector and filters.
            index_name (str): Index to search in.

        Returns:
            List[dict]: Matched documents and their content.
        """

    @abstractmethod
    async def delete_documents_by_kb_id(
        self, index_name: str, kb_id: int, id_field: str = "knowledge_base_id"
    ) -> Dict[str, Any]:
        """Deletes all documents in an index with the given knowledge base ID.

        Args:
            index_name (str): Index to delete from.
            kb_id (int): Knowledge base ID to match.
            id_field (str): Field name representing the ID (default: "knowledge_base_id").

        Returns:
            Dict[str, Any]: Deletion result.
        """

    @abstractmethod
    async def delete_document_by_id_prefix(
        self, index_name: str, doc_id_prefix: str
    ) -> Dict[str, Any]:
        """Deletes all documents whose ID starts with the specified prefix.

        Args:
            index_name (str): The index to delete from.
            doc_id_prefix (str): The prefix to match document IDs.

        Returns:
            Dict[str, Any]: Deletion result.
        """


class VectorSQLAdapter(ABC):
    """
    Abstract Base Class for a minimalistic Vector SQL Database Adapter.
    Focuses on core operations typical for SQL databases with vector capabilities.
    """

    @abstractmethod
    async def connect(self) -> None:
        """Establish a connection to the database."""

    @abstractmethod
    async def close(self) -> None:
        """Close the database connection."""

    @abstractmethod
    async def create_index(self, index_name: str, schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a table to store documents/vectors.
        'schema' should define columns, data types, vector dimensions,
        and any vector-specific indexing options (e.g., HNSW parameters for pgvector).
        Example schema:
        {
            "columns": [
                {"name": "id", "type": "SERIAL PRIMARY KEY"},
                {"name": "text_content", "type": "TEXT"},
                {"name": "metadata", "type": "JSONB"},
                {"name": "embedding", "type": "VECTOR(384)"} // Dimension specific to your model
            ],
            "vector_index": { // Optional: specific vector index details
                "column": "embedding",
                "method": "hnsw", // or "ivfflat", etc.
                "options": {"m": 16, "ef_construction": 64}
            }
        }
        Returns a confirmation or status.
        """

    @abstractmethod
    async def delete_index(self, index_name: str) -> Dict[str, Any]:
        """
        Delete a table.
        Returns a confirmation or status.
        """

    @abstractmethod
    async def insert_doc(self, index_name: str, row_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Insert a single row (document) into the specified table.
        'row_data' is a dictionary where keys are column names.
        Returns the inserted row's ID or a status.
        """

    @abstractmethod
    async def bulk_insert_docs(
        self, index_name: str, rows_data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Insert multiple rows (documents) into the specified table efficiently.
        'rows_data' is a list of dictionaries, each representing a row.
        Returns a summary of the operation (e.g., number of rows inserted).
        """

    @abstractmethod
    async def knn_similarity_search(self, index_name: str, request: VectorSearchRequest):
        """
        Perform a K-Nearest Neighbors (k-NN) similarity search on a vector column.
        'request' should contain the query_embedding, top_k, and any filters.
        Filters within the request should be translatable to SQL WHERE clauses.
        Example request: VectorSearchRequest(query_embedding=[...],
        top_k=5, filters={"metadata.category": "electronics"})
        Returns a list of matching rows/documents with scores.
        """
