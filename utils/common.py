"""Common utility functions for user data extraction and request handling."""
from datetime import datetime

from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from starlette.requests import Request

from clerk_integration.utils import UserData

from config.logging import logger
from config.settings import loaded_config
from utils.exceptions import SessionExpiredException


async def get_user_data_from_request(request: Request):
    """Attempts to retrieve user data from the request using Clerk or fallback from request state.

    Args:
        request (Request): The incoming FastAPI request object.

    Returns:
        UserData: The extracted user data from Clerk or request state.

    Raises:
        HTTPException: If user data cannot be retrieved, raises 401 Unauthorized with session
        expired info.
    """

    try:
        user_data: UserData = await loaded_config.clerk_auth_helper.get_user_data_from_clerk(
            request
        )
        return user_data
        # return UserData(
        #     _id="user_2wAfohyRcJAYvyHN9iwoWMsniHP",
        #     orgId="org_2wM6P0x3exs5fK4VVX4VY2EtqYA",
        #     firstName="John",
        #     lastName="Doe",
        #     email="<EMAIL>",
        #     username="johndoe",
        #     phoneNumber="1234567890",
        #     profilePicUrl="https://example.com/profile.jpg",
        #     active=True,
        #     roleIds=[1, 2],
        #     meta={"key": "value"},
        #     createdAt=datetime.now(),
        #     updatedAt=datetime.now(),
        #     workspace=[]
        # )
    except Exception:  # pylint: disable=broad-exception-caught
        try:
            user_data: UserData = request.state.user_data
            return user_data
        except Exception as fallback_error:
            logger.error("Invalid request headers: %s", fallback_error)
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={
                    "error": SessionExpiredException.DEFAULT_MESSAGE,
                    "message": str(fallback_error),
                    "code": SessionExpiredException.ERROR_CODE
                }
            ) from fallback_error
