"""Kafka producer wrapper classes for event emission and batch processing."""
from typing import List
from eventqueue.constants import DEFAULT_DESERIALIZATION_FORMAT, DEFAULT_HASH_FLAG
from eventqueue.emitter import AsyncEventEmitter

from utils.kafka.producer.config import KAFKA_COMMON_PRODUCER_CONFIG
from utils.singleton import Singleton


class AsyncEventBridge(metaclass=Singleton):
    """Class AsyncEventBridge."""

    def __init__(self, configurations=None, *args, **kwargs):  # pylint: disable=unused-argument
        """Initializes the AsyncEventBridge with a Kafka event emitter.

        Args:
            configurations (dict): Kafka producer configuration dictionary.
        """
        if configurations is None:
            configurations = KAFKA_COMMON_PRODUCER_CONFIG

        self.event_emitter = AsyncEventEmitter(configurations)

    async def stop_producer(self):
        """Stops the underlying Kafka producer if it is active."""

        if self.event_emitter.kafka_producer and self.event_emitter.kafka_producer.producer:
            await self.event_emitter.kafka_producer.stop_producer()


class AsyncEventEmitterWrapper:
    """Wrapper class for AsyncEventBridge providing event queuing
    and batch processing functionality."""

    def __init__(self, *args, **kwargs):
        """Initializes the wrapper around AsyncEventBridge and prepares an internal event queue."""

        self.event_emitter = AsyncEventBridge(*args, **kwargs).event_emitter
        self.event_queue: List = []

    # pylint: disable=too-many-arguments
    def add_event_to_queue(
        self, *,
        topics, partition_value,
        event, event_meta=None,
        serialization_format=DEFAULT_DESERIALIZATION_FORMAT,
        hash_flag=DEFAULT_HASH_FLAG, callback=False, headers=None
    ):
        """Adds a single event to the internal queue for later batch emission.

        Args:
            topics: Topic(s) to publish the event to.
            partition_value: Value used to determine Kafka partition.
            event: The event payload.
            event_meta (dict): Additional metadata for the event.
            serialization_format: Format to serialize the event (default: JSON).
            hash_flag (bool): Whether to hash the partition key.
            callback (bool): Whether a delivery callback is expected.
            headers (dict): Optional Kafka headers to attach.
        """
        if event_meta is None:
            event_meta = {}

        event_dict = {
            'topics': topics,
            'partition_value': partition_value,
            'event': event,
            'event_meta': event_meta,
            'serialization_format': serialization_format,
            'hash_flag': hash_flag,
            'callback': callback,
            'headers': headers
        }
        self.event_queue.append(event_dict)

    async def emit(self, *args, **kwargs):
        """Emits a single event immediately using the underlying AsyncEventEmitter."""

        return await self.event_emitter.emit(*args, **kwargs)

    async def produce_event(self, *args, **kwargs):
        """Produces a single event to Kafka using the underlying emitter's producer logic."""

        return await self.event_emitter.produce_event(*args, **kwargs)

    async def emit_events(self):
        """Emits all queued events in order and clears the queue afterward."""

        for event in self.event_queue:
            try:
                await self.event_emitter.emit(
                    topics=event["topics"],
                    partition_value=event["partition_value"],
                    event=event["event"],
                    event_meta=event["event_meta"],
                    serialization_format=event["serialization_format"],
                    hash_flag=event["hash_flag"],
                    callback=event["callback"],
                    headers=event["headers"]
                )
            except Exception:  # pylint: disable=broad-exception-caught
                pass
        self.clear_queue()

    def clear_queue(self):
        """Clears the internal event queue."""

        self.event_queue = []
