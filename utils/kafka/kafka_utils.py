"""Kafka utility classes and functions for partition management
and message distribution."""
from config.settings import loaded_config


class RoundRobinPartitioner:
    """Implements round-robin partition selection for distributing Kafka
    messages evenly across partitions."""
    
    def __init__(self, partitions):
        """Initializes the round-robin partitioner with the given number of partitions.

        Args:
            partitions (int): Total number of partitions to cycle through.
        """

        self.partitions = partitions
        self.index = 0

    def partition(self):
        """Returns the next partition number in a round-robin fashion.

        Returns:
            int: The selected partition index.
        """

        partition = self.index
        self.index = (self.index + 1) % self.partitions
        return partition


almanac_partitioner = RoundRobinPartitioner(int(loaded_config.kafka_partitions))
