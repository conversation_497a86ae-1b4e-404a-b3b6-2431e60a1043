"""SQLAlchemy utilities for database operations, URL conversion, and timestamp mixins."""
from datetime import datetime
from urllib.parse import urlparse, urlunparse

from pytz import timezone
from sqlalchemy import DateTime, Column
from sqlalchemy.orm import declarative_mixin, Mapped, declarative_base

from utils.constants import UTC_TIME_ZONE


def get_current_time(time_zone: str = UTC_TIME_ZONE):
    """Returns the current datetime in the specified timezone.

    Args:
        time_zone (str): The timezone to use. Defaults to UTC.

    Returns:
        datetime: Current time with timezone information.
    """

    return datetime.now(timezone(time_zone))


def async_db_url(db_url: str):
    """Converts a standard PostgreSQL database URL to its asyncpg-compatible version.

    Args:
        db_url (str): Synchronous PostgreSQL database URL.

    Returns:
        str: Asynchronous database URL using asyncpg driver.
    """

    parsed_url = urlparse(db_url)
    new_scheme = "postgresql+asyncpg"
    netloc = f"{parsed_url.username}:{parsed_url.password}@{parsed_url.hostname}:{parsed_url.port}"
    updated_db_url = urlunparse((new_scheme, netloc, parsed_url.path, '', '', ''))
    return updated_db_url


@declarative_mixin
class TimestampMixin:
    """SQLAlchemy mixin that adds timezone-aware `created_at` and `updated_at` timestamp fields."""

    created_at: Mapped[datetime] = Column(DateTime(timezone=True), default=get_current_time)
    updated_at: Mapped[datetime] = Column(DateTime(timezone=True), default=get_current_time,
                                          onupdate=get_current_time)


Base = declarative_base()
