"""Custom middleware classes for FastAPI application security and request processing."""
from starlette.middleware.base import BaseHTTPMiddleware


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware that adds security headers to HTTP responses for enhanced application security."""
        
    async def dispatch(self, request, call_next):
        """Intercepts the request to add standard security headers to the HTTP response.

        Args:
            request: The incoming HTTP request.
            call_next: The next middleware or route handler in the ASGI stack.

        Returns:
            Response: The HTTP response with security headers applied.
        """

        response = await call_next(request)
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        response.headers['Content-Security-Policy'] = "default-src 'self'; script-src 'self'"
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['Referrer-Policy'] = 'no-referrer'
        response.headers['Permissions-Policy'] = 'geolocation=(), microphone=()'
        return response
