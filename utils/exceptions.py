"""Custom exception classes for application-specific error handling and logging."""
from structlog.contextvars import bind_contextvars


class CustomException(Exception):
    """Base custom exception class with structured logging integration."""

    DEFAULT_ERROR_MESSAGE = "Exception occurred"

    def __init__(self, error_message: str = None):
        """Initializes a custom exception with an optional error message and binds it to
        structlog context.

        Args:
            error_message (str, optional): The message to include with the exception.
            Defaults to a standard message.
        """

        error_message = error_message or self.DEFAULT_ERROR_MESSAGE
        self.error_message = error_message
        super().__init__(self.error_message)
        bind_contextvars(exception={"error_message": self.error_message})


class ApiException(CustomException):
    """Represents API-related errors, with a default message indicating an API failure."""

    DEFAULT_ERROR_MESSAGE = "API Exception"


class SessionExpiredException(CustomException):
    """Represents a session expiration error with a default message and custom error code."""

    DEFAULT_MESSAGE = "Session Expired for Almanac"
    ERROR_CODE = 6001
    