"""Response serializers and data models for API responses using Pydantic."""
from typing import List, Dict, Optional
from uuid import uuid4

from pydantic.fields import Field
from pydantic import BaseModel


class ResponseData(BaseModel):
    """Standard response data model for API endpoints with success, error, and data fields."""
    
    identifier: str = Field(default_factory=lambda: str(uuid4()))
    success: bool
    message: Optional[str] = None
    errors: Optional[List] = Field(None)
    data: Optional[List | Dict] = Field(None)

    def dict(self, *args, **kwargs):
        """Overrides the default `dict()` method to use Pydantic's `model_dump()` for serialization.

        Returns:
            dict: A dictionary representation of the model.
        """

        return super().model_dump(*args, **kwargs)
