"""Singleton metaclass implementation for creating singleton design pattern classes."""


class Singleton(type):
    """
    Use this class as a metaclass to create Singleton classes
    """

    _instances = {}

    def __call__(cls, *args, **kwargs):
        """Creates or returns the existing instance of the Singleton class.

        Ensures only one instance of the class is ever created.

        Returns:
            object: The single instance of the class.
        """

        if cls not in cls._instances:
            cls._instances[cls] = super().__call__(*args, **kwargs)
        return cls._instances[cls]
