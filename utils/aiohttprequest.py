"""HTTP client utility using aiohttp for asynchronous HTTP requests and session management."""
import aiohttp


class AioHttpRequest:
    """Manages aiohttp ClientSession for making asynchronous HTTP requests."""

    def __init__(self):
        """Initializes the AioHttpRequest with a new aiohttp ClientSession."""

        session = aiohttp.ClientSession()
        self.session = session

    def get_aiohttp_session(self):
        """Returns the aiohttp ClientSession instance.

        Returns:
            aiohttp.ClientSession: The current HTTP session.
        """

        return self.session

    async def close_session(self):
        """Closes the aiohttp ClientSession to release resources."""

        await self.session.close()
