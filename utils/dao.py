"""Base Data Access Object (DAO) class for SQLAlchemy ORM operations and database interactions."""
from sqlalchemy import inspect, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm.attributes import flag_modified, flag_dirty
from sqlalchemy.exc import IntegrityError


class BaseDao:
    """Base Data Access Object providing common database operations for SQLAlchemy ORM models."""

    def __init__(self, session: AsyncSession, db_model):
        """Initializes the BaseDao with a given database session and ORM model.

        Args:
            session (AsyncSession): SQLAlchemy asynchronous session.
            db_model: SQLAlchemy ORM model class.
        """

        self.session = session
        self.db_model = db_model


    async def _flush(self):
        """Flushes the current session to the database without committing."""

        await self.session.flush()


    async def _commit(self):
        """Commits the current transaction in the session."""

        await self.session.commit()


    async def _execute_query(self, query):
        """Executes a given SQLAlchemy query using the session.

        Args:
            query: The SQLAlchemy query object to be executed.

        Returns:
            Result: The result of the executed query.
        """

        return await self.session.execute(query)


    def get_orm_object(self, **kwargs):
        """Creates an instance of the ORM model with the given keyword arguments.

        Returns:
            ORM object: An instance of the database model.
        """

        return self.db_model(**kwargs)


    def add_object(self, create_object_dict=None, **create_kwargs):
        """Adds a new ORM object to the session using either a dict or keyword arguments.

        Args:
            create_object_dict (dict, optional): A dictionary of values for the new object.
            **create_kwargs: Additional keyword arguments.

        Returns:
            ORM object: The added ORM object.
        """

        kwargs = create_object_dict or create_kwargs
        orm_object = self.get_orm_object(**kwargs)
        self.session.add(orm_object)
        return orm_object


    @staticmethod
    def flag_modified(orm_obj, key: str):
        """Marks a specific field of an ORM object as modified for SQLAlchemy change tracking.

        Args:
            orm_obj: The ORM object.
            key (str): The field to mark as modified.
        """

        flag_modified(orm_obj, key=key)


    @staticmethod
    def flag_dirty(orm_obj):
        """Marks an ORM object as dirty to ensure SQLAlchemy considers it modified.

        Args:
            orm_obj: The ORM object to flag.
        """

        flag_dirty(orm_obj)


    async def create(self, create_object_dict=None, **create_kwargs):
        """Creates and commits a new ORM object.

        Args:
            create_object_dict (dict, optional): A dictionary of values for the new object.
            **create_kwargs: Additional keyword arguments.

        Returns:
            ORM object: The created and committed ORM object.
        """

        orm_object = self.add_object(create_object_dict, **create_kwargs)
        await self._commit()
        return orm_object


    async def update_by_pk(self, pk_values, update_values_dict=None, **update_kwargs):
        """Updates records in the database by primary key values.

        Args:
            pk_values (Union[int, List[int]]): One or more primary key values to match.
            update_values_dict (dict, optional): Dictionary of fields to update.
            **update_kwargs: Additional update values as keyword arguments.

        Returns:
            Result: The result of the update execution.
        """

        if not isinstance(pk_values, list):
            pk_values = [pk_values]
        pk_field_name = inspect(self.db_model).primary_key[0].name
        pk_field = getattr(self.db_model, pk_field_name)
        kwargs = update_values_dict or update_kwargs
        update_query = update(self.db_model).where(pk_field.in_(pk_values)).values(**kwargs)
        return await self._execute_query(update_query)


    async def get_by_pk(self, pk_value):
        """Fetches a single record by its primary key value.

        Args:
            pk_value: The primary key value of the record to retrieve.

        Returns:
            ORM object or None: The matched record, if found.
        """

        return await self.session.get(self.db_model, pk_value)


    async def bulk_insert(self, mappings):
        """Performs a bulk insert operation with the given mappings.

        Args:
            mappings (List[dict]): List of dictionaries representing rows to insert.

        Raises:
            IntegrityError: If a database integrity error occurs.
        """

        try:
            await self.session.run_sync(
                lambda ses: ses.bulk_insert_mappings(self.db_model, mappings)
            )
            await self.session.commit()
        except IntegrityError as e:
            self.session.rollback()
            raise e
