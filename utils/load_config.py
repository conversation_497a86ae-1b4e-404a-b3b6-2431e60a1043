"""Application lifecycle management for startup, shutdown, and connection initialization."""
from spacy.cli import download

from config.settings import loaded_config
from utils.connection_manager import ConnectionManager
from utils.aiohttprequest import AioHttpRequest



async def run_on_startup():
    """Executes tasks needed during application startup, such as initializing connections."""

    try:
        await init_connections()
    except Exception as e:  # pylint: disable=broad-exception-caught
        print(e)


async def run_on_exit():
    """Cleans up resources during application shutdown, including database and HTTP client
    connections."""

    await loaded_config.connection_manager.close_connections()
    await loaded_config.aiohttp_request.close_session()


async def run_on_consumer_exit():
    """Closes only database connections during the shutdown of a consumer service."""

    await loaded_config.connection_manager.close_connections()


async def init_connections():
    """Initializes and assigns the main application's database and HTTP client connections
    to global config."""

    connection_manager = ConnectionManager(
        db_url=loaded_config.db_url,
        db_echo=loaded_config.db_echo
    )
    loaded_config.connection_manager = connection_manager
    loaded_config.aiohttp_request = AioHttpRequest()


async def run_on_consumer_startup():
    """Initializes connections and downloads required NLP model for consumer service startup."""

    try:
        await init_consumer_connections()
        download('en_core_web_md')
    except Exception as e:  # pylint: disable=broad-exception-caught
        print(e)


async def init_consumer_connections():
    """Initializes and assigns database connection manager specifically for consumer
    services."""

    connection_manager = ConnectionManager(
        db_url=loaded_config.db_url,
        db_echo=loaded_config.db_echo
    )
    loaded_config.connection_manager = connection_manager
