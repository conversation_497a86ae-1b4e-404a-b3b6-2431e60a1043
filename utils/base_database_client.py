"""Base database client strategy pattern implementation for various database integrations."""
import abc
from typing import List, Dict, Any

class DatabaseClientStrategy(abc.ABC):
    """
    Abstract base class defining the interface for database client strategies.
    """

    def __init__(self, credentials: dict):
        """Initializes the database client strategy with provided credentials.

        Args:
            credentials (dict): Credentials required to initialize the database client.
        """

        self.credentials = credentials
        # More specific type can be Union of all possible client types if known
        self.client: Any = None

    @abc.abstractmethod
    async def validate_credentials(self) -> bool:
        """
        Validates the provided credentials by attempting a simple operation.

        Returns:
            bool: True if credentials are valid, False otherwise.
        """

    @abc.abstractmethod
    async def get_table_schemas(
        self, database_or_schema_name: str
    ) -> List[Dict[str, Dict[str, str]]]:
        """
        Retrieves schemas for all tables within a given database or schema.

        Args:
            database_or_schema_name (str): The name of the database or schema.

        Returns:
            List[Dict[str, Dict[str, str]]]: A list of dictionaries.
            Each dictionary in the list represents one table and typically has a single entry
            where the key is the fully qualified table name (e.g., "schema_name.table_name")
            and the value is a dictionary of {column_name: data_type}.
            Example for database_or_schema_name = "dataset1":
            [
                {"dataset1.tableA": {"col1": "STRING", "col2": "INTEGER"}},
                {"dataset1.tableB": {"col_x": "BOOLEAN"}}
            ]
            Returns an empty list if the target has no tables or doesn't exist.
        """


class DatabaseContext:
    """
    The Context class that uses a database strategy.
    """

    def __init__(self, strategy: DatabaseClientStrategy):
        """Initializes the DatabaseContext with a specific database client strategy.

        Args:
            strategy (DatabaseClientStrategy): The strategy implementation to use.
        """

        self._strategy = strategy


    @property
    def strategy(self) -> DatabaseClientStrategy:
        """Gets the current database client strategy in use.

        Returns:
            DatabaseClientStrategy: The currently set strategy instance.
        """

        return self._strategy


    @strategy.setter
    def strategy(self, strategy: DatabaseClientStrategy) -> None:
        """Sets a new database client strategy.

        Args:
            strategy (DatabaseClientStrategy): The strategy to replace the current one.
        """

        self._strategy = strategy


    async def check_connection(self) -> bool:
        """Checks the validity of the credentials using the current database strategy.

        Returns:
            bool: True if the connection is valid, False otherwise.
        """

        return await self._strategy.validate_credentials()


    async def get_schemas_for_tables_in(
        self, database_or_schema_name: str
    ) -> List[Dict[str, Dict[str, str]]]:
        """Fetches the schema details for all tables in the specified database or schema.

        Args:
            database_or_schema_name (str): The name of the database or schema.

        Returns:
            List[Dict[str, Dict[str, str]]]: A list of table schema mappings.
        """

        return await self._strategy.get_table_schemas(database_or_schema_name)