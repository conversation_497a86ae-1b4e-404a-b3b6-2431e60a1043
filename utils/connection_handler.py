"""Database connection handler for managing SQLAlchemy sessions and event emitters."""
from typing import Optional

from sqlalchemy.ext.asyncio import AsyncSession

from config.settings import loaded_config
from utils.kafka import AsyncEventEmitterWrapper


class ConnectionHandler:
    """Handles database connections and event emission for application lifecycle management."""

    def __init__(
        self, connection_manager=None, event_bridge=None
    ):  # pylint: disable=unused-argument
        """Initializes the ConnectionHandler with optional connection manager and event bridge.

        Args:
            connection_manager (optional): Manages database connections.
            event_bridge (optional): Interface to emit events, if provided.
        """

        self._session: Optional[AsyncSession] = None
        self._connection_manager = connection_manager
        self._event_emitter: Optional[AsyncEventEmitterWrapper] = None


    @property
    def session(self):
        """Lazily initializes and returns an asynchronous SQLAlchemy session.

        Returns:
            AsyncSession: The active SQLAlchemy asynchronous session.
        """

        if not self._session:
            session_factory = self._connection_manager.get_session_factory()
            self._session = session_factory()
        return self._session


    @property
    def event_emitter(self):
        """Lazily initializes and returns an AsyncEventEmitterWrapper instance.

        Returns:
            AsyncEventEmitterWrapper: Event emitter instance for producing events.
        """

        if not self._event_emitter:
            self._event_emitter = AsyncEventEmitterWrapper()
        return self._event_emitter

    async def session_commit(self):
        """Commits the current SQLAlchemy session."""

        await self.session.commit()

    async def close(self):
        """Closes the SQLAlchemy session if it has been initialized."""

        if self._session:
            await self._session.close()


async def get_connection_handler_for_app():
    """Dependency provider that yields a ConnectionHandler instance for the app lifecycle.

    Ensures the session is properly closed after use.
    """

    connection_handler = ConnectionHandler(
        connection_manager=loaded_config.connection_manager
    )
    try:
        yield connection_handler
    finally:
        await connection_handler.close()
