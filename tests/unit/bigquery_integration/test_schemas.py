"""
Comprehensive tests for BigQuery integration schema models.

This module contains test data that are safe for testing purposes.
All test data in this file is fictional and for testing purposes only.

SECURITY NOTE: All data in this file is safe test data:
- All descriptions and metadata are fictional test values
- Column names and data types are standard test schema elements
- Database names and table information are test configuration values
- No production credentials or sensitive data is used
"""

import pytest
from typing import List, Dict, Any
from pydantic import ValidationError

from bigquery_integration.schemas import ColumnMetadata, TableMetadata, DatabaseSchema
from knowledge_base.models import IntegrationType


# Test Constants - All values are safe test data, not production values
# Column Description Constants
PRIMARY_KEY_DESCRIPTION = "Primary key for users"
EMAIL_DESCRIPTION = "User email address"
TIMESTAMP_DESCRIPTION = "Timestamp when record was created"
ACCOUNT_CREATION_DESCRIPTION = "Account creation timestamp"

# Table Description Constants
USER_ACCOUNT_INFO = "User account information"
USER_DATA_DESCRIPTION = "User data"
USER_ACTIVITY_DESCRIPTION = "User activity logs"

# Database Description Constants
TEST_SCHEMA_DESCRIPTION = "Test schema"
PRODUCTION_SCHEMA_DESCRIPTION = "Production database schema"
USER_ANALYTICS_DESCRIPTION = "User analytics database containing user info and activity"

# Column Name Constants
ID_COLUMN = "id"
USER_ID_COLUMN = "user_id"
EMAIL_COLUMN = "email"
NAME_COLUMN = "name"
CREATED_AT_COLUMN = "created_at"
ORDER_ID_COLUMN = "order_id"

# Data Type Constants
INTEGER_TYPE = "INTEGER"
STRING_TYPE = "STRING"
TIMESTAMP_TYPE = "TIMESTAMP"

# Table Name Constants
USERS_TABLE = "users"
ORDERS_TABLE = "orders"
TEST_TABLE = "test"
EMPTY_TABLE = "empty_table"
USER_ACTIVITY_TABLE = "user_activity"

# Database Name Constants
TEST_DB = "test_db"
PRODUCTION_DB = "production_db"
EMPTY_DB = "empty_db"
USER_ANALYTICS_DB = "user_analytics"

# Knowledge Base Name Constants
TEST_KB = "test_kb"
PRODUCTION_KB = "production_kb"
EMPTY_KB = "empty_kb"
USER_ANALYTICS_KB = "user_analytics_kb"

# Team ID Constants
TEAM_456 = "team_456"
TEAM_1 = "team_1"
ANALYTICS_TEAM = "analytics_team"

# Project Constants
TEST_PROJECT = "test-project"
ANALYTICS_PROJECT = "analytics-project"

# Key Constants
TEST_KEY = "test-key"
KEY_123 = "key123"

# ID Constants
KB_ID_123 = 123
KB_ID_STRING = "456"
KB_ID_789 = "kb_789"

# Validation Constants
NOT_A_LIST = "not_a_list"
INVALID_PROVIDER = "invalid_provider"

# Field Name Constants
DATA_TYPE_FIELD = "data_type"
COLUMNS_FIELD = "columns"
PROVIDER_FIELD = "provider"
DATABASE_NAME_FIELD = "database_name"
KB_NAME_FIELD = "kb_name"
LIST_FIELD = "list"


class TestColumnMetadata:
    """Test cases for ColumnMetadata model."""

    def test_create_column_metadata_required_fields(self):
        """Test creating ColumnMetadata with required fields only."""
        # Arrange & Act
        column = ColumnMetadata(
            column_name=ID_COLUMN,
            data_type=INTEGER_TYPE
        )
        
        # Assert
        assert column.column_name == ID_COLUMN
        assert column.data_type == INTEGER_TYPE
        assert column.description is None

    def test_create_column_metadata_all_fields(self):
        """Test creating ColumnMetadata with all fields."""
        # Arrange & Act
        column = ColumnMetadata(
            column_name=USER_ID_COLUMN,
            description=PRIMARY_KEY_DESCRIPTION,
            data_type=INTEGER_TYPE
        )
        
        # Assert
        assert column.column_name == USER_ID_COLUMN
        assert column.description == PRIMARY_KEY_DESCRIPTION
        assert column.data_type == INTEGER_TYPE

    def test_column_metadata_missing_required_field(self):
        """Test ValidationError when missing required field."""
        # Act & Assert
        with pytest.raises(ValidationError) as exc_info:
            ColumnMetadata(
                column_name=ID_COLUMN
                # Missing required data_type field
            )
        
        assert DATA_TYPE_FIELD in str(exc_info.value)

    def test_column_metadata_empty_strings(self):
        """Test ColumnMetadata with empty string values."""
        # Arrange & Act
        column = ColumnMetadata(
            column_name="",
            data_type="",
            description=""
        )
        
        # Assert
        assert column.column_name == ""
        assert column.data_type == ""
        assert column.description == ""

    def test_column_metadata_dict_conversion(self):
        """Test ColumnMetadata can be converted to dictionary."""
        # Arrange
        column = ColumnMetadata(
            column_name=EMAIL_COLUMN,
            description=EMAIL_DESCRIPTION,
            data_type=STRING_TYPE
        )
        
        # Act
        column_dict = column.model_dump()
        
        # Assert
        assert column_dict["column_name"] == EMAIL_COLUMN
        assert column_dict["description"] == EMAIL_DESCRIPTION
        assert column_dict["data_type"] == STRING_TYPE

    def test_column_metadata_from_dict(self):
        """Test creating ColumnMetadata from dictionary."""
        # Arrange
        data = {
            "column_name": CREATED_AT_COLUMN,
            "description": TIMESTAMP_DESCRIPTION,
            "data_type": TIMESTAMP_TYPE
        }
        
        # Act
        column = ColumnMetadata(**data)
        
        # Assert
        assert column.column_name == CREATED_AT_COLUMN
        assert column.description == TIMESTAMP_DESCRIPTION
        assert column.data_type == TIMESTAMP_TYPE


class TestTableMetadata:
    """Test cases for TableMetadata model."""

    def test_create_table_metadata_required_fields(self):
        """Test creating TableMetadata with required fields only."""
        # Arrange
        columns = [
            ColumnMetadata(column_name=ID_COLUMN, data_type=INTEGER_TYPE),
            ColumnMetadata(column_name=NAME_COLUMN, data_type=STRING_TYPE)
        ]
        
        # Act
        table = TableMetadata(
            table_name=USERS_TABLE,
            columns=columns
        )
        
        # Assert
        assert table.table_name == USERS_TABLE
        assert len(table.columns) == 2
        assert table.table_description is None

    def test_create_table_metadata_all_fields(self):
        """Test creating TableMetadata with all fields."""
        # Arrange
        columns = [
            ColumnMetadata(column_name=ID_COLUMN, data_type=INTEGER_TYPE),
            ColumnMetadata(column_name=EMAIL_COLUMN, data_type=STRING_TYPE)
        ]
        
        # Act
        table = TableMetadata(
            table_name=USERS_TABLE,
            table_description=USER_ACCOUNT_INFO,
            columns=columns
        )
        
        # Assert
        assert table.table_name == USERS_TABLE
        assert table.table_description == USER_ACCOUNT_INFO
        assert len(table.columns) == 2

    def test_table_metadata_empty_columns(self):
        """Test creating TableMetadata with empty columns list."""
        # Arrange & Act
        table = TableMetadata(
            table_name=EMPTY_TABLE,
            columns=[]
        )
        
        # Assert
        assert table.table_name == EMPTY_TABLE
        assert table.columns == []

    def test_table_metadata_missing_required_fields(self):
        """Test ValidationError when missing required fields."""
        # Act & Assert
        with pytest.raises(ValidationError) as exc_info:
            TableMetadata(
                table_name=TEST_TABLE
                # Missing required columns field
            )
        
        assert COLUMNS_FIELD in str(exc_info.value)

    def test_table_metadata_invalid_columns_type(self):
        """Test ValidationError when columns is not a list."""
        # Act & Assert
        with pytest.raises(ValidationError) as exc_info:
            TableMetadata(
                table_name=TEST_TABLE,
                columns=NOT_A_LIST
            )
        
        assert LIST_FIELD in str(exc_info.value).lower()

    def test_table_metadata_nested_validation(self):
        """Test TableMetadata validates nested ColumnMetadata objects."""
        # Arrange
        invalid_column_data = {
            "column_name": "test_column"
            # Missing required data_type field
        }
        
        # Act & Assert
        with pytest.raises(ValidationError) as exc_info:
            TableMetadata(
                table_name=TEST_TABLE,
                columns=[invalid_column_data]
            )
        
        assert DATA_TYPE_FIELD in str(exc_info.value)

    def test_table_metadata_dict_conversion(self):
        """Test TableMetadata can be converted to dictionary."""
        # Arrange
        columns = [
            ColumnMetadata(column_name=ID_COLUMN, data_type=INTEGER_TYPE),
            ColumnMetadata(column_name=NAME_COLUMN, data_type=STRING_TYPE)
        ]
        table = TableMetadata(
            table_name=USERS_TABLE,
            table_description=USER_DATA_DESCRIPTION,
            columns=columns
        )
        
        # Act
        table_dict = table.model_dump()
        
        # Assert
        assert table_dict["table_name"] == USERS_TABLE
        assert table_dict["table_description"] == USER_DATA_DESCRIPTION
        assert len(table_dict["columns"]) == 2
        assert table_dict["columns"][0]["column_name"] == ID_COLUMN


class TestDatabaseSchema:
    """Test cases for DatabaseSchema model."""

    def test_create_database_schema_required_fields(self):
        """Test creating DatabaseSchema with required fields only."""
        # Arrange
        tables = [
            TableMetadata(
                table_name=USERS_TABLE,
                columns=[ColumnMetadata(column_name=ID_COLUMN, data_type=INTEGER_TYPE)]
            )
        ]

        # Act
        schema = DatabaseSchema(
            provider=IntegrationType.bigquery,
            database_name=TEST_DB,
            tables=tables,
            kb_name=TEST_KB
        )

        # Assert
        assert schema.provider == IntegrationType.bigquery
        assert schema.database_name == TEST_DB
        assert len(schema.tables) == 1
        assert schema.kb_name == TEST_KB
        assert schema.knowledge_base_id is None
        assert schema.description is None
        assert schema.credentials is None
        assert schema.team_id is None

    def test_create_database_schema_all_fields(self):
        """Test creating DatabaseSchema with all fields."""
        # Arrange
        tables = [
            TableMetadata(
                table_name=USERS_TABLE,
                columns=[ColumnMetadata(column_name=ID_COLUMN, data_type=INTEGER_TYPE)]
            ),
            TableMetadata(
                table_name=ORDERS_TABLE,
                columns=[ColumnMetadata(column_name=ORDER_ID_COLUMN, data_type=STRING_TYPE)]
            )
        ]
        credentials = {"project_id": TEST_PROJECT, "private_key": TEST_KEY}

        # Act
        schema = DatabaseSchema(
            provider=IntegrationType.bigquery,
            database_name=PRODUCTION_DB,
            tables=tables,
            knowledge_base_id=KB_ID_123,
            description=PRODUCTION_SCHEMA_DESCRIPTION,
            credentials=credentials,
            team_id=TEAM_456,
            kb_name=PRODUCTION_KB
        )

        # Assert
        assert schema.provider == IntegrationType.bigquery
        assert schema.database_name == PRODUCTION_DB
        assert len(schema.tables) == 2
        assert schema.knowledge_base_id == KB_ID_123
        assert schema.description == PRODUCTION_SCHEMA_DESCRIPTION
        assert schema.credentials == credentials
        assert schema.team_id == TEAM_456
        assert schema.kb_name == PRODUCTION_KB

    def test_database_schema_knowledge_base_id_types(self):
        """Test DatabaseSchema accepts both int and string for knowledge_base_id."""
        # Arrange
        tables = [
            TableMetadata(
                table_name=TEST_TABLE,
                columns=[ColumnMetadata(column_name=ID_COLUMN, data_type=INTEGER_TYPE)]
            )
        ]

        # Test with integer
        schema_int = DatabaseSchema(
            provider=IntegrationType.bigquery,
            database_name=TEST_DB,
            tables=tables,
            knowledge_base_id=KB_ID_123,
            kb_name=TEST_KB
        )

        # Test with string
        schema_str = DatabaseSchema(
            provider=IntegrationType.bigquery,
            database_name=TEST_DB,
            tables=tables,
            knowledge_base_id=KB_ID_STRING,
            kb_name=TEST_KB
        )

        # Assert
        assert schema_int.knowledge_base_id == KB_ID_123
        assert schema_str.knowledge_base_id == KB_ID_STRING

    def test_database_schema_empty_tables(self):
        """Test creating DatabaseSchema with empty tables list."""
        # Arrange & Act
        schema = DatabaseSchema(
            provider=IntegrationType.bigquery,
            database_name=EMPTY_DB,
            tables=[],
            kb_name=EMPTY_KB
        )

        # Assert
        assert schema.provider == IntegrationType.bigquery
        assert schema.database_name == EMPTY_DB
        assert schema.tables == []
        assert schema.kb_name == EMPTY_KB

    def test_database_schema_missing_required_fields(self):
        """Test ValidationError when missing required fields."""
        # Test missing provider
        with pytest.raises(ValidationError) as exc_info:
            DatabaseSchema(
                database_name=TEST_DB,
                tables=[],
                kb_name=TEST_KB
            )
        assert PROVIDER_FIELD in str(exc_info.value)

        # Test missing database_name
        with pytest.raises(ValidationError) as exc_info:
            DatabaseSchema(
                provider=IntegrationType.bigquery,
                tables=[],
                kb_name=TEST_KB
            )
        assert DATABASE_NAME_FIELD in str(exc_info.value)

        # Test missing kb_name
        with pytest.raises(ValidationError) as exc_info:
            DatabaseSchema(
                provider=IntegrationType.bigquery,
                database_name=TEST_DB,
                tables=[]
            )
        assert KB_NAME_FIELD in str(exc_info.value)

    def test_database_schema_invalid_provider(self):
        """Test ValidationError when provider is not a valid IntegrationType."""
        # Arrange
        tables = [
            TableMetadata(
                table_name=TEST_TABLE,
                columns=[ColumnMetadata(column_name=ID_COLUMN, data_type=INTEGER_TYPE)]
            )
        ]

        # Act & Assert
        with pytest.raises(ValidationError) as exc_info:
            DatabaseSchema(
                provider=INVALID_PROVIDER,
                database_name=TEST_DB,
                tables=tables,
                kb_name=TEST_KB
            )
        
        assert PROVIDER_FIELD in str(exc_info.value)

    def test_database_schema_credentials_validation(self):
        """Test DatabaseSchema accepts various credential formats."""
        # Arrange
        tables = [
            TableMetadata(
                table_name=TEST_TABLE,
                columns=[ColumnMetadata(column_name=ID_COLUMN, data_type=INTEGER_TYPE)]
            )
        ]

        # Test with dict credentials
        schema = DatabaseSchema(
            provider=IntegrationType.bigquery,
            database_name=TEST_DB,
            tables=tables,
            credentials={"key": "value", "number": 123},
            kb_name=TEST_KB
        )

        # Assert
        assert schema.credentials == {"key": "value", "number": 123}

        # Test with None credentials
        schema_none = DatabaseSchema(
            provider=IntegrationType.bigquery,
            database_name=TEST_DB,
            tables=tables,
            credentials=None,
            kb_name=TEST_KB
        )

        # Assert
        assert schema_none.credentials is None

    def test_database_schema_nested_validation(self):
        """Test DatabaseSchema validates nested TableMetadata objects."""
        # Arrange
        invalid_table_data = {
            "table_name": TEST_TABLE
            # Missing required columns field
        }

        # Act & Assert
        with pytest.raises(ValidationError):
            DatabaseSchema(
                provider=IntegrationType.bigquery,
                database_name=TEST_DB,
                tables=[invalid_table_data],
                kb_name=TEST_KB
            )

    def test_database_schema_dict_conversion(self):
        """Test DatabaseSchema can be converted to dictionary."""
        # Arrange
        tables = [
            TableMetadata(
                table_name=USERS_TABLE,
                columns=[ColumnMetadata(column_name=ID_COLUMN, data_type=INTEGER_TYPE)]
            )
        ]
        schema = DatabaseSchema(
            provider=IntegrationType.bigquery,
            database_name=TEST_DB,
            tables=tables,
            knowledge_base_id=KB_ID_123,
            description=TEST_SCHEMA_DESCRIPTION,
            credentials={"project_id": "test"},
            team_id=TEAM_1,
            kb_name=TEST_KB
        )

        # Act
        schema_dict = schema.model_dump()

        # Assert
        assert schema_dict["provider"] == IntegrationType.bigquery
        assert schema_dict["database_name"] == TEST_DB
        assert schema_dict["knowledge_base_id"] == KB_ID_123
        assert schema_dict["description"] == TEST_SCHEMA_DESCRIPTION
        assert schema_dict["credentials"] == {"project_id": "test"}
        assert schema_dict["team_id"] == TEAM_1
        assert schema_dict["kb_name"] == TEST_KB
        assert len(schema_dict["tables"]) == 1

    def test_database_schema_from_dict(self):
        """Test creating DatabaseSchema from dictionary."""
        # Arrange
        data = {
            "provider": IntegrationType.bigquery,
            "database_name": TEST_DB,
            "tables": [
                {
                    "table_name": USERS_TABLE,
                    "columns": [
                        {"column_name": ID_COLUMN, "data_type": INTEGER_TYPE}
                    ]
                }
            ],
            "kb_name": TEST_KB
        }

        # Act
        schema = DatabaseSchema(**data)

        # Assert
        assert schema.provider == IntegrationType.bigquery
        assert schema.database_name == TEST_DB
        assert len(schema.tables) == 1
        assert schema.tables[0].table_name == USERS_TABLE
        assert len(schema.tables[0].columns) == 1
        assert schema.tables[0].columns[0].column_name == ID_COLUMN
        assert schema.kb_name == TEST_KB

    def test_database_schema_complex_nested_structure(self):
        """Test DatabaseSchema with complex nested structure."""
        # Arrange
        columns = [
            ColumnMetadata(
                column_name=USER_ID_COLUMN,
                description=PRIMARY_KEY_DESCRIPTION,
                data_type=INTEGER_TYPE
            ),
            ColumnMetadata(
                column_name=EMAIL_COLUMN,
                description=EMAIL_DESCRIPTION,
                data_type=STRING_TYPE
            ),
            ColumnMetadata(
                column_name=CREATED_AT_COLUMN,
                description=ACCOUNT_CREATION_DESCRIPTION,
                data_type=TIMESTAMP_TYPE
            )
        ]

        tables = [
            TableMetadata(
                table_name=USERS_TABLE,
                table_description=USER_ACCOUNT_INFO,
                columns=columns[:2]
            ),
            TableMetadata(
                table_name=USER_ACTIVITY_TABLE,
                table_description=USER_ACTIVITY_DESCRIPTION,
                columns=[columns[0], columns[2]]
            )
        ]

        # Act
        schema = DatabaseSchema(
            provider=IntegrationType.bigquery,
            database_name=USER_ANALYTICS_DB,
            tables=tables,
            knowledge_base_id=KB_ID_789,
            description=USER_ANALYTICS_DESCRIPTION,
            credentials={
                "project_id": ANALYTICS_PROJECT,
                "type": "service_account",
                "private_key_id": KEY_123
            },
            team_id=ANALYTICS_TEAM,
            kb_name=USER_ANALYTICS_KB
        )

        # Assert
        assert schema.provider == IntegrationType.bigquery
        assert schema.database_name == USER_ANALYTICS_DB
        assert len(schema.tables) == 2
        assert schema.knowledge_base_id == KB_ID_789
        assert schema.description == USER_ANALYTICS_DESCRIPTION
        assert schema.credentials["project_id"] == ANALYTICS_PROJECT
        assert schema.team_id == ANALYTICS_TEAM
        assert schema.kb_name == USER_ANALYTICS_KB

        # Verify nested structure
        users_table = schema.tables[0]
        assert users_table.table_name == USERS_TABLE
        assert users_table.table_description == USER_ACCOUNT_INFO
        assert len(users_table.columns) == 2
        assert users_table.columns[0].column_name == USER_ID_COLUMN
        assert users_table.columns[0].description == PRIMARY_KEY_DESCRIPTION
        assert users_table.columns[1].column_name == EMAIL_COLUMN

        activity_table = schema.tables[1]
        assert activity_table.table_name == USER_ACTIVITY_TABLE
        assert activity_table.table_description == USER_ACTIVITY_DESCRIPTION
        assert len(activity_table.columns) == 2
        assert activity_table.columns[0].column_name == USER_ID_COLUMN
        assert activity_table.columns[1].column_name == CREATED_AT_COLUMN 