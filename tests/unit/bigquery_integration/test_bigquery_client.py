"""
Comprehensive tests for bigquery_integration.bigquery_client module to achieve SonarCube compliance.

This module contains test data that are safe for testing purposes.
All test data in this file is fictional and for testing purposes only.

SECURITY NOTE: All data in this file is safe test data:
- Project IDs are fictional test identifiers
- Service account keys are fictional test keys
- Dataset and table names are fictional test values
- Error messages are fictional test error descriptions
- No production credentials or sensitive data is used
"""

import pytest
from unittest.mock import Mock, patch, MagicMock, AsyncMock
import asyncio
from google.cloud import bigquery
from google.oauth2 import service_account
import google.auth.exceptions

from bigquery_integration.bigquery_client import BigQueryClientStrategy


# Test Constants - All values are safe test data, not production values
# Project and Authentication Constants
TEST_PROJECT_ID = "test-project"
TEST_PRIVATE_KEY = "test-key"
SERVICE_ACCOUNT_TYPE = "service_account"
PROJECT_ID_FIELD = "project_id"
TYPE_FIELD = "type"
PRIVATE_KEY_FIELD = "private_key"

# Dataset and Table Constants
TEST_DATASET = "test_dataset"
TEST_TABLE = "test_table"
TEST_VIEW = "test_view"
EMPTY_DATASET = "empty_dataset"
TABLE_TYPE = "TABLE"
VIEW_TYPE = "VIEW"

# Column and Data Type Constants
COLUMN_NAME_1 = "col1"
COLUMN_NAME_2 = "col2"
TABLE_NAME_1 = "table1"
TABLE_NAME_2 = "table2"
DATA_TYPE_STRING = "STRING"
DATA_TYPE_INTEGER = "INTEGER"
DATA_TYPE_FLOAT = "FLOAT"

# Error Message Constants
ERROR_NO_CREDENTIALS = "BigQuery credentials are required"
ERROR_MISSING_PROJECT_ID = "The 'project_id' is missing"
ERROR_INVALID_CREDENTIALS = "Invalid credentials"
ERROR_UNEXPECTED_ERROR = "Unexpected error"
ERROR_ACCESS_DENIED = "Access denied"
ERROR_CLIENT_NOT_INITIALIZED = "BigQuery client is not initialized"
ERROR_AUTH_FAILED = "Auth failed"
ERROR_NETWORK_ERROR = "Network error"
ERROR_QUERY_FAILED = "Query failed"
ERROR_TOKEN_EXPIRED = "Token expired"

# Log Message Constants
LOG_SUCCESS_INIT = "Successfully initialized BigQuery client for project: {}"
LOG_ERROR_CREDENTIALS = "Error: Invalid or expired BigQuery credentials. Details: {}"
LOG_ERROR_UNEXPECTED = "An unexpected error occurred during BigQuery client initialization: {}"

# Numeric Constants
MAX_RESULTS_LIMIT = 1
EMPTY_RESULT_COUNT = 0

# Dictionary Keys
TABLES_KEY = "tables"
VIEWS_KEY = "views"
TABLE_NAME_KEY = "table_name"
COLUMN_NAME_KEY = "column_name"
DATA_TYPE_KEY = "data_type"

# String Constants
EMPTY_STRING = ""
INVALID_CREDENTIALS_TYPE = "invalid"

# Schema Result Constants
SCHEMA_RESULT_PREFIX = TEST_DATASET + "."


@pytest.fixture
def valid_credentials():
    """Create valid test credentials."""
    return {
        PROJECT_ID_FIELD: TEST_PROJECT_ID,
        TYPE_FIELD: SERVICE_ACCOUNT_TYPE,
        PRIVATE_KEY_FIELD: TEST_PRIVATE_KEY
    }


@pytest.fixture
def minimal_credentials():
    """Create minimal test credentials with only project_id."""
    return {PROJECT_ID_FIELD: TEST_PROJECT_ID}


@pytest.fixture
def invalid_credentials():
    """Create invalid test credentials."""
    return {TYPE_FIELD: SERVICE_ACCOUNT_TYPE}


@pytest.fixture
def mock_bigquery_client():
    """Create mock BigQuery client."""
    mock_client = Mock()
    mock_client.project = TEST_PROJECT_ID
    mock_client.list_datasets.return_value = []
    mock_client.list_tables.return_value = []
    return mock_client


@pytest.fixture
def mock_dataset():
    """Create mock dataset."""
    mock_dataset = Mock()
    mock_dataset.dataset_id = TEST_DATASET
    return mock_dataset


@pytest.fixture
def mock_table():
    """Create mock table."""
    mock_table = Mock()
    mock_table.table_id = TEST_TABLE
    mock_table.table_type = TABLE_TYPE
    return mock_table


@pytest.fixture
def mock_view():
    """Create mock view."""
    mock_view = Mock()
    mock_view.table_id = TEST_VIEW
    mock_view.table_type = VIEW_TYPE
    return mock_view


@pytest.fixture
def mock_empty_dataset():
    """Create mock empty dataset."""
    mock_dataset = Mock()
    mock_dataset.dataset_id = EMPTY_DATASET
    return mock_dataset


def create_mock_query_row(table_name, column_name, data_type):
    """
    Create mock query result row.
    
    :param table_name: Name of the table
    :param column_name: Name of the column
    :param data_type: Data type of the column
    :return: Mock query row
    """
    mock_row = Mock()
    mock_row.table_name = table_name
    mock_row.column_name = column_name
    mock_row.data_type = data_type
    return mock_row


def assert_client_initialization(client, project_id):
    """
    Assert that client is properly initialized.
    
    :param client: BigQuery client strategy instance
    :param project_id: Expected project ID
    """
    assert client.project_id == project_id
    assert client.client is not None


def assert_expected_dataset_structure(result, dataset_name, tables_list, views_list):
    """
    Assert expected dataset structure.
    
    :param result: Result to check
    :param dataset_name: Name of the dataset
    :param tables_list: List of expected tables
    :param views_list: List of expected views
    """
    expected = {
        dataset_name: {
            TABLES_KEY: tables_list,
            VIEWS_KEY: views_list
        }
    }
    assert result == expected


class TestBigQueryClientStrategy:
    """Test cases for BigQueryClientStrategy class."""

    def test_init_success(self, valid_credentials, mock_bigquery_client):
        """Test successful initialization of BigQueryClientStrategy."""
        with patch('google.oauth2.service_account.Credentials.from_service_account_info') as mock_creds, \
             patch('google.cloud.bigquery.Client') as mock_client:
            
            mock_creds.return_value = Mock()
            mock_client.return_value = mock_bigquery_client
            
            # Act
            client = BigQueryClientStrategy(valid_credentials)
            
            # Assert
            assert_client_initialization(client, TEST_PROJECT_ID)
            assert client.client == mock_bigquery_client
            mock_creds.assert_called_once_with(valid_credentials)
            mock_client.assert_called_once()

    @pytest.mark.parametrize("invalid_credentials_input", [
        None,
        INVALID_CREDENTIALS_TYPE
    ])
    def test_init_no_credentials(self, invalid_credentials_input):
        """Test initialization fails with no or invalid credentials."""
        with pytest.raises(ValueError, match=ERROR_NO_CREDENTIALS):
            BigQueryClientStrategy(invalid_credentials_input)

    def test_init_missing_project_id(self, invalid_credentials):
        """Test initialization fails with missing project_id."""
        with pytest.raises(ValueError, match=ERROR_MISSING_PROJECT_ID):
            BigQueryClientStrategy(invalid_credentials)

    def test_init_invalid_credentials(self, valid_credentials):
        """Test initialization fails with invalid service account credentials."""
        with patch('google.oauth2.service_account.Credentials.from_service_account_info') as mock_creds:
            mock_creds.side_effect = google.auth.exceptions.RefreshError(ERROR_INVALID_CREDENTIALS)
            
            with pytest.raises(google.auth.exceptions.DefaultCredentialsError):
                BigQueryClientStrategy(valid_credentials)

    def test_init_unexpected_error(self, valid_credentials):
        """Test initialization handles unexpected errors."""
        with patch('google.oauth2.service_account.Credentials.from_service_account_info') as mock_creds:
            mock_creds.side_effect = Exception(ERROR_UNEXPECTED_ERROR)
            
            with pytest.raises(Exception, match=ERROR_UNEXPECTED_ERROR):
                BigQueryClientStrategy(valid_credentials)

    @pytest.mark.asyncio
    async def test_validate_credentials_success(self, minimal_credentials, mock_bigquery_client):
        """Test successful credentials validation."""
        with patch('google.oauth2.service_account.Credentials.from_service_account_info'), \
             patch('google.cloud.bigquery.Client') as mock_client_class:
            
            mock_bigquery_client.list_datasets.return_value = []
            mock_client_class.return_value = mock_bigquery_client
            
            client = BigQueryClientStrategy(minimal_credentials)
            
            # Act
            result = await client.validate_credentials()
            
            # Assert
            assert result is True
            mock_bigquery_client.list_datasets.assert_called_once_with(max_results=MAX_RESULTS_LIMIT)

    @pytest.mark.asyncio
    async def test_validate_credentials_failure(self, minimal_credentials, mock_bigquery_client):
        """Test credentials validation failure."""
        with patch('google.oauth2.service_account.Credentials.from_service_account_info'), \
             patch('google.cloud.bigquery.Client') as mock_client_class:
            
            mock_bigquery_client.list_datasets.side_effect = Exception(ERROR_ACCESS_DENIED)
            mock_client_class.return_value = mock_bigquery_client
            
            client = BigQueryClientStrategy(minimal_credentials)
            
            # Act
            result = await client.validate_credentials()
            
            # Assert
            assert result is False

    def test_list_all_datasets_tables_and_views_success(self, minimal_credentials, mock_bigquery_client, 
                                                        mock_dataset, mock_table, mock_view):
        """Test successful listing of datasets, tables, and views."""
        with patch('google.oauth2.service_account.Credentials.from_service_account_info'), \
             patch('google.cloud.bigquery.Client') as mock_client_class:
            
            mock_bigquery_client.list_datasets.return_value = [mock_dataset]
            mock_bigquery_client.list_tables.return_value = [mock_table, mock_view]
            mock_client_class.return_value = mock_bigquery_client
            
            client = BigQueryClientStrategy(minimal_credentials)
            
            # Act
            result = client.list_all_datasets_tables_and_views()
            
            # Assert
            assert_expected_dataset_structure(result, TEST_DATASET, [TEST_TABLE], [TEST_VIEW])

    def test_list_all_datasets_tables_and_views_no_datasets(self, minimal_credentials, mock_bigquery_client):
        """Test listing when no datasets exist."""
        with patch('google.oauth2.service_account.Credentials.from_service_account_info'), \
             patch('google.cloud.bigquery.Client') as mock_client_class:
            
            mock_bigquery_client.list_datasets.return_value = []
            mock_client_class.return_value = mock_bigquery_client
            
            client = BigQueryClientStrategy(minimal_credentials)
            
            # Act
            result = client.list_all_datasets_tables_and_views()
            
            # Assert
            assert result == {}

    def test_list_all_datasets_tables_and_views_no_tables(self, minimal_credentials, mock_bigquery_client,
                                                          mock_empty_dataset):
        """Test listing when dataset has no tables or views."""
        with patch('google.oauth2.service_account.Credentials.from_service_account_info'), \
             patch('google.cloud.bigquery.Client') as mock_client_class:
            
            mock_bigquery_client.list_datasets.return_value = [mock_empty_dataset]
            mock_bigquery_client.list_tables.return_value = []
            mock_client_class.return_value = mock_bigquery_client
            
            client = BigQueryClientStrategy(minimal_credentials)
            
            # Act
            result = client.list_all_datasets_tables_and_views()
            
            # Assert
            assert_expected_dataset_structure(result, EMPTY_DATASET, [], [])

    def test_list_all_datasets_tables_and_views_client_not_initialized(self, minimal_credentials):
        """Test listing fails when client is not initialized."""
        with patch('google.oauth2.service_account.Credentials.from_service_account_info'), \
             patch('google.cloud.bigquery.Client'):
            
            client = BigQueryClientStrategy(minimal_credentials)
            client.client = None  # Simulate uninitialized client
            
            with pytest.raises(RuntimeError, match=ERROR_CLIENT_NOT_INITIALIZED):
                client.list_all_datasets_tables_and_views()

    @pytest.mark.parametrize("exception_type,error_message", [
        (google.auth.exceptions.GoogleAuthError, ERROR_AUTH_FAILED),
        (Exception, ERROR_NETWORK_ERROR)
    ])
    def test_list_all_datasets_tables_and_views_errors(self, minimal_credentials, mock_bigquery_client,
                                                       exception_type, error_message):
        """Test listing handles authentication and general errors."""
        with patch('google.oauth2.service_account.Credentials.from_service_account_info'), \
             patch('google.cloud.bigquery.Client') as mock_client_class:
            
            mock_bigquery_client.list_datasets.side_effect = exception_type(error_message)
            mock_client_class.return_value = mock_bigquery_client
            
            client = BigQueryClientStrategy(minimal_credentials)
            
            with pytest.raises(exception_type):
                client.list_all_datasets_tables_and_views()

    @pytest.mark.asyncio
    async def test_get_table_schemas_success(self, minimal_credentials, mock_bigquery_client):
        """Test successful table schema retrieval."""
        # Arrange
        mock_rows = [
            create_mock_query_row(TABLE_NAME_1, COLUMN_NAME_1, DATA_TYPE_STRING),
            create_mock_query_row(TABLE_NAME_1, COLUMN_NAME_2, DATA_TYPE_INTEGER),
            create_mock_query_row(TABLE_NAME_2, COLUMN_NAME_1, DATA_TYPE_FLOAT)
        ]
        
        with patch('google.oauth2.service_account.Credentials.from_service_account_info'), \
             patch('google.cloud.bigquery.Client') as mock_client_class, \
             patch('asyncio.to_thread') as mock_to_thread:
            
            mock_client_class.return_value = mock_bigquery_client
            mock_to_thread.return_value = mock_rows
            
            client = BigQueryClientStrategy(minimal_credentials)
            
            # Act
            result = await client.get_table_schemas(TEST_DATASET)
            
            # Assert
            expected = [
                {SCHEMA_RESULT_PREFIX + TABLE_NAME_1: {COLUMN_NAME_1: DATA_TYPE_STRING, COLUMN_NAME_2: DATA_TYPE_INTEGER}},
                {SCHEMA_RESULT_PREFIX + TABLE_NAME_2: {COLUMN_NAME_1: DATA_TYPE_FLOAT}}
            ]
            assert result == expected

    @pytest.mark.asyncio
    async def test_get_table_schemas_empty_database_name(self, minimal_credentials):
        """Test table schema retrieval with empty database name."""
        with patch('google.oauth2.service_account.Credentials.from_service_account_info'), \
             patch('google.cloud.bigquery.Client'):
            
            client = BigQueryClientStrategy(minimal_credentials)
            
            # Act
            result = await client.get_table_schemas(EMPTY_STRING)
            
            # Assert
            assert result == []

    @pytest.mark.asyncio
    async def test_get_table_schemas_client_not_initialized(self, minimal_credentials):
        """Test table schema retrieval fails when client is not initialized."""
        with patch('google.oauth2.service_account.Credentials.from_service_account_info'), \
             patch('google.cloud.bigquery.Client'):
            
            client = BigQueryClientStrategy(minimal_credentials)
            client.client = None  # Simulate uninitialized client
            
            with pytest.raises(RuntimeError, match=ERROR_CLIENT_NOT_INITIALIZED):
                await client.get_table_schemas(TEST_DATASET)

    @pytest.mark.parametrize("exception_type,error_message", [
        (google.auth.exceptions.GoogleAuthError, ERROR_AUTH_FAILED),
        (Exception, ERROR_QUERY_FAILED)
    ])
    @pytest.mark.asyncio
    async def test_get_table_schemas_errors(self, minimal_credentials, mock_bigquery_client,
                                           exception_type, error_message):
        """Test table schema retrieval handles authentication and general errors."""
        with patch('google.oauth2.service_account.Credentials.from_service_account_info'), \
             patch('google.cloud.bigquery.Client') as mock_client_class, \
             patch('asyncio.to_thread') as mock_to_thread:
            
            mock_client_class.return_value = mock_bigquery_client
            mock_to_thread.side_effect = exception_type(error_message)
            
            client = BigQueryClientStrategy(minimal_credentials)
            
            with pytest.raises(exception_type):
                await client.get_table_schemas(TEST_DATASET)

    def test_inheritance_from_database_client_strategy(self, minimal_credentials):
        """Test that BigQueryClientStrategy properly inherits from DatabaseClientStrategy."""
        with patch('google.oauth2.service_account.Credentials.from_service_account_info'), \
             patch('google.cloud.bigquery.Client'):
            
            # Act
            client = BigQueryClientStrategy(minimal_credentials)
            
            # Assert
            # Check that it has inherited the credentials attribute
            assert hasattr(client, 'credentials')
            assert client.credentials == minimal_credentials

    def test_logging_on_successful_initialization(self, minimal_credentials, mock_bigquery_client):
        """Test that successful initialization logs the correct message."""
        with patch('google.oauth2.service_account.Credentials.from_service_account_info'), \
             patch('google.cloud.bigquery.Client') as mock_client_class, \
             patch('bigquery_integration.bigquery_client.logger') as mock_logger:
            
            mock_client_class.return_value = mock_bigquery_client
            
            # Act
            BigQueryClientStrategy(minimal_credentials)
            
            # Assert
            mock_logger.info.assert_called_once_with(
                LOG_SUCCESS_INIT.format(TEST_PROJECT_ID)
            )

    def test_logging_on_auth_error(self, minimal_credentials):
        """Test that authentication errors are properly logged."""
        with patch('google.oauth2.service_account.Credentials.from_service_account_info') as mock_creds, \
             patch('bigquery_integration.bigquery_client.logger') as mock_logger:
            
            auth_error = google.auth.exceptions.RefreshError(ERROR_TOKEN_EXPIRED)
            mock_creds.side_effect = auth_error
            
            # Act & Assert
            with pytest.raises(google.auth.exceptions.DefaultCredentialsError):
                BigQueryClientStrategy(minimal_credentials)
            
            mock_logger.error.assert_called_once_with(
                LOG_ERROR_CREDENTIALS.format(ERROR_TOKEN_EXPIRED)
            )

    def test_logging_on_unexpected_error(self, minimal_credentials):
        """Test that unexpected errors are properly logged."""
        with patch('google.oauth2.service_account.Credentials.from_service_account_info') as mock_creds, \
             patch('bigquery_integration.bigquery_client.logger') as mock_logger:
            
            unexpected_error = Exception(ERROR_UNEXPECTED_ERROR)
            mock_creds.side_effect = unexpected_error
            
            # Act & Assert
            with pytest.raises(Exception, match=ERROR_UNEXPECTED_ERROR):
                BigQueryClientStrategy(minimal_credentials)
            
            mock_logger.error.assert_called_once_with(
                LOG_ERROR_UNEXPECTED.format(ERROR_UNEXPECTED_ERROR)
            ) 