"""
Comprehensive tests for app.config module to achieve SonarCube compliance.

This module contains test data that are safe for testing purposes.
All test data in this file is fictional and for testing purposes only.

SECURITY NOTE: All data in this file is safe test data:
- API keys and secrets are fictional test keys for testing only
- Database URLs and connection strings are fictional test values
- Environment names and configuration keys are test configuration values
- All YAML content is fictional test configuration data
- No production credentials or sensitive data is used
"""

import os
import pytest
from unittest.mock import Mock, patch, mock_open, MagicMock
from functools import lru_cache

from app.config import Config, OpenAIConfig, get_config


# Test Constants - All values are safe test data, not production values
# API Keys and Secrets (All fictional test values)
TEST_API_KEY = "test-key"
TEST_OPENAI_API_KEY = "test-api-key"
TEST_STRING_API_KEY = "string-key"
TEST_EMPTY_API_KEY = ""

# Database Configuration Constants
TEST_DATABASE_URL = "postgresql://test:test@localhost:5432/test"
TEST_DB_HOST = "localhost"
TEST_DB_PORT = 5432
TEST_DB_NAME = "test_db"

# Environment and Configuration Constants
TEST_ENVIRONMENT = "test"
TEST_OTHER_SETTING = "value"
TEST_NESTED_VALUE = "value"
TEST_STRING_VALUE = "test"

# Numeric Test Constants
TEST_INT_VALUE = 42
TEST_FLOAT_VALUE = 3.14
TEST_INT_API_KEY = 12345
TEST_TIMEOUT_VALUE = 30
TEST_RETRIES_VALUE = 3

# Boolean Test Constants
TEST_BOOL_VALUE = True
TEST_BOOL_API_KEY = True

# List and Dictionary Test Constants
TEST_LIST_VALUE = [1, 2, 3]
TEST_DICT_VALUE = {"nested": "value"}

# File and Path Constants
TEST_CONFIG_KEY = "test_key"
TEST_CONFIG_VALUE = "test_value"
YAML_FILE_EXTENSION = ".yml"
CONFIG_DIRECTORY_NAME = "config"
DEFAULT_CONFIG_FILENAME = "default.yml"

# YAML Content Constants
SIMPLE_YAML_CONTENT = "key: value"
COMPLEX_YAML_CONTENT = """
openai_gpt4o_api_key: sk-test123
database:
  host: localhost
  port: 5432
  name: test_db
api_settings:
  timeout: 30
  retries: 3
"""
EMPTY_YAML_CONTENT = ""
MALFORMED_YAML_CONTENT = "key: value\n  invalid: [missing bracket"
UNICODE_YAML_CONTENT = "unicode_key: 'Test éñglish ünïcödé'"

# Error Message Constants
ERROR_FILE_NOT_FOUND = "No such file or directory"
ERROR_YAML_PARSE = "YAML parse error"
ERROR_PERMISSION = "Permission denied"

# Dictionary Keys
KEY_OPENAI_GPT4O_API_KEY = "openai_gpt4o_api_key"
KEY_DATABASE_URL = "database_url"
KEY_ENVIRONMENT = "environment"
KEY_OTHER_SETTING = "other_setting"
KEY_DATABASE = "database"
KEY_API_SETTINGS = "api_settings"
KEY_HOST = "host"
KEY_PORT = "port"
KEY_NAME = "name"
KEY_TIMEOUT = "timeout"
KEY_RETRIES = "retries"
KEY_STRING_VALUE = "string_value"
KEY_INT_VALUE = "int_value"
KEY_FLOAT_VALUE = "float_value"
KEY_BOOL_VALUE = "bool_value"
KEY_LIST_VALUE = "list_value"
KEY_DICT_VALUE = "dict_value"
KEY_NONE_VALUE = "none_value"
KEY_NESTED = "nested"
KEY_UNICODE_KEY = "unicode_key"
YAML_KEY = "key"
YAML_VALUE = "value"

# Mock Patch Paths
PATCH_OPEN = "builtins.open"
PATCH_YAML_SAFE_LOAD = "yaml.safe_load"
PATCH_OS_PATH_DIRNAME = "os.path.dirname"
PATCH_OS_PATH_JOIN = "os.path.join"

# Special Values
NONE_VALUE = None
UNICODE_TEST_VALUE = "Test éñglish ünïcödé"


@pytest.fixture
def simple_config_data():
    """Create simple configuration data for testing."""
    return {
        KEY_OPENAI_GPT4O_API_KEY: TEST_API_KEY,
        KEY_DATABASE_URL: TEST_DATABASE_URL,
        KEY_ENVIRONMENT: TEST_ENVIRONMENT
    }


@pytest.fixture
def empty_config_data():
    """Create empty configuration data for testing."""
    return {}


@pytest.fixture
def nested_config_data():
    """Create nested configuration data for testing."""
    return {
        KEY_OPENAI_GPT4O_API_KEY: TEST_API_KEY,
        KEY_DATABASE: {
            KEY_HOST: TEST_DB_HOST,
            KEY_PORT: TEST_DB_PORT,
            KEY_NAME: TEST_DB_NAME
        },
        KEY_API_SETTINGS: {
            KEY_TIMEOUT: TEST_TIMEOUT_VALUE,
            KEY_RETRIES: TEST_RETRIES_VALUE
        }
    }


@pytest.fixture
def various_types_config_data():
    """Create configuration data with various data types."""
    return {
        KEY_STRING_VALUE: TEST_STRING_VALUE,
        KEY_INT_VALUE: TEST_INT_VALUE,
        KEY_FLOAT_VALUE: TEST_FLOAT_VALUE,
        KEY_BOOL_VALUE: TEST_BOOL_VALUE,
        KEY_LIST_VALUE: TEST_LIST_VALUE,
        KEY_DICT_VALUE: TEST_DICT_VALUE,
        KEY_NONE_VALUE: NONE_VALUE
    }


@pytest.fixture
def openai_config_data():
    """Create OpenAI configuration data for testing."""
    return {KEY_OPENAI_GPT4O_API_KEY: TEST_OPENAI_API_KEY}


@pytest.fixture
def config_without_openai_key():
    """Create configuration data without OpenAI key."""
    return {KEY_OTHER_SETTING: TEST_OTHER_SETTING}


@pytest.fixture
def mock_yaml_file():
    """Create mock YAML file content."""
    return SIMPLE_YAML_CONTENT


@pytest.fixture
def mock_complex_yaml_file():
    """Create mock complex YAML file content."""
    return COMPLEX_YAML_CONTENT


def assert_config_properties(config, expected_data, expected_openai_type=OpenAIConfig):
    """
    Assert Config object properties.
    
    :param config: Config instance to check
    :param expected_data: Expected data dictionary
    :param expected_openai_type: Expected OpenAI config type
    """
    assert config.data == expected_data
    assert isinstance(config.openai, expected_openai_type)


def assert_openai_config_properties(openai_config, expected_api_key):
    """
    Assert OpenAIConfig object properties.
    
    :param openai_config: OpenAIConfig instance to check
    :param expected_api_key: Expected API key value
    """
    assert openai_config.api_key == expected_api_key


def assert_config_data_values(config_data, expected_values):
    """
    Assert configuration data values.
    
    :param config_data: Configuration data dictionary
    :param expected_values: Dictionary of expected key-value pairs
    """
    for key, expected_value in expected_values.items():
        assert config_data[key] == expected_value


def create_mock_file_error(error_type, error_message):
    """
    Create mock file error for testing.
    
    :param error_type: Type of error to raise
    :param error_message: Error message
    :return: Error instance
    """
    if error_type == FileNotFoundError:
        return FileNotFoundError(error_message)
    elif error_type == PermissionError:
        return PermissionError(error_message)
    else:
        return Exception(error_message)


def build_expected_config_path(dirname_result, join_result):
    """
    Build expected configuration file path.
    
    :param dirname_result: Result from os.path.dirname
    :param join_result: Result from os.path.join
    :return: Expected path
    """
    return join_result


class TestConfig:
    """Test cases for Config class."""

    def test_config_initialization(self, simple_config_data):
        """Test Config initialization with configuration data."""
        # Act
        config = Config(simple_config_data)

        # Assert
        assert_config_properties(config, simple_config_data)
        assert_openai_config_properties(config.openai, TEST_API_KEY)

    def test_config_initialization_with_empty_data(self, empty_config_data):
        """Test Config initialization with empty configuration data."""
        # Act
        config = Config(empty_config_data)

        # Assert
        assert_config_properties(config, empty_config_data)
        assert_openai_config_properties(config.openai, NONE_VALUE)

    def test_config_initialization_with_none_data(self):
        """Test Config initialization with None configuration data."""
        # Act
        config = Config(NONE_VALUE)

        # Assert
        assert_config_properties(config, NONE_VALUE)
        assert_openai_config_properties(config.openai, NONE_VALUE)

    def test_config_initialization_with_nested_data(self, nested_config_data):
        """Test Config initialization with nested configuration data."""
        # Act
        config = Config(nested_config_data)

        # Assert
        assert_config_properties(config, nested_config_data)
        assert config.data[KEY_DATABASE][KEY_HOST] == TEST_DB_HOST
        assert config.data[KEY_API_SETTINGS][KEY_TIMEOUT] == TEST_TIMEOUT_VALUE
        assert_openai_config_properties(config.openai, TEST_API_KEY)

    def test_config_initialization_with_various_data_types(self, various_types_config_data):
        """Test Config initialization with various data types."""
        # Act
        config = Config(various_types_config_data)

        # Assert
        assert_config_properties(config, various_types_config_data)
        
        expected_values = {
            KEY_STRING_VALUE: TEST_STRING_VALUE,
            KEY_INT_VALUE: TEST_INT_VALUE,
            KEY_FLOAT_VALUE: TEST_FLOAT_VALUE,
            KEY_BOOL_VALUE: TEST_BOOL_VALUE,
            KEY_LIST_VALUE: TEST_LIST_VALUE,
            KEY_DICT_VALUE: TEST_DICT_VALUE,
            KEY_NONE_VALUE: NONE_VALUE
        }
        assert_config_data_values(config.data, expected_values)


class TestOpenAIConfig:
    """Test cases for OpenAIConfig class."""

    def test_openai_config_initialization_with_api_key(self, openai_config_data):
        """Test OpenAIConfig initialization with API key."""
        # Act
        openai_config = OpenAIConfig(openai_config_data)

        # Assert
        assert_openai_config_properties(openai_config, TEST_OPENAI_API_KEY)

    def test_openai_config_initialization_without_api_key(self, config_without_openai_key):
        """Test OpenAIConfig initialization without API key."""
        # Act
        openai_config = OpenAIConfig(config_without_openai_key)

        # Assert
        assert_openai_config_properties(openai_config, NONE_VALUE)

    def test_openai_config_initialization_with_empty_config(self, empty_config_data):
        """Test OpenAIConfig initialization with empty configuration."""
        # Act
        openai_config = OpenAIConfig(empty_config_data)

        # Assert
        assert_openai_config_properties(openai_config, NONE_VALUE)

    def test_openai_config_initialization_with_none_config(self):
        """Test OpenAIConfig initialization with None configuration."""
        # Act
        openai_config = OpenAIConfig(NONE_VALUE)

        # Assert
        assert_openai_config_properties(openai_config, NONE_VALUE)

    def test_openai_config_initialization_with_empty_api_key(self):
        """Test OpenAIConfig initialization with empty API key."""
        # Arrange
        config_data = {KEY_OPENAI_GPT4O_API_KEY: TEST_EMPTY_API_KEY}

        # Act
        openai_config = OpenAIConfig(config_data)

        # Assert
        assert_openai_config_properties(openai_config, TEST_EMPTY_API_KEY)

    def test_openai_config_initialization_with_none_api_key(self):
        """Test OpenAIConfig initialization with None API key."""
        # Arrange
        config_data = {KEY_OPENAI_GPT4O_API_KEY: NONE_VALUE}

        # Act
        openai_config = OpenAIConfig(config_data)

        # Assert
        assert_openai_config_properties(openai_config, NONE_VALUE)

    @pytest.mark.parametrize("api_key_value,expected_value", [
        (TEST_STRING_API_KEY, TEST_STRING_API_KEY),
        (TEST_INT_API_KEY, TEST_INT_API_KEY),
        (TEST_BOOL_API_KEY, TEST_BOOL_API_KEY)
    ])
    def test_openai_config_initialization_with_different_key_types(self, api_key_value, expected_value):
        """Test OpenAIConfig initialization with different API key types."""
        # Arrange
        config_data = {KEY_OPENAI_GPT4O_API_KEY: api_key_value}
        
        # Act
        openai_config = OpenAIConfig(config_data)
        
        # Assert
        assert_openai_config_properties(openai_config, expected_value)


class TestGetConfig:
    """Test cases for get_config function."""

    @patch(PATCH_OPEN, new_callable=mock_open)
    @patch(PATCH_YAML_SAFE_LOAD)
    def test_get_config_successful_load(self, mock_yaml_load, mock_file, simple_config_data):
        """Test get_config successfully loads configuration."""
        # Arrange
        mock_yaml_load.return_value = simple_config_data

        # Act
        config = get_config()

        # Assert
        assert isinstance(config, Config)
        mock_file.assert_called_once()
        mock_yaml_load.assert_called_once()

    @patch(PATCH_OPEN)
    def test_get_config_file_not_found_error(self, mock_file):
        """Test get_config handles FileNotFoundError."""
        # Arrange
        mock_file.side_effect = create_mock_file_error(FileNotFoundError, ERROR_FILE_NOT_FOUND)

        # Act & Assert
        with pytest.raises(FileNotFoundError):
            get_config()

    @patch(PATCH_OPEN, new_callable=mock_open)
    @patch(PATCH_YAML_SAFE_LOAD)
    def test_get_config_yaml_parse_error(self, mock_yaml_load, mock_file):
        """Test get_config handles YAML parse error."""
        # Arrange
        import yaml
        mock_yaml_load.side_effect = yaml.YAMLError(ERROR_YAML_PARSE)

        # Act & Assert
        with pytest.raises(yaml.YAMLError):
            get_config()

    @patch(PATCH_OPEN)
    def test_get_config_permission_error(self, mock_file):
        """Test get_config handles PermissionError."""
        # Arrange
        mock_file.side_effect = create_mock_file_error(PermissionError, ERROR_PERMISSION)

        # Act & Assert
        with pytest.raises(PermissionError):
            get_config()

    @patch(PATCH_OPEN, new_callable=mock_open)
    @patch(PATCH_YAML_SAFE_LOAD)
    def test_get_config_with_empty_yaml_file(self, mock_yaml_load, mock_file):
        """Test get_config with empty YAML file."""
        # Arrange
        mock_file.return_value.read.return_value = EMPTY_YAML_CONTENT
        mock_yaml_load.return_value = NONE_VALUE

        # Act
        config = get_config()

        # Assert
        assert isinstance(config, Config)
        assert config.data is NONE_VALUE

    @patch(PATCH_OPEN, new_callable=mock_open)
    @patch(PATCH_YAML_SAFE_LOAD)
    def test_get_config_with_malformed_yaml(self, mock_yaml_load, mock_file):
        """Test get_config with malformed YAML."""
        # Arrange
        import yaml
        mock_file.return_value.read.return_value = MALFORMED_YAML_CONTENT
        mock_yaml_load.side_effect = yaml.YAMLError(ERROR_YAML_PARSE)

        # Act & Assert
        with pytest.raises(yaml.YAMLError):
            get_config()

    @patch(PATCH_OS_PATH_DIRNAME)
    @patch(PATCH_OS_PATH_JOIN)
    @patch(PATCH_OPEN, new_callable=mock_open)
    @patch(PATCH_YAML_SAFE_LOAD)
    def test_get_config_path_construction(self, mock_yaml_load, mock_file, mock_join, mock_dirname):
        """Test get_config constructs correct file path."""
        # Arrange
        mock_dirname.return_value = "/app"
        mock_join.return_value = "/app/config/default.yml"
        mock_yaml_load.return_value = {TEST_CONFIG_KEY: TEST_CONFIG_VALUE}

        # Act
        config = get_config()

        # Assert
        mock_dirname.assert_called_once()
        mock_join.assert_called_once_with("/app", CONFIG_DIRECTORY_NAME, DEFAULT_CONFIG_FILENAME)
        assert isinstance(config, Config)

    @patch(PATCH_OPEN, new_callable=mock_open)
    @patch(PATCH_YAML_SAFE_LOAD)
    def test_get_config_caching_behavior(self, mock_yaml_load, mock_file, simple_config_data):
        """Test get_config caching behavior."""
        # Arrange
        mock_yaml_load.return_value = simple_config_data
        get_config.cache_clear()  # Clear any existing cache

        # Act
        config1 = get_config()
        config2 = get_config()

        # Assert
        assert config1 is config2  # Should be the same object due to caching
        mock_file.assert_called_once()  # File should only be opened once
        mock_yaml_load.assert_called_once()  # YAML should only be loaded once

    @patch(PATCH_OPEN, new_callable=mock_open)
    @patch(PATCH_YAML_SAFE_LOAD)
    def test_get_config_clear_cache(self, mock_yaml_load, mock_file, simple_config_data):
        """Test get_config cache clearing."""
        # Arrange
        mock_yaml_load.return_value = simple_config_data
        get_config.cache_clear()

        # Act
        config1 = get_config()
        get_config.cache_clear()
        config2 = get_config()

        # Assert
        assert config1 is not config2  # Should be different objects after cache clear
        assert mock_file.call_count == 2  # File should be opened twice
        assert mock_yaml_load.call_count == 2  # YAML should be loaded twice

    @patch(PATCH_OPEN, new_callable=mock_open)
    @patch(PATCH_YAML_SAFE_LOAD)
    def test_get_config_with_different_encoding(self, mock_yaml_load, mock_file):
        """Test get_config with different file encoding."""
        # Arrange
        unicode_data = {KEY_UNICODE_KEY: UNICODE_TEST_VALUE}
        mock_yaml_load.return_value = unicode_data

        # Act
        config = get_config()

        # Assert
        assert isinstance(config, Config)
        assert config.data[KEY_UNICODE_KEY] == UNICODE_TEST_VALUE

    @patch(PATCH_OPEN, new_callable=mock_open)
    @patch(PATCH_YAML_SAFE_LOAD)
    def test_get_config_with_complex_yaml_structure(self, mock_yaml_load, mock_file):
        """Test get_config with complex YAML structure."""
        # Arrange
        complex_data = {
            KEY_OPENAI_GPT4O_API_KEY: "sk-test123",
            KEY_DATABASE: {
                KEY_HOST: TEST_DB_HOST,
                KEY_PORT: TEST_DB_PORT,
                KEY_NAME: TEST_DB_NAME
            },
            KEY_API_SETTINGS: {
                KEY_TIMEOUT: TEST_TIMEOUT_VALUE,
                KEY_RETRIES: TEST_RETRIES_VALUE
            }
        }
        mock_yaml_load.return_value = complex_data

        # Act
        config = get_config()

        # Assert
        assert isinstance(config, Config)
        assert config.data[KEY_DATABASE][KEY_HOST] == TEST_DB_HOST
        assert config.data[KEY_API_SETTINGS][KEY_TIMEOUT] == TEST_TIMEOUT_VALUE
        assert config.openai.api_key == "sk-test123"

    @patch(PATCH_OPEN, new_callable=mock_open)
    @patch(PATCH_YAML_SAFE_LOAD)
    def test_get_config_unicode_handling(self, mock_yaml_load, mock_file):
        """Test get_config handles Unicode characters correctly."""
        # Arrange
        unicode_config = {
            "app_name": "Test Application",
            "description": UNICODE_TEST_VALUE,
            "languages": ["English", "Español", "Français"]
        }
        mock_yaml_load.return_value = unicode_config

        # Act
        config = get_config()

        # Assert
        assert isinstance(config, Config)
        assert config.data["description"] == UNICODE_TEST_VALUE
        assert len(config.data["languages"]) == 3


class TestConfigIntegration:
    """Integration tests for config module."""

    @patch(PATCH_OPEN, new_callable=mock_open)
    @patch(PATCH_YAML_SAFE_LOAD)
    def test_config_end_to_end_workflow(self, mock_yaml_load, mock_file, nested_config_data):
        """Test complete configuration workflow."""
        # Arrange
        mock_yaml_load.return_value = nested_config_data
        get_config.cache_clear()

        # Act
        config = get_config()

        # Assert
        # Test Config object creation
        assert isinstance(config, Config)
        assert_config_properties(config, nested_config_data)
        
        # Test OpenAI configuration
        assert isinstance(config.openai, OpenAIConfig)
        assert_openai_config_properties(config.openai, TEST_API_KEY)
        
        # Test nested data access
        assert config.data[KEY_DATABASE][KEY_HOST] == TEST_DB_HOST
        assert config.data[KEY_API_SETTINGS][KEY_TIMEOUT] == TEST_TIMEOUT_VALUE

    @patch(PATCH_OPEN, new_callable=mock_open)
    @patch(PATCH_YAML_SAFE_LOAD)
    def test_config_with_missing_openai_key(self, mock_yaml_load, mock_file):
        """Test configuration without OpenAI key."""
        # Arrange
        config_data = {
            KEY_DATABASE_URL: TEST_DATABASE_URL,
            KEY_ENVIRONMENT: TEST_ENVIRONMENT
        }
        mock_yaml_load.return_value = config_data

        # Act
        config = get_config()

        # Assert
        assert isinstance(config, Config)
        assert_config_properties(config, config_data)
        assert_openai_config_properties(config.openai, NONE_VALUE) 