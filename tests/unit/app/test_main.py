"""
Comprehensive tests for app.main module to achieve SonarCube compliance.

This module contains test data that are safe for testing purposes.
All test data in this file is fictional and for testing purposes only.

SECURITY NOTE: All data in this file is safe test data:
- Host addresses and ports are fictional test configuration values
- Worker counts and server settings are test configuration values
- Debug settings are test configuration values
- All configuration values are for testing purposes only
- No production credentials or sensitive data is used
"""
import pytest
from unittest.mock import Mock, patch, call
import uvicorn

from app.main import main


# Test Constants - All values are safe test data, not production values
# Server Configuration Constants
TEST_HOST_DEFAULT = "0.0.0.0"
TEST_HOST_LOCALHOST = "localhost"
TEST_PORT_DEFAULT = 8080
TEST_PORT_STANDARD = 8000

# Worker Configuration Constants
TEST_WORKERS_COUNT_MULTIPLE = 4
TEST_WORKERS_COUNT_SINGLE = 1

# Debug Configuration Constants
TEST_DEBUG_TRUE = True
TEST_DEBUG_FALSE = False

# Reload Configuration Constants
TEST_RELOAD_TRUE = True
TEST_RELOAD_FALSE = False

# Application Constants
APP_FACTORY_PATH = "app.application:get_app"
FACTORY_PATTERN_TRUE = True

# Expected Call Constants
EXPECTED_CALL_COUNT_ONCE = 1
EXPECTED_CALL_COUNT_TWICE = 2

# None Value Constants
NONE_VALUE = None

# Mock Patch Paths
PATCH_UVICORN_RUN = 'app.main.uvicorn.run'
PATCH_LOADED_CONFIG = 'app.main.loaded_config'

# Assertion Messages
ASSERTION_FACTORY_TRUE = "Factory pattern should be enabled"
ASSERTION_CALLABLE = "Main function should be callable"
ASSERTION_DOCSTRING = "Main function should have docstring"
ASSERTION_RETURN_NONE = "Main function should return None"
DOCSTRING_CONTENT = "Starts the Uvicorn server"


@pytest.fixture
def mock_loaded_config_production():
    """Create mock loaded config for production-like environment."""
    mock_config = Mock()
    mock_config.workers_count = TEST_WORKERS_COUNT_MULTIPLE
    mock_config.host = TEST_HOST_DEFAULT
    mock_config.port = TEST_PORT_DEFAULT
    mock_config.debug = TEST_DEBUG_TRUE
    return mock_config


@pytest.fixture
def mock_loaded_config_development():
    """Create mock loaded config for development environment."""
    mock_config = Mock()
    mock_config.workers_count = TEST_WORKERS_COUNT_SINGLE
    mock_config.host = TEST_HOST_LOCALHOST
    mock_config.port = TEST_PORT_STANDARD
    mock_config.debug = TEST_DEBUG_FALSE
    return mock_config


@pytest.fixture
def mock_loaded_config_debug():
    """Create mock loaded config with debug enabled."""
    mock_config = Mock()
    mock_config.workers_count = TEST_WORKERS_COUNT_SINGLE
    mock_config.host = TEST_HOST_LOCALHOST
    mock_config.port = TEST_PORT_STANDARD
    mock_config.debug = TEST_DEBUG_TRUE
    return mock_config


@pytest.fixture
def mock_loaded_config_no_debug():
    """Create mock loaded config with debug disabled."""
    mock_config = Mock()
    mock_config.workers_count = TEST_WORKERS_COUNT_SINGLE
    mock_config.host = TEST_HOST_LOCALHOST
    mock_config.port = TEST_PORT_STANDARD
    mock_config.debug = TEST_DEBUG_FALSE
    return mock_config


@pytest.fixture
def mock_loaded_config_none_values():
    """Create mock loaded config with None values."""
    mock_config = Mock()
    mock_config.workers_count = NONE_VALUE
    mock_config.host = NONE_VALUE
    mock_config.port = NONE_VALUE
    mock_config.debug = NONE_VALUE
    return mock_config


def assert_uvicorn_run_called_with_params(mock_uvicorn_run, expected_params):
    """
    Assert uvicorn.run was called with expected parameters.
    
    :param mock_uvicorn_run: Mock uvicorn.run object
    :param expected_params: Dictionary of expected parameters
    """
    mock_uvicorn_run.assert_called_once_with(
        APP_FACTORY_PATH,
        workers=expected_params.get('workers'),
        host=expected_params.get('host'),
        port=expected_params.get('port'),
        reload=expected_params.get('reload'),
        factory=FACTORY_PATTERN_TRUE,
    )


def assert_uvicorn_call_count(mock_uvicorn_run, expected_count):
    """
    Assert uvicorn.run call count matches expected.
    
    :param mock_uvicorn_run: Mock uvicorn.run object
    :param expected_count: Expected call count
    """
    assert mock_uvicorn_run.call_count == expected_count


def assert_factory_pattern_enabled(mock_uvicorn_run):
    """
    Assert factory pattern is enabled in uvicorn call.
    
    :param mock_uvicorn_run: Mock uvicorn.run object
    """
    args, kwargs = mock_uvicorn_run.call_args
    assert kwargs['factory'] is FACTORY_PATTERN_TRUE, ASSERTION_FACTORY_TRUE
    assert args[0] == APP_FACTORY_PATH


def assert_reload_setting(mock_uvicorn_run, expected_reload):
    """
    Assert reload setting matches expected value.
    
    :param mock_uvicorn_run: Mock uvicorn.run object
    :param expected_reload: Expected reload value
    """
    args, kwargs = mock_uvicorn_run.call_args
    assert kwargs['reload'] is expected_reload


def build_expected_params(workers, host, port, reload):
    """
    Build expected parameters dictionary.
    
    :param workers: Worker count
    :param host: Host address
    :param port: Port number
    :param reload: Reload setting
    :return: Parameters dictionary
    """
    return {
        'workers': workers,
        'host': host,
        'port': port,
        'reload': reload
    }


class TestMain:
    """Test suite for app.main module."""

    @patch(PATCH_UVICORN_RUN)
    @patch(PATCH_LOADED_CONFIG)
    def test_main_function_calls_uvicorn_run(self, mock_loaded_config, mock_uvicorn_run, mock_loaded_config_production):
        """Test that main() calls uvicorn.run with correct parameters."""
        # Arrange
        # Patch the loaded_config object directly with our fixture values
        mock_loaded_config.workers_count = mock_loaded_config_production.workers_count
        mock_loaded_config.host = mock_loaded_config_production.host
        mock_loaded_config.port = mock_loaded_config_production.port
        mock_loaded_config.debug = mock_loaded_config_production.debug

        # Act
        main()

        # Assert
        expected_params = build_expected_params(
            TEST_WORKERS_COUNT_MULTIPLE,
            TEST_HOST_DEFAULT,
            TEST_PORT_DEFAULT,
            TEST_RELOAD_TRUE
        )
        assert_uvicorn_run_called_with_params(mock_uvicorn_run, expected_params)

    @patch(PATCH_UVICORN_RUN)
    @patch(PATCH_LOADED_CONFIG)
    def test_main_function_with_production_config(self, mock_loaded_config, mock_uvicorn_run, mock_loaded_config_development):
        """Test main() with production-like configuration."""
        # Arrange
        # Patch the loaded_config object directly with our fixture values
        mock_loaded_config.workers_count = mock_loaded_config_development.workers_count
        mock_loaded_config.host = mock_loaded_config_development.host
        mock_loaded_config.port = mock_loaded_config_development.port
        mock_loaded_config.debug = mock_loaded_config_development.debug

        # Act
        main()

        # Assert
        expected_params = build_expected_params(
            TEST_WORKERS_COUNT_SINGLE,
            TEST_HOST_LOCALHOST,
            TEST_PORT_STANDARD,
            TEST_RELOAD_FALSE
        )
        assert_uvicorn_run_called_with_params(mock_uvicorn_run, expected_params)

    @patch(PATCH_UVICORN_RUN)
    @patch(PATCH_LOADED_CONFIG)
    def test_main_function_uses_factory_pattern(self, mock_loaded_config, mock_uvicorn_run, mock_loaded_config_development):
        """Test that main() uses factory pattern for app creation."""
        # Arrange
        # Patch the loaded_config object directly with our fixture values
        mock_loaded_config.workers_count = mock_loaded_config_development.workers_count
        mock_loaded_config.host = mock_loaded_config_development.host
        mock_loaded_config.port = mock_loaded_config_development.port
        mock_loaded_config.debug = mock_loaded_config_development.debug

        # Act
        main()

        # Assert
        assert_factory_pattern_enabled(mock_uvicorn_run)

    @patch(PATCH_UVICORN_RUN)
    @patch(PATCH_LOADED_CONFIG)
    def test_main_function_with_debug_enables_reload(self, mock_loaded_config, mock_uvicorn_run, mock_loaded_config_debug):
        """Test that debug mode enables reload in uvicorn."""
        # Arrange
        # Patch the loaded_config object directly with our fixture values
        mock_loaded_config.workers_count = mock_loaded_config_debug.workers_count
        mock_loaded_config.host = mock_loaded_config_debug.host
        mock_loaded_config.port = mock_loaded_config_debug.port
        mock_loaded_config.debug = mock_loaded_config_debug.debug

        # Act
        main()

        # Assert
        assert_reload_setting(mock_uvicorn_run, TEST_RELOAD_TRUE)

    @patch(PATCH_UVICORN_RUN)
    @patch(PATCH_LOADED_CONFIG)
    def test_main_function_without_debug_disables_reload(self, mock_loaded_config, mock_uvicorn_run, mock_loaded_config_no_debug):
        """Test that non-debug mode disables reload in uvicorn."""
        # Arrange
        # Patch the loaded_config object directly with our fixture values
        mock_loaded_config.workers_count = mock_loaded_config_no_debug.workers_count
        mock_loaded_config.host = mock_loaded_config_no_debug.host
        mock_loaded_config.port = mock_loaded_config_no_debug.port
        mock_loaded_config.debug = mock_loaded_config_no_debug.debug

        # Act
        main()

        # Assert
        assert_reload_setting(mock_uvicorn_run, TEST_RELOAD_FALSE)

    @patch(PATCH_UVICORN_RUN)
    @patch(PATCH_LOADED_CONFIG)
    def test_main_function_with_none_values(self, mock_loaded_config, mock_uvicorn_run, mock_loaded_config_none_values):
        """Test main() handles None values gracefully."""
        # Arrange
        # Patch the loaded_config object directly with our fixture values
        mock_loaded_config.workers_count = mock_loaded_config_none_values.workers_count
        mock_loaded_config.host = mock_loaded_config_none_values.host
        mock_loaded_config.port = mock_loaded_config_none_values.port
        mock_loaded_config.debug = mock_loaded_config_none_values.debug

        # Act
        main()

        # Assert
        expected_params = build_expected_params(
            NONE_VALUE,
            NONE_VALUE,
            NONE_VALUE,
            NONE_VALUE
        )
        assert_uvicorn_run_called_with_params(mock_uvicorn_run, expected_params)

    @patch(PATCH_UVICORN_RUN)
    @patch(PATCH_LOADED_CONFIG)
    def test_main_function_called_multiple_times(self, mock_loaded_config, mock_uvicorn_run, mock_loaded_config_development):
        """Test that main() can be called multiple times."""
        # Arrange
        # Patch the loaded_config object directly with our fixture values
        mock_loaded_config.workers_count = mock_loaded_config_development.workers_count
        mock_loaded_config.host = mock_loaded_config_development.host
        mock_loaded_config.port = mock_loaded_config_development.port
        mock_loaded_config.debug = mock_loaded_config_development.debug

        # Act
        main()
        main()

        # Assert
        assert_uvicorn_call_count(mock_uvicorn_run, EXPECTED_CALL_COUNT_TWICE)

    def test_main_function_imports_correctly(self):
        """Test that main function can be imported without errors."""
        # Act & Assert
        from app.main import main
        assert callable(main), ASSERTION_CALLABLE

    def test_main_function_has_correct_docstring(self):
        """Test that main function has proper docstring."""
        # Act & Assert
        from app.main import main
        assert main.__doc__ is not None, ASSERTION_DOCSTRING
        assert DOCSTRING_CONTENT in main.__doc__

    @patch(PATCH_UVICORN_RUN)
    @patch(PATCH_LOADED_CONFIG)
    def test_main_function_type_annotations(self, mock_loaded_config, mock_uvicorn_run, mock_loaded_config_development):
        """Test that main function has correct type annotations."""
        # Arrange
        # Patch the loaded_config object directly with our fixture values
        mock_loaded_config.workers_count = mock_loaded_config_development.workers_count
        mock_loaded_config.host = mock_loaded_config_development.host
        mock_loaded_config.port = mock_loaded_config_development.port
        mock_loaded_config.debug = mock_loaded_config_development.debug

        # Act
        result = main()

        # Assert
        assert result is None, ASSERTION_RETURN_NONE
        assert_uvicorn_call_count(mock_uvicorn_run, EXPECTED_CALL_COUNT_ONCE)

    @pytest.mark.parametrize("workers,host,port,debug,expected_reload", [
        (TEST_WORKERS_COUNT_SINGLE, TEST_HOST_LOCALHOST, TEST_PORT_STANDARD, TEST_DEBUG_TRUE, TEST_RELOAD_TRUE),
        (TEST_WORKERS_COUNT_SINGLE, TEST_HOST_LOCALHOST, TEST_PORT_STANDARD, TEST_DEBUG_FALSE, TEST_RELOAD_FALSE),
        (TEST_WORKERS_COUNT_MULTIPLE, TEST_HOST_DEFAULT, TEST_PORT_DEFAULT, TEST_DEBUG_TRUE, TEST_RELOAD_TRUE),
        (TEST_WORKERS_COUNT_MULTIPLE, TEST_HOST_DEFAULT, TEST_PORT_DEFAULT, TEST_DEBUG_FALSE, TEST_RELOAD_FALSE),
    ])
    @patch(PATCH_UVICORN_RUN)
    @patch(PATCH_LOADED_CONFIG)
    def test_main_function_with_various_configurations(self, mock_loaded_config, mock_uvicorn_run, 
                                                      workers, host, port, debug, expected_reload):
        """Test main() with various configuration combinations."""
        # Arrange
        # Patch the loaded_config object directly with parametrized values
        mock_loaded_config.workers_count = workers
        mock_loaded_config.host = host
        mock_loaded_config.port = port
        mock_loaded_config.debug = debug

        # Act
        main()

        # Assert
        expected_params = build_expected_params(workers, host, port, expected_reload)
        assert_uvicorn_run_called_with_params(mock_uvicorn_run, expected_params)


class TestMainIntegration:
    """Integration tests for main module."""

    @patch(PATCH_UVICORN_RUN)
    @patch(PATCH_LOADED_CONFIG)
    def test_main_configuration_consistency(self, mock_loaded_config, mock_uvicorn_run, mock_loaded_config_production):
        """Test that configuration is consistently applied."""
        # Arrange
        # Patch the loaded_config object directly with our fixture values
        mock_loaded_config.workers_count = mock_loaded_config_production.workers_count
        mock_loaded_config.host = mock_loaded_config_production.host
        mock_loaded_config.port = mock_loaded_config_production.port
        mock_loaded_config.debug = mock_loaded_config_production.debug

        # Act
        main()

        # Assert
        # Verify all expected parameters are passed
        args, kwargs = mock_uvicorn_run.call_args
        assert APP_FACTORY_PATH in args
        assert 'workers' in kwargs
        assert 'host' in kwargs
        assert 'port' in kwargs
        assert 'reload' in kwargs
        assert 'factory' in kwargs

    @patch(PATCH_UVICORN_RUN)
    @patch(PATCH_LOADED_CONFIG)
    def test_main_debug_reload_correlation(self, mock_loaded_config, mock_uvicorn_run):
        """Test that debug setting correctly correlates with reload setting."""
        # Test debug=True -> reload=True
        mock_loaded_config.workers_count = TEST_WORKERS_COUNT_SINGLE
        mock_loaded_config.host = TEST_HOST_LOCALHOST
        mock_loaded_config.port = TEST_PORT_STANDARD
        mock_loaded_config.debug = TEST_DEBUG_TRUE

        main()
        
        args, kwargs = mock_uvicorn_run.call_args
        assert kwargs['reload'] is TEST_RELOAD_TRUE

        # Reset mock
        mock_uvicorn_run.reset_mock()

        # Test debug=False -> reload=False
        mock_loaded_config.workers_count = TEST_WORKERS_COUNT_SINGLE
        mock_loaded_config.host = TEST_HOST_LOCALHOST
        mock_loaded_config.port = TEST_PORT_STANDARD
        mock_loaded_config.debug = TEST_DEBUG_FALSE

        main()
        
        args, kwargs = mock_uvicorn_run.call_args
        assert kwargs['reload'] is TEST_RELOAD_FALSE

    def test_main_module_structure(self):
        """Test main module has correct structure."""
        # Act & Assert
        import app.main
        assert hasattr(app.main, 'main'), "Module should have main function"
        assert callable(app.main.main), "main should be callable"
        assert hasattr(app.main, 'uvicorn'), "Module should import uvicorn" 