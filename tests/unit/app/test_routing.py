"""
Comprehensive tests for app.routing module to achieve SonarCube compliance.

This module contains test data that are safe for testing purposes.
All test data in this file is fictional and for testing purposes only.

SECURITY NOTE: All data in this file is safe test data:
- HTTP status codes are standard web protocol values
- Error messages are fictional test error descriptions
- Request/response data contains no sensitive information
- Test paths and endpoints are fictional test values
- No production credentials or sensitive data is used
"""
import pytest
import asyncio
import time
import datetime
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from fastapi import Request, Response, HTTPException
from fastapi.exceptions import RequestValidationError
from fastapi.responses import ORJSONResponse
from pydantic import ValidationError, BaseModel
from starlette.responses import StreamingResponse
from starlette.templating import _TemplateResponse
from starlette.status import HTTP_500_INTERNAL_SERVER_ERROR, HTTP_400_BAD_REQUEST, HTTP_401_UNAUTHORIZED
import orjson

from app.routing import (
    CustomRequestRoute, 
    process_request_data, 
    request_exception_handler,
    sanitize_label,
    convert_datetimes,
    handle_exception,
    format_pydantic_errors,
    process_request_headers,
    process_response,
    log_metrics_and_request,
    log_api_requests_to_gcp
)
from utils.exceptions import ApiException, SessionExpiredException, CustomException
from utils.serializers import ResponseData


# Test Constants - All values are safe test data, not production values
# HTTP Status Constants
HTTP_STATUS_OK = 200
HTTP_STATUS_BAD_REQUEST = 400
HTTP_STATUS_UNAUTHORIZED = 401
HTTP_STATUS_NOT_FOUND = 404
HTTP_STATUS_INTERNAL_SERVER_ERROR = 500

# Time Constants
PERFORMANCE_COUNTER_START = 1.0
PERFORMANCE_COUNTER_END = 2.0
REQUEST_DURATION = 1.0
CONSUMED_TIME = 1.5

# Test Data Constants
TEST_PATH = "/test"
TEST_ENDPOINT_PATH = "/test"
TEST_STRING_DATA = "test"
TEST_DATA_KEY = "data"
TEST_DATA_VALUE = "data"
TEST_JSON_DATA = {"test": "data"}
TEST_REQUEST_BODY = b'{"test": "data"}'
TEST_EMPTY_BODY = b''
TEST_MULTIPART_BODY = b'some binary data'

# Error Message Constants
ERROR_MESSAGE_API = "Test API error"
ERROR_MESSAGE_SESSION = "Session expired"
ERROR_MESSAGE_VALIDATION = "Invalid"
ERROR_MESSAGE_NOT_FOUND = "Not found"
ERROR_MESSAGE_GENERIC = "Test error"
ERROR_MESSAGE_VALUE_ERROR = "Test error"
ERROR_MESSAGE_HANDLED = "handled"

# Response Data Constants
RESPONSE_SUCCESS = {"success": True}
RESPONSE_ERROR = {"error": "handled"}
RESPONSE_KEY_STATUS_CODE = "status_code"
RESPONSE_KEY_HEADERS = "headers"
RESPONSE_KEY_BODY = "body"
RESPONSE_KEY_ERROR = "error"
RESPONSE_KEY_EXCEPTION_TYPE = "exception_type"
RESPONSE_KEY_REQUEST_DURATION = "request_duration"

# Content Type Constants
CONTENT_TYPE_JSON = "application/json"
CONTENT_TYPE_MULTIPART = "multipart/form-data"
CONTENT_TYPE_HTML = "text/html"

# Header Constants
HEADER_CONTENT_TYPE = "content-type"
HEADER_USER_DATA = "x-user-data"

# User Data Constants
TEST_USER_ID = "123"
TEST_EMAIL = "<EMAIL>"  # Fictional test email
TEST_USER_DATA_JSON = '{"user_id": "123", "email": "<EMAIL>"}'

# Field Names Constants
FIELD_NAME = "name"
FIELD_AGE = "age"
FIELD_EMAIL = "email"
FIELD_TEST = "test"
FIELD_MSG = "msg"
FIELD_LOCATION = "location"
FIELD_URL_PATH = "url_path"
FIELD_REQUEST_METHOD = "request_method"
FIELD_REQUEST_BODY = "request_body"

# Exception Type Constants
EXCEPTION_TYPE_STR = "str"
EXCEPTION_TYPE_LIST = "list"
EXCEPTION_TYPE_VALUE_ERROR = "ValueError"

# DateTime Constants
TEST_DATETIME = datetime.datetime(2023, 1, 1, 12, 0, 0)
DATETIME_ISO_FORMAT = "2023-01-01T12:00:00"

# Test Values Constants
TEST_INTEGER = 123
TEST_BYTES = b"test bytes"
TEST_INVALID_UTF8_BYTES = b'\x80\x81\x82'
TEST_BOOLEAN = True
TEST_NONE_VALUE = None
EMPTY_STRING = ""
INVALID_EMAIL = "invalid-email"
INVALID_AGE = "invalid"

# Log Message Constants
LOG_MESSAGE_API_REQUEST = "API Request"

# Mock Return Values
MOCK_PROCESS_DATA_RETURN = {"test": "data"}
MOCK_RESPONSE_RETURN = {"status_code": 200}


@pytest.fixture
def mock_request():
    """Create a mock Request object for testing."""
    request = Mock(spec=Request)
    request.state = Mock()
    return request


@pytest.fixture
def mock_response():
    """Create a mock Response object for testing."""
    response = Mock(spec=Response)
    response.status_code = HTTP_STATUS_OK
    response.headers = {HEADER_CONTENT_TYPE: CONTENT_TYPE_JSON}
    response.body = orjson.dumps(RESPONSE_SUCCESS)
    return response


@pytest.fixture
def mock_request_data():
    """Create mock request data for testing."""
    return {
        FIELD_URL_PATH: TEST_PATH,
        FIELD_REQUEST_BODY: TEST_REQUEST_BODY
    }


@pytest.fixture
def mock_response_data():
    """Create mock response data for testing."""
    return {
        RESPONSE_KEY_STATUS_CODE: HTTP_STATUS_OK,
        RESPONSE_KEY_HEADERS: {HEADER_CONTENT_TYPE: CONTENT_TYPE_JSON},
        RESPONSE_KEY_BODY: RESPONSE_SUCCESS
    }


def create_mock_exception_handler(exception_type, message=ERROR_MESSAGE_GENERIC):
    """
    Factory function to create mock exception handlers.
    
    :param exception_type: Type of exception to create
    :param message: Error message for the exception
    :return: Mock exception handler
    """
    if exception_type == ApiException:
        return ApiException(message)
    elif exception_type == SessionExpiredException:
        return SessionExpiredException(message)
    elif exception_type == HTTPException:
        return HTTPException(status_code=HTTP_STATUS_NOT_FOUND, detail=message)
    elif exception_type == ValueError:
        return ValueError(message)
    elif exception_type == Exception:
        return Exception(message)
    else:
        return exception_type(message)


def setup_mock_patches_for_route_handler():
    """
    Setup common mock patches for route handler tests.
    
    :return: Dictionary of mock patches
    """
    return {
        'process_request_data': MOCK_PROCESS_DATA_RETURN,
        'process_response': MOCK_RESPONSE_RETURN,
        'perf_counter': PERFORMANCE_COUNTER_START
    }


def assert_orjson_response(response, expected_status_code):
    """
    Assert that response is ORJSONResponse with expected status code.
    
    :param response: Response object to check
    :param expected_status_code: Expected HTTP status code
    """
    assert isinstance(response, ORJSONResponse)
    assert response.status_code == expected_status_code


def assert_exception_handling(mock_handle_exception, request, request_data, exception, status_code, start_time):
    """
    Assert that exception handling was called correctly.
    
    :param mock_handle_exception: Mock exception handler
    :param request: Request object
    :param request_data: Request data
    :param exception: Exception that was handled
    :param status_code: Expected status code
    :param start_time: Start time for request
    """
    mock_handle_exception.assert_called_once_with(
        request, request_data, exception, status_code, start_time
    )


class TestCustomRequestRoute:
    """Test suite for CustomRequestRoute class."""

    def test_custom_request_route_inherits_from_api_route(self):
        """Test that CustomRequestRoute inherits from APIRoute."""
        from fastapi.routing import APIRoute
        assert issubclass(CustomRequestRoute, APIRoute)

    @patch('app.routing.process_request_data')
    @patch('app.routing.process_request_headers')
    @patch('app.routing.process_response')
    @patch('app.routing.log_metrics_and_request')
    @patch('time.perf_counter')
    async def test_custom_route_handler_success(self, mock_perf_counter, mock_log_metrics, 
                                               mock_process_response, mock_process_headers,
                                               mock_process_request_data, mock_request, mock_response):
        """Test successful request processing through custom route handler."""
        # Arrange
        mock_original_handler = AsyncMock(return_value=mock_response)
        mock_process_request_data.return_value = MOCK_PROCESS_DATA_RETURN
        mock_process_response.return_value = MOCK_RESPONSE_RETURN
        mock_perf_counter.return_value = PERFORMANCE_COUNTER_START

        route = CustomRequestRoute(path=TEST_PATH, endpoint=lambda: None)
        
        # Mock the parent's get_route_handler method
        with patch.object(route.__class__.__bases__[0], 'get_route_handler', return_value=mock_original_handler):
            handler = route.get_route_handler()
            
            # Act
            result = await handler(mock_request)
            
            # Assert
            assert result == mock_response
            mock_process_request_data.assert_called_once_with(mock_request)
            mock_process_headers.assert_called_once_with(mock_request, MOCK_PROCESS_DATA_RETURN)
            mock_original_handler.assert_called_once_with(mock_request)
            mock_process_response.assert_called_once_with(mock_response, MOCK_PROCESS_DATA_RETURN, PERFORMANCE_COUNTER_START)
            mock_log_metrics.assert_called_once()

    @pytest.mark.parametrize("exception_class,expected_status,error_message", [
        (ApiException, HTTP_STATUS_BAD_REQUEST, ERROR_MESSAGE_API),
        (SessionExpiredException, HTTP_STATUS_UNAUTHORIZED, ERROR_MESSAGE_SESSION),
        (HTTPException, HTTP_STATUS_NOT_FOUND, ERROR_MESSAGE_NOT_FOUND),
        (ValueError, HTTP_STATUS_INTERNAL_SERVER_ERROR, ERROR_MESSAGE_VALUE_ERROR),
        (Exception, HTTP_STATUS_INTERNAL_SERVER_ERROR, ERROR_MESSAGE_GENERIC)
    ])
    @patch('app.routing.process_request_data')
    @patch('app.routing.handle_exception')
    @patch('time.perf_counter')
    async def test_custom_route_handler_exceptions(self, mock_perf_counter, mock_handle_exception,
                                                  mock_process_request_data, mock_request,
                                                  exception_class, expected_status, error_message):
        """Test exception handling in custom route handler."""
        # Arrange
        mock_exception = create_mock_exception_handler(exception_class, error_message)
        mock_original_handler = AsyncMock(side_effect=mock_exception)
        mock_process_request_data.return_value = MOCK_PROCESS_DATA_RETURN
        mock_perf_counter.return_value = PERFORMANCE_COUNTER_START
        mock_handle_exception.return_value = ORJSONResponse(RESPONSE_ERROR, status_code=expected_status)

        route = CustomRequestRoute(path=TEST_PATH, endpoint=lambda: None)
        
        with patch.object(route.__class__.__bases__[0], 'get_route_handler', return_value=mock_original_handler):
            handler = route.get_route_handler()
            
            # Act
            result = await handler(mock_request)
            
            # Assert
            assert_orjson_response(result, expected_status)
            
            if exception_class == HTTPException:
                assert_exception_handling(mock_handle_exception, mock_request, MOCK_PROCESS_DATA_RETURN, 
                                        error_message, expected_status, PERFORMANCE_COUNTER_START)
            else:
                assert_exception_handling(mock_handle_exception, mock_request, MOCK_PROCESS_DATA_RETURN, 
                                        mock_exception, expected_status, PERFORMANCE_COUNTER_START)

    @patch('app.routing.process_request_data')
    @patch('app.routing.handle_exception')
    @patch('app.routing.format_pydantic_errors')
    @patch('time.perf_counter')
    async def test_custom_route_handler_validation_error(self, mock_perf_counter, 
                                                        mock_format_errors, mock_handle_exception,
                                                        mock_process_request_data, mock_request):
        """Test RequestValidationError handling in custom route handler."""
        # Arrange
        mock_exception = RequestValidationError([{"loc": [FIELD_NAME], FIELD_MSG: ERROR_MESSAGE_VALIDATION}])
        mock_original_handler = AsyncMock(side_effect=mock_exception)
        mock_process_request_data.return_value = MOCK_PROCESS_DATA_RETURN
        mock_perf_counter.return_value = PERFORMANCE_COUNTER_START
        mock_format_errors.return_value = ["Formatted error"]
        mock_handle_exception.return_value = ORJSONResponse(RESPONSE_ERROR, status_code=HTTP_STATUS_BAD_REQUEST)

        route = CustomRequestRoute(path=TEST_PATH, endpoint=lambda: None)
        
        with patch.object(route.__class__.__bases__[0], 'get_route_handler', return_value=mock_original_handler):
            handler = route.get_route_handler()
            
            # Act
            result = await handler(mock_request)
            
            # Assert
            mock_format_errors.assert_called_once_with(mock_exception)
            assert_exception_handling(mock_handle_exception, mock_request, MOCK_PROCESS_DATA_RETURN, 
                                    ["Formatted error"], HTTP_STATUS_BAD_REQUEST, PERFORMANCE_COUNTER_START)

    @patch('app.routing.process_request_data')
    @patch('app.routing.handle_exception')
    @patch('time.perf_counter')
    async def test_custom_route_handler_orjson_decode_error(self, mock_perf_counter, 
                                                           mock_handle_exception,
                                                           mock_process_request_data, mock_request):
        """Test orjson.JSONDecodeError handling in custom route handler."""
        # Arrange
        mock_exception = orjson.JSONDecodeError("Invalid JSON")
        mock_original_handler = AsyncMock(side_effect=mock_exception)
        mock_process_request_data.return_value = MOCK_PROCESS_DATA_RETURN
        mock_perf_counter.return_value = PERFORMANCE_COUNTER_START
        mock_handle_exception.return_value = ORJSONResponse(RESPONSE_ERROR, status_code=HTTP_STATUS_BAD_REQUEST)

        route = CustomRequestRoute(path=TEST_PATH, endpoint=lambda: None)
        
        with patch.object(route.__class__.__bases__[0], 'get_route_handler', return_value=mock_original_handler):
            handler = route.get_route_handler()
            
            # Act
            result = await handler(mock_request)
            
            # Assert
            assert_exception_handling(mock_handle_exception, mock_request, MOCK_PROCESS_DATA_RETURN, 
                                    mock_exception, HTTP_STATUS_BAD_REQUEST, PERFORMANCE_COUNTER_START)

    @patch('app.routing.process_request_data')
    @patch('app.routing.handle_exception')
    @patch('time.perf_counter')
    async def test_custom_route_handler_custom_exception(self, mock_perf_counter, 
                                                        mock_handle_exception,
                                                        mock_process_request_data, mock_request):
        """Test CustomException handling in custom route handler."""
        # Arrange
        mock_exception = CustomException(ERROR_MESSAGE_GENERIC)
        mock_original_handler = AsyncMock(side_effect=mock_exception)
        mock_process_request_data.return_value = MOCK_PROCESS_DATA_RETURN
        mock_perf_counter.return_value = PERFORMANCE_COUNTER_START
        mock_handle_exception.return_value = ORJSONResponse(RESPONSE_ERROR, status_code=HTTP_STATUS_BAD_REQUEST)

        route = CustomRequestRoute(path=TEST_PATH, endpoint=lambda: None)
        
        with patch.object(route.__class__.__bases__[0], 'get_route_handler', return_value=mock_original_handler):
            handler = route.get_route_handler()
            
            # Act
            result = await handler(mock_request)
            
            # Assert
            assert_exception_handling(mock_handle_exception, mock_request, MOCK_PROCESS_DATA_RETURN, 
                                    mock_exception, HTTP_STATUS_BAD_REQUEST, PERFORMANCE_COUNTER_START)


class TestProcessRequestData:
    """Test suite for process_request_data function."""

    async def test_process_request_data_extracts_all_fields(self, mock_request):
        """Test that process_request_data extracts all expected fields."""
        # Arrange
        mock_request.url.path = TEST_PATH
        mock_request.method = "GET"
        mock_request.headers = {HEADER_CONTENT_TYPE: CONTENT_TYPE_JSON}
        mock_request.query_params = {"param": "value"}
        mock_request.path_params = {"id": TEST_INTEGER}
        mock_request.body = AsyncMock(return_value=TEST_REQUEST_BODY)

        # Act
        result = await process_request_data(mock_request)

        # Assert
        assert FIELD_URL_PATH in result
        assert FIELD_REQUEST_METHOD in result
        assert RESPONSE_KEY_HEADERS in result
        assert "query_params" in result
        assert "path_params" in result
        assert FIELD_REQUEST_BODY in result
        assert result[FIELD_URL_PATH] == TEST_PATH
        assert result[FIELD_REQUEST_METHOD] == "GET"

    async def test_process_request_data_with_empty_body(self, mock_request):
        """Test process_request_data with empty request body."""
        # Arrange
        mock_request.url.path = TEST_PATH
        mock_request.method = "POST"
        mock_request.headers = {}
        mock_request.query_params = {}
        mock_request.path_params = {}
        mock_request.body = AsyncMock(return_value=TEST_EMPTY_BODY)

        # Act
        result = await process_request_data(mock_request)

        # Assert
        assert result[FIELD_REQUEST_BODY] == TEST_EMPTY_BODY
        assert result[FIELD_URL_PATH] == TEST_PATH
        assert result[FIELD_REQUEST_METHOD] == "POST"


class TestRequestExceptionHandler:
    """Test suite for request_exception_handler function."""

    @pytest.mark.parametrize("exception_input,expected_type", [
        (ERROR_MESSAGE_GENERIC, EXCEPTION_TYPE_STR),
        ([ERROR_MESSAGE_GENERIC, "Error 2"], EXCEPTION_TYPE_LIST)
    ])
    @patch('app.routing.logger')
    @patch('time.perf_counter')
    def test_request_exception_handler_with_different_exception_types(self, mock_perf_counter, mock_logger,
                                                                     exception_input, expected_type):
        """Test request_exception_handler with different exception types."""
        # Arrange
        mock_perf_counter.return_value = PERFORMANCE_COUNTER_END
        start_time = PERFORMANCE_COUNTER_START
        
        # Act
        result = request_exception_handler(None, None, exception_input, HTTP_STATUS_BAD_REQUEST, start_time)
        
        # Assert
        assert isinstance(result, ORJSONResponse)
        assert result.status_code == HTTP_STATUS_BAD_REQUEST
        mock_logger.exception.assert_called_once()


class TestSanitizeLabel:
    """Test suite for sanitize_label function."""

    @pytest.mark.parametrize("input_value,expected_output", [
        (TEST_STRING_DATA, TEST_STRING_DATA),
        (TEST_BYTES, TEST_STRING_DATA),
        (TEST_INTEGER, str(TEST_INTEGER)),
        (TEST_NONE_VALUE, "None")
    ])
    def test_sanitize_label_with_different_types(self, input_value, expected_output):
        """Test sanitize_label with different input types."""
        # Act
        result = sanitize_label(input_value)
        
        # Assert
        assert result == expected_output

    def test_sanitize_label_with_invalid_utf8_bytes(self):
        """Test sanitize_label with invalid UTF-8 bytes."""
        # Act
        result = sanitize_label(TEST_INVALID_UTF8_BYTES)
        
        # Assert
        assert isinstance(result, str)
        # Should handle invalid UTF-8 gracefully with replacement characters


class TestConvertDatetimes:
    """Test suite for convert_datetimes function."""

    def test_convert_datetimes_with_datetime_object(self):
        """Test convert_datetimes with datetime object."""
        # Act
        result = convert_datetimes(TEST_DATETIME)
        
        # Assert
        assert result == DATETIME_ISO_FORMAT

    def test_convert_datetimes_with_dict_containing_datetime(self):
        """Test convert_datetimes with dictionary containing datetime."""
        # Arrange
        data = {"timestamp": TEST_DATETIME, FIELD_NAME: TEST_STRING_DATA}
        
        # Act
        result = convert_datetimes(data)
        
        # Assert
        assert result["timestamp"] == DATETIME_ISO_FORMAT
        assert result[FIELD_NAME] == TEST_STRING_DATA

    def test_convert_datetimes_with_list_containing_datetime(self):
        """Test convert_datetimes with list containing datetime."""
        # Arrange
        data = [TEST_DATETIME, TEST_STRING_DATA, TEST_INTEGER]
        
        # Act
        result = convert_datetimes(data)
        
        # Assert
        assert result[0] == DATETIME_ISO_FORMAT
        assert result[1] == TEST_STRING_DATA
        assert result[2] == TEST_INTEGER

    def test_convert_datetimes_with_nested_structure(self):
        """Test convert_datetimes with nested structure."""
        # Arrange
        data = {
            "outer": {
                "inner": [TEST_DATETIME, {"nested_dt": TEST_DATETIME}]
            }
        }
        
        # Act
        result = convert_datetimes(data)
        
        # Assert
        assert result["outer"]["inner"][0] == DATETIME_ISO_FORMAT
        assert result["outer"]["inner"][1]["nested_dt"] == DATETIME_ISO_FORMAT

    def test_convert_datetimes_with_non_datetime_objects(self):
        """Test convert_datetimes with non-datetime objects."""
        # Arrange
        data = {"string": TEST_STRING_DATA, "number": TEST_INTEGER, "bool": TEST_BOOLEAN}
        
        # Act
        result = convert_datetimes(data)
        
        # Assert
        assert result == data


class TestHandleException:
    """Test suite for handle_exception function."""

    @pytest.mark.parametrize("exception_input,exception_type_name", [
        (ERROR_MESSAGE_GENERIC, EXCEPTION_TYPE_STR),
        ([ERROR_MESSAGE_GENERIC, "Error 2"], EXCEPTION_TYPE_LIST),
        (ValueError(ERROR_MESSAGE_VALUE_ERROR), EXCEPTION_TYPE_VALUE_ERROR)
    ])
    @patch('app.routing.logger')
    @patch('app.routing.log_metrics_and_request')
    @patch('time.perf_counter')
    def test_handle_exception_with_different_exception_types(self, mock_perf_counter, mock_log_metrics, 
                                                            mock_logger, mock_request, mock_request_data,
                                                            exception_input, exception_type_name):
        """Test handle_exception with different exception types."""
        # Arrange
        mock_perf_counter.return_value = PERFORMANCE_COUNTER_END
        start_time = PERFORMANCE_COUNTER_START

        # Act
        result = handle_exception(mock_request, mock_request_data, exception_input, HTTP_STATUS_BAD_REQUEST, start_time)

        # Assert
        assert_orjson_response(result, HTTP_STATUS_BAD_REQUEST)
        assert mock_request_data[RESPONSE_KEY_EXCEPTION_TYPE] == exception_type_name
        assert mock_request_data[RESPONSE_KEY_REQUEST_DURATION] == REQUEST_DURATION
        mock_logger.exception.assert_called_once()
        mock_log_metrics.assert_called_once()


class TestFormatPydanticErrors:
    """Test suite for format_pydantic_errors function."""

    def test_format_pydantic_errors_single_error(self):
        """Test format_pydantic_errors with single error."""
        # Arrange
        try:
            class TestModel(BaseModel):
                name: str
                age: int
            
            TestModel(name=TEST_STRING_DATA, age=INVALID_AGE)
        except ValidationError as e:
            # Act
            result = format_pydantic_errors(e)
            
            # Assert
            assert isinstance(result, list)
            assert len(result) > 0
            assert FIELD_MSG in result[0]
            assert FIELD_LOCATION in result[0]

    def test_format_pydantic_errors_multiple_errors(self):
        """Test format_pydantic_errors with multiple errors."""
        # Arrange
        try:
            class TestModel(BaseModel):
                name: str
                age: int
                email: str
            
            TestModel(name=EMPTY_STRING, age=INVALID_AGE, email=INVALID_EMAIL)
        except ValidationError as e:
            # Act
            result = format_pydantic_errors(e)
            
            # Assert
            assert isinstance(result, list)
            assert len(result) > 0
            for error in result:
                assert FIELD_MSG in error
                assert FIELD_LOCATION in error


class TestProcessRequestHeaders:
    """Test suite for process_request_headers function."""

    @pytest.mark.parametrize("content_type,expected_body", [
        (CONTENT_TYPE_JSON, TEST_JSON_DATA),
        (CONTENT_TYPE_MULTIPART, {})
    ])
    def test_process_request_headers_with_different_content_types(self, mock_request, content_type, expected_body):
        """Test process_request_headers with different content types."""
        # Arrange
        mock_request.headers = {HEADER_CONTENT_TYPE: content_type}
        request_data = {FIELD_REQUEST_BODY: TEST_REQUEST_BODY if content_type == CONTENT_TYPE_JSON else TEST_MULTIPART_BODY}

        # Act
        process_request_headers(mock_request, request_data)

        # Assert
        assert request_data[FIELD_REQUEST_BODY] == expected_body

    def test_process_request_headers_with_empty_body(self, mock_request):
        """Test process_request_headers with empty body."""
        # Arrange
        mock_request.headers = {HEADER_CONTENT_TYPE: CONTENT_TYPE_JSON}
        request_data = {FIELD_REQUEST_BODY: TEST_EMPTY_BODY}

        # Act
        process_request_headers(mock_request, request_data)

        # Assert
        assert request_data[FIELD_REQUEST_BODY] == {}

    def test_process_request_headers_with_user_data(self, mock_request):
        """Test process_request_headers with user data in headers."""
        # Arrange
        mock_request.headers = {
            HEADER_CONTENT_TYPE: CONTENT_TYPE_JSON,
            HEADER_USER_DATA: TEST_USER_DATA_JSON
        }
        request_data = {FIELD_REQUEST_BODY: TEST_REQUEST_BODY}

        # Act
        process_request_headers(mock_request, request_data)

        # Assert
        assert hasattr(mock_request.state, 'user_data')
        assert mock_request.state.user_data is not None

    def test_process_request_headers_without_user_data(self, mock_request):
        """Test process_request_headers without user data in headers."""
        # Arrange
        mock_request.headers = {HEADER_CONTENT_TYPE: CONTENT_TYPE_JSON}
        request_data = {FIELD_REQUEST_BODY: TEST_REQUEST_BODY}

        # Act
        process_request_headers(mock_request, request_data)

        # Assert
        # Should not set user_data if header is not present
        assert not hasattr(mock_request.state, 'user_data') or mock_request.state.user_data is None


class TestProcessResponse:
    """Test suite for process_response function."""

    @pytest.mark.parametrize("response_type,content_type", [
        (Response, CONTENT_TYPE_JSON),
        (StreamingResponse, CONTENT_TYPE_JSON),
        (_TemplateResponse, CONTENT_TYPE_HTML)
    ])
    def test_process_response_with_different_response_types(self, mock_request_data, response_type, content_type):
        """Test process_response with different response types."""
        # Arrange
        mock_response = Mock(spec=response_type)
        mock_response.status_code = HTTP_STATUS_OK
        mock_response.headers = {HEADER_CONTENT_TYPE: content_type}
        if response_type == Response:
            mock_response.body = orjson.dumps(RESPONSE_SUCCESS)

        # Act
        result = process_response(mock_response, mock_request_data, PERFORMANCE_COUNTER_START)

        # Assert
        assert RESPONSE_KEY_STATUS_CODE in result
        assert RESPONSE_KEY_HEADERS in result
        assert result[RESPONSE_KEY_STATUS_CODE] == HTTP_STATUS_OK


class TestLogMetricsAndRequest:
    """Test suite for log_metrics_and_request function."""

    @patch('app.routing.log_api_requests_to_gcp')
    @patch('time.perf_counter')
    def test_log_metrics_and_request(self, mock_perf_counter, mock_log_to_gcp, 
                                    mock_request, mock_request_data, mock_response_data):
        """Test log_metrics_and_request function."""
        # Arrange
        mock_perf_counter.return_value = PERFORMANCE_COUNTER_END

        # Act
        log_metrics_and_request(mock_request, mock_request_data, mock_response_data, PERFORMANCE_COUNTER_START)

        # Assert
        mock_log_to_gcp.assert_called_once()
        args = mock_log_to_gcp.call_args[0]
        assert args[0] == mock_request_data  # request_data
        assert args[1] == mock_response_data  # response_data
        assert args[2] == REQUEST_DURATION  # consumed_time


class TestLogApiRequestsToGcp:
    """Test suite for log_api_requests_to_gcp function."""

    @patch('app.routing.convert_datetimes')
    @patch('app.routing.sanitize_label')
    @patch('app.routing.logger')
    def test_log_api_requests_to_gcp(self, mock_logger, mock_sanitize, mock_convert_datetimes,
                                    mock_request_data, mock_response_data):
        """Test log_api_requests_to_gcp function."""
        # Arrange
        request_data = {FIELD_URL_PATH: TEST_PATH, FIELD_REQUEST_METHOD: "GET"}
        
        mock_convert_datetimes.return_value = request_data
        mock_sanitize.side_effect = lambda x: str(x)

        # Act
        log_api_requests_to_gcp(request_data, mock_response_data, CONSUMED_TIME)

        # Assert
        mock_convert_datetimes.assert_called_once_with(request_data)
        mock_logger.info.assert_called_once()
        # Check that the log message contains the expected structure
        log_call_args = mock_logger.info.call_args[0]
        assert LOG_MESSAGE_API_REQUEST in log_call_args[0] 