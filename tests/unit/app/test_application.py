"""
Comprehensive tests for app.application module to achieve SonarCube compliance.

This module contains test data that are safe for testing purposes.
All test data in this file is fictional and for testing purposes only.

SECURITY NOTE: All data in this file is safe test data:
- Application names and paths are fictional test values
- Pod names and directory paths are fictional test identifiers
- Sleep intervals and timeouts are test configuration values
- Error messages are fictional test error descriptions
- No production credentials or sensitive data is used
"""
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock, MagicMock, call
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.sessions import SessionMiddleware

from app.application import get_app, lifespan, repeated_task_for_prometheus


# Test Constants - All values are safe test data, not production values
# Application Configuration Constants
APP_TITLE = "almanac"
APP_DEBUG_TRUE = True
APP_DOCS_URL_NONE = None
APP_OPENAPI_URL = "/openapi.json"

# Directory and Path Constants
TEST_BASE_DIR = "/app"
TEST_METRICS_DIR = "/metrics"
TEST_TEMP_POD_NAME = "temp"
TEST_POD_NAME = "test-pod"
LOG_FILE_NAME = "logs.prom"

# Time and Sleep Constants
PROMETHEUS_LOG_TIME = 10
SLEEP_INTERVAL = 10

# Error Message Constants
ERROR_STARTUP = "Startup error"
ERROR_SHUTDOWN = "Shutdown error"
ERROR_PROMETHEUS = "Prometheus error"

# Middleware Count Constants
MINIMUM_MIDDLEWARE_COUNT = 0
EXPECTED_MIDDLEWARE_COUNT = 4

# File Extension Constants
PROM_EXTENSION = ".prom"

# Mock Patch Paths
PATCH_CONFIGURE_SENTRY = 'app.application.configure_sentry'
PATCH_API_ROUTER = 'app.application.api_router'
PATCH_METRICS_ENDPOINT = 'app.application.metrics_endpoint'
PATCH_RUN_ON_STARTUP = 'app.application.run_on_startup'
PATCH_RUN_ON_EXIT = 'app.application.run_on_exit'
PATCH_REPEATED_TASK = 'app.application.repeated_task_for_prometheus'
PATCH_ASYNCIO_CREATE_TASK = 'asyncio.create_task'
PATCH_ASYNCIO_SLEEP = 'asyncio.sleep'
PATCH_GENERATE_PROMETHEUS = 'app.application.generate_prometheus_data'
PATCH_LOADED_CONFIG = 'app.application.loaded_config'
PATCH_OS_GETENV = 'os.getenv'


@pytest.fixture
def mock_app():
    """Create a mock FastAPI application."""
    return Mock(spec=FastAPI)


@pytest.fixture
def mock_loaded_config():
    """Create mock loaded config."""
    config = Mock()
    config.BASE_DIR = TEST_BASE_DIR
    config.POD_NAME = TEST_TEMP_POD_NAME
    return config


@pytest.fixture
def mock_loaded_config_with_pod():
    """Create mock loaded config with specific pod name."""
    config = Mock()
    config.BASE_DIR = TEST_BASE_DIR
    config.POD_NAME = TEST_POD_NAME
    return config


@pytest.fixture
def common_patches():
    """Create common patches for get_app tests."""
    with patch(PATCH_CONFIGURE_SENTRY) as mock_sentry, \
         patch(PATCH_API_ROUTER) as mock_router:
        yield {
            'sentry': mock_sentry,
            'router': mock_router
        }


def assert_fastapi_app_properties(app, title=APP_TITLE, debug=APP_DEBUG_TRUE, docs_url=APP_DOCS_URL_NONE, openapi_url=APP_OPENAPI_URL):
    """
    Assert FastAPI application properties.
    
    :param app: FastAPI application instance
    :param title: Expected app title
    :param debug: Expected debug value
    :param docs_url: Expected docs URL
    :param openapi_url: Expected OpenAPI URL
    """
    assert isinstance(app, FastAPI)
    assert app.title == title
    assert app.debug == debug
    assert app.docs_url == docs_url
    assert app.openapi_url == openapi_url


def assert_middleware_count(app, minimum_count=MINIMUM_MIDDLEWARE_COUNT):
    """
    Assert middleware count is at least the minimum.
    
    :param app: FastAPI application instance
    :param minimum_count: Minimum expected middleware count
    """
    assert len(app.user_middleware) > minimum_count


def assert_lifespan_context(app):
    """
    Assert lifespan context is properly configured.
    
    :param app: FastAPI application instance
    """
    assert app.router.lifespan_context is not None


def assert_mock_call_counts(mock_startup, mock_exit, startup_count=1, exit_count=1):
    """
    Assert mock call counts for startup and exit functions.
    
    :param mock_startup: Mock startup function
    :param mock_exit: Mock exit function
    :param startup_count: Expected startup call count
    :param exit_count: Expected exit call count
    """
    if startup_count > 0:
        assert mock_startup.call_count == startup_count
    else:
        mock_startup.assert_not_called()
    
    if exit_count > 0:
        assert mock_exit.call_count == exit_count
    else:
        mock_exit.assert_not_called()


def create_mock_with_side_effects(side_effects):
    """
    Create mock with specific side effects.
    
    :param side_effects: List of side effects
    :return: Mock with side effects
    """
    mock = Mock()
    mock.side_effect = side_effects
    return mock


def build_prometheus_file_path(base_dir, filename):
    """
    Build prometheus file path.
    
    :param base_dir: Base directory
    :param filename: Filename
    :return: Complete file path
    """
    return f"{base_dir}/{filename}"


def build_metrics_file_path(metrics_dir, pod_name):
    """
    Build metrics file path with pod name.
    
    :param metrics_dir: Metrics directory
    :param pod_name: Pod name
    :return: Complete file path
    """
    return f"{metrics_dir}/{pod_name}{PROM_EXTENSION}"


class TestGetApp:
    """Test suite for get_app function."""

    def test_get_app_returns_fastapi_instance(self, common_patches):
        """Test that get_app returns a FastAPI instance."""
        # Act
        app = get_app()

        # Assert
        assert_fastapi_app_properties(app)

    def test_get_app_configures_sentry(self, common_patches):
        """Test that get_app calls configure_sentry."""
        # Act
        get_app()

        # Assert
        common_patches['sentry'].assert_called_once()

    def test_get_app_includes_router(self, common_patches):
        """Test that get_app includes the API router."""
        # Act
        app = get_app()

        # Assert
        # The router should be included - check it was referenced
        assert common_patches['router'] is not None

    def test_get_app_adds_cors_middleware(self, common_patches):
        """Test that get_app adds CORS middleware with correct configuration."""
        # Act
        app = get_app()

        # Assert
        assert_middleware_count(app)

    def test_get_app_adds_session_middleware(self, common_patches):
        """Test that get_app adds session middleware."""
        # Act
        app = get_app()

        # Assert
        assert_middleware_count(app)

    def test_get_app_adds_prometheus_middleware(self, common_patches):
        """Test that get_app adds Prometheus middleware."""
        # Act
        app = get_app()

        # Assert
        assert_middleware_count(app)

    def test_get_app_adds_security_headers_middleware(self, common_patches):
        """Test that get_app adds security headers middleware."""
        # Act
        app = get_app()

        # Assert
        assert_middleware_count(app)

    @patch(PATCH_METRICS_ENDPOINT)
    def test_get_app_adds_metrics_route(self, mock_metrics_endpoint, common_patches):
        """Test that get_app adds the metrics endpoint route."""
        # Act
        app = get_app()

        # Assert
        # Check that routes were added (should have more than 0)
        assert len(app.routes) > MINIMUM_MIDDLEWARE_COUNT

    def test_get_app_with_lifespan(self, common_patches):
        """Test that get_app configures lifespan correctly."""
        # Act
        app = get_app()

        # Assert
        assert_lifespan_context(app)


class TestLifespan:
    """Test suite for lifespan context manager."""

    @patch(PATCH_RUN_ON_STARTUP)
    @patch(PATCH_RUN_ON_EXIT)
    @patch(PATCH_REPEATED_TASK)
    @patch(PATCH_ASYNCIO_CREATE_TASK)
    async def test_lifespan_context_manager(self, mock_create_task, mock_prometheus_task, 
                                           mock_run_on_exit, mock_run_on_startup, mock_app):
        """Test the lifespan context manager startup and shutdown."""
        # Arrange
        mock_run_on_startup.return_value = None
        mock_run_on_exit.return_value = None

        # Act
        async with lifespan(mock_app):
            pass

        # Assert
        assert_mock_call_counts(mock_run_on_startup, mock_run_on_exit)
        mock_create_task.assert_called_once_with(mock_prometheus_task())

    @patch(PATCH_RUN_ON_STARTUP)
    @patch(PATCH_RUN_ON_EXIT)
    @patch(PATCH_REPEATED_TASK)
    @patch(PATCH_ASYNCIO_CREATE_TASK)
    async def test_lifespan_startup_exception(self, mock_create_task, mock_prometheus_task,
                                             mock_run_on_exit, mock_run_on_startup, mock_app):
        """Test lifespan handles startup exceptions."""
        # Arrange
        mock_run_on_startup.side_effect = Exception(ERROR_STARTUP)

        # Act & Assert
        with pytest.raises(Exception, match=ERROR_STARTUP):
            async with lifespan(mock_app):
                pass

        assert_mock_call_counts(mock_run_on_startup, mock_run_on_exit, startup_count=1, exit_count=0)

    @patch(PATCH_RUN_ON_STARTUP)
    @patch(PATCH_RUN_ON_EXIT)
    @patch(PATCH_REPEATED_TASK)
    @patch(PATCH_ASYNCIO_CREATE_TASK)
    async def test_lifespan_shutdown_exception(self, mock_create_task, mock_prometheus_task,
                                              mock_run_on_exit, mock_run_on_startup, mock_app):
        """Test lifespan handles shutdown exceptions."""
        # Arrange
        mock_run_on_startup.return_value = None
        mock_run_on_exit.side_effect = Exception(ERROR_SHUTDOWN)

        # Act & Assert
        with pytest.raises(Exception, match=ERROR_SHUTDOWN):
            async with lifespan(mock_app):
                pass

        assert_mock_call_counts(mock_run_on_startup, mock_run_on_exit)


class TestRepeatedTaskForPrometheus:
    """Test suite for repeated_task_for_prometheus function."""

    @patch(PATCH_GENERATE_PROMETHEUS)
    @patch(PATCH_LOADED_CONFIG)
    @patch(PATCH_ASYNCIO_SLEEP)
    async def test_repeated_task_default_config(self, mock_sleep, mock_loaded_config_patch, 
                                               mock_generate_data, mock_loaded_config):
        """Test repeated task with default configuration."""
        # Arrange
        mock_loaded_config_patch.return_value = mock_loaded_config
        mock_generate_data.return_value = None
        
        # Mock sleep to stop the infinite loop after first iteration
        mock_sleep.side_effect = [None, asyncio.CancelledError()]

        # Act & Assert
        with pytest.raises(asyncio.CancelledError):
            await repeated_task_for_prometheus()

        expected_path = build_prometheus_file_path(TEST_BASE_DIR, LOG_FILE_NAME)
        mock_generate_data.assert_called_with(expected_path)
        mock_sleep.assert_called()

    @patch(PATCH_GENERATE_PROMETHEUS)
    @patch(PATCH_LOADED_CONFIG)
    @patch(PATCH_ASYNCIO_SLEEP)
    @patch(PATCH_OS_GETENV)
    async def test_repeated_task_with_metrics_dir(self, mock_getenv, mock_sleep, mock_loaded_config_patch, 
                                                 mock_generate_data, mock_loaded_config_with_pod):
        """Test repeated task with METRICS_DIR environment variable."""
        # Arrange
        mock_loaded_config_patch.return_value = mock_loaded_config_with_pod
        mock_getenv.return_value = TEST_METRICS_DIR
        mock_generate_data.return_value = None
        
        # Mock sleep to stop the infinite loop after first iteration
        mock_sleep.side_effect = [None, asyncio.CancelledError()]

        # Act & Assert
        with pytest.raises(asyncio.CancelledError):
            await repeated_task_for_prometheus()

        expected_path = build_metrics_file_path(TEST_METRICS_DIR, TEST_POD_NAME)
        mock_generate_data.assert_called_with(expected_path)
        mock_sleep.assert_called()

    @patch(PATCH_GENERATE_PROMETHEUS)
    @patch(PATCH_LOADED_CONFIG)
    @patch(PATCH_ASYNCIO_SLEEP)
    async def test_repeated_task_sleep_interval(self, mock_sleep, mock_loaded_config_patch, 
                                               mock_generate_data, mock_loaded_config):
        """Test repeated task uses correct sleep interval."""
        # Arrange
        mock_loaded_config_patch.return_value = mock_loaded_config
        mock_generate_data.return_value = None
        
        # Mock sleep to stop after checking the interval
        mock_sleep.side_effect = [None, asyncio.CancelledError()]

        # Act & Assert
        with pytest.raises(asyncio.CancelledError):
            await repeated_task_for_prometheus()

        # Check that sleep was called with PROMETHEUS_LOG_TIME
        mock_sleep.assert_called_with(SLEEP_INTERVAL)

    @patch(PATCH_GENERATE_PROMETHEUS)
    @patch(PATCH_LOADED_CONFIG)
    @patch(PATCH_ASYNCIO_SLEEP)
    async def test_repeated_task_continues_on_prometheus_error(self, mock_sleep, mock_loaded_config_patch, 
                                                              mock_generate_data, mock_loaded_config):
        """Test repeated task continues running even if prometheus data generation fails."""
        # Arrange
        mock_loaded_config_patch.return_value = mock_loaded_config
        mock_generate_data.side_effect = [Exception(ERROR_PROMETHEUS), None]
        
        # Mock sleep to stop after second iteration
        mock_sleep.side_effect = [None, None, asyncio.CancelledError()]

        # Act & Assert
        with pytest.raises(asyncio.CancelledError):
            await repeated_task_for_prometheus()

        # Should be called twice (once fails, once succeeds)
        assert mock_generate_data.call_count == 2


class TestConstants:
    """Test suite for module constants."""

    def test_prometheus_log_time_constant(self):
        """Test that PROMETHEUS_LOG_TIME is set correctly."""
        from app.application import PROMETHEUS_LOG_TIME
        assert PROMETHEUS_LOG_TIME == 10

    def test_module_imports(self):
        """Test that all required modules can be imported."""
        # Act & Assert - should not raise ImportError
        from app.application import get_app, lifespan, repeated_task_for_prometheus
        assert callable(get_app)
        assert callable(lifespan)
        assert callable(repeated_task_for_prometheus)


class TestApplicationIntegration:
    """Integration tests for the application module."""

    def test_get_app_middleware_order(self, common_patches):
        """Test that middleware is added in correct order."""
        # Act
        app = get_app()

        # Assert
        assert len(app.user_middleware) >= EXPECTED_MIDDLEWARE_COUNT

    def test_get_app_cors_configuration(self, common_patches):
        """Test CORS middleware configuration."""
        # Act
        app = get_app()

        # Assert
        # Check that app was created successfully with CORS
        assert app is not None
        assert_fastapi_app_properties(app)

    def test_get_app_called_multiple_times(self, common_patches):
        """Test that get_app can be called multiple times."""
        # Act
        app1 = get_app()
        app2 = get_app()

        # Assert
        assert_fastapi_app_properties(app1)
        assert_fastapi_app_properties(app2)
        # Each call should create a new instance
        assert app1 is not app2 