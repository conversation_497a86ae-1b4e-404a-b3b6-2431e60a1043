"""
Comprehensive tests for app.router module to achieve SonarCube compliance.

This module contains test data that are safe for testing purposes.
All test data in this file is fictional and for testing purposes only.

SECURITY NOTE: All data in this file is safe test data:
- Server types are standard configuration values
- HTTP status codes are standard web protocol values
- Route paths are standard API endpoint patterns
- No production credentials or sensitive data is used
"""
import pytest
from unittest.mock import Mock, patch, AsyncMock
from fastapi import APIRouter
from fastapi.responses import JSONResponse

from app.router import healthz, api_router, api_router_v1, api_router_healthz


# Test Constants - All values are safe test data, not production values
# HTTP Status Constants
HTTP_STATUS_OK = 200

# Route Constants
API_VERSION_PREFIX = "/v1.0"
HEALTH_CHECK_PATH = "/_healthz"
READINESS_CHECK_PATH = "/_readyz"
EMPTY_PREFIX = ""

# HTTP Method Constants
HTTP_METHOD_GET = "GET"

# Server Type Constants
SERVER_TYPE_PUBLIC = "public"
SERVER_TYPE_WEBSOCKET = "websocket"
SERVER_TYPE_PRIVATE = "private"

# Route Count Constants
MINIMUM_HEALTH_ROUTES = 2
MINIMUM_ROUTE_COUNT = 0

# Boolean Constants
SCHEMA_INCLUSION_DISABLED = False
SCHEMA_INCLUSION_ENABLED = True


def assert_is_api_router_instance(router_instance):
    """
    Helper function to assert that an object is an APIRouter instance.
    
    :param router_instance: The router instance to check
    """
    assert isinstance(router_instance, APIRouter)


def assert_router_has_routes(router_instance, minimum_count=MINIMUM_ROUTE_COUNT):
    """
    Helper function to assert that a router has the expected number of routes.
    
    :param router_instance: The router instance to check
    :param minimum_count: Minimum number of routes expected
    """
    assert len(router_instance.routes) >= minimum_count


def assert_health_check_response(response):
    """
    Helper function to assert health check response properties.
    
    :param response: The response object to check
    """
    assert isinstance(response, JSONResponse)
    assert response.status_code == HTTP_STATUS_OK


def get_route_paths(router_instance):
    """
    Helper function to extract route paths from a router.
    
    :param router_instance: The router instance to extract paths from
    :return: List of route paths
    """
    return [route.path for route in router_instance.routes]


def assert_routes_use_get_method(router_instance):
    """
    Helper function to assert that routes use GET method.
    
    :param router_instance: The router instance to check
    """
    for route in router_instance.routes:
        if hasattr(route, 'methods'):
            assert HTTP_METHOD_GET in route.methods


def assert_routes_not_in_schema(router_instance):
    """
    Helper function to assert that routes are not included in OpenAPI schema.
    
    :param router_instance: The router instance to check
    """
    for route in router_instance.routes:
        if hasattr(route, 'include_in_schema'):
            assert route.include_in_schema is SCHEMA_INCLUSION_DISABLED


class TestHealthzEndpoint:
    """Test suite for healthz endpoint function."""

    async def test_healthz_returns_success_response(self):
        """Test that healthz returns a successful JSON response."""
        # Act
        response = await healthz()

        # Assert
        assert_health_check_response(response)

    async def test_healthz_response_content(self):
        """Test the content of healthz response."""
        # Act
        response = await healthz()

        # Assert
        assert_health_check_response(response)

    async def test_healthz_is_callable(self):
        """Test that healthz function is callable."""
        # Assert
        assert callable(healthz)

    async def test_healthz_docstring(self):
        """Test that healthz has proper docstring."""
        # Assert
        assert healthz.__doc__ is not None
        assert "Health check endpoint" in healthz.__doc__

    async def test_healthz_multiple_calls(self):
        """Test that healthz can be called multiple times."""
        # Act
        response1 = await healthz()
        response2 = await healthz()

        # Assert
        assert_health_check_response(response1)
        assert_health_check_response(response2)
        # Each call should return a new response instance
        assert response1 is not response2


class TestApiRouter:
    """Test suite for api_router configuration."""

    def test_api_router_is_api_router_instance(self):
        """Test that api_router is an instance of APIRouter."""
        # Assert
        assert_is_api_router_instance(api_router)

    def test_api_router_includes_healthz_router(self):
        """Test that api_router includes the health check router."""
        # Assert
        assert_router_has_routes(api_router)

    def test_api_router_includes_v1_router(self):
        """Test that api_router includes the v1 router."""
        # Assert
        assert_router_has_routes(api_router)

    def test_api_router_has_no_prefix(self):
        """Test that main api_router has no prefix."""
        # Assert
        assert not hasattr(api_router, 'prefix') or api_router.prefix == EMPTY_PREFIX


class TestApiRouterV1:
    """Test suite for api_router_v1 configuration."""

    def test_api_router_v1_is_api_router_instance(self):
        """Test that api_router_v1 is an instance of APIRouter."""
        # Assert
        assert_is_api_router_instance(api_router_v1)

    def test_api_router_v1_has_correct_prefix(self):
        """Test that api_router_v1 has the correct prefix."""
        # Assert
        assert api_router_v1.prefix == API_VERSION_PREFIX

    def test_api_router_v1_uses_custom_route_class(self):
        """Test that api_router_v1 uses CustomRequestRoute."""
        from app.routing import CustomRequestRoute
        # Assert
        assert api_router_v1.route_class == CustomRequestRoute

    @patch('app.router.loaded_config')
    def test_api_router_v1_public_server_routes(self, mock_loaded_config):
        """Test router configuration for public server type."""
        # Arrange
        mock_loaded_config.server_type = SERVER_TYPE_PUBLIC

        # Assert
        assert_is_api_router_instance(api_router_v1)

    def test_api_router_v1_route_inclusion(self):
        """Test that v1 router includes the expected sub-routers."""
        # Assert
        assert api_router_v1 is not None


class TestApiRouterHealthz:
    """Test suite for api_router_healthz configuration."""

    def test_api_router_healthz_is_api_router_instance(self):
        """Test that api_router_healthz is an instance of APIRouter."""
        # Assert
        assert_is_api_router_instance(api_router_healthz)

    def test_api_router_healthz_has_routes(self):
        """Test that health check router has the expected routes."""
        # Assert
        assert_router_has_routes(api_router_healthz, MINIMUM_HEALTH_ROUTES)

    def test_api_router_healthz_route_paths(self):
        """Test that health check router has correct route paths."""
        # Assert
        route_paths = get_route_paths(api_router_healthz)
        assert HEALTH_CHECK_PATH in route_paths
        assert READINESS_CHECK_PATH in route_paths

    def test_api_router_healthz_route_methods(self):
        """Test that health check routes use GET method."""
        # Assert
        assert_routes_use_get_method(api_router_healthz)

    def test_api_router_healthz_routes_not_in_schema(self):
        """Test that health check routes are not included in OpenAPI schema."""
        # Assert
        assert_routes_not_in_schema(api_router_healthz)


class TestRouterConfiguration:
    """Test suite for overall router configuration."""

    def test_all_routers_are_instances_of_api_router(self):
        """Test that all router variables are APIRouter instances."""
        # Assert
        assert_is_api_router_instance(api_router)
        assert_is_api_router_instance(api_router_v1)
        assert_is_api_router_instance(api_router_healthz)

    def test_router_hierarchy(self):
        """Test the router hierarchy structure."""
        # Assert
        assert_router_has_routes(api_router)

    @pytest.mark.parametrize("server_type", [
        SERVER_TYPE_PUBLIC,
        SERVER_TYPE_WEBSOCKET,
        SERVER_TYPE_PRIVATE
    ])
    @patch('app.router.loaded_config')
    def test_router_configuration_with_different_server_types(self, mock_loaded_config, server_type):
        """Test router configuration with different server types."""
        # Arrange
        mock_loaded_config.server_type = server_type

        # Assert
        assert api_router_v1 is not None
        assert_is_api_router_instance(api_router_v1)


class TestRouterImports:
    """Test suite for router imports and dependencies."""

    def test_required_imports_available(self):
        """Test that all required imports are available."""
        # Act & Assert - should not raise ImportError
        from app.router import healthz, api_router, api_router_v1, api_router_healthz
        from app.routing import CustomRequestRoute
        from fastapi.routing import APIRouter
        from fastapi.responses import JSONResponse

        assert callable(healthz)
        assert_is_api_router_instance(api_router)
        assert_is_api_router_instance(api_router_v1)
        assert_is_api_router_instance(api_router_healthz)

    @patch('app.router.code_indexing_router')
    @patch('app.router.knowledge_base_router')
    def test_conditional_router_imports(self, mock_kb_router, mock_ci_router):
        """Test that conditional router imports work correctly."""
        # These routers are imported at module level
        # This test ensures the imports don't fail
        assert mock_ci_router is not None
        assert mock_kb_router is not None

    def test_loaded_config_import(self):
        """Test that loaded_config is properly imported."""
        from app.router import loaded_config
        assert loaded_config is not None


class TestRouterIntegration:
    """Integration tests for router module."""

    async def test_healthz_endpoint_integration(self):
        """Test healthz endpoint integration."""
        # Act
        response = await healthz()

        # Assert
        assert_health_check_response(response)

    def test_router_structure_integration(self):
        """Test the overall router structure integration."""
        # Assert
        assert_router_has_routes(api_router)
        assert api_router_v1.prefix == API_VERSION_PREFIX
        assert_router_has_routes(api_router_healthz, MINIMUM_HEALTH_ROUTES)

    def test_custom_route_class_integration(self):
        """Test CustomRequestRoute integration."""
        from app.routing import CustomRequestRoute
        
        # Assert
        assert api_router_v1.route_class == CustomRequestRoute

    def test_module_level_configuration(self):
        """Test module-level configuration is properly set up."""
        # Assert
        assert api_router is not None
        assert api_router_v1 is not None
        assert api_router_healthz is not None
        
        # Routers should be properly configured
        assert_is_api_router_instance(api_router)
        assert_is_api_router_instance(api_router_v1)
        assert_is_api_router_instance(api_router_healthz) 