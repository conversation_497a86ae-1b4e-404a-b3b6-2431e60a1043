"""
Tests for utils/exceptions.py module - Custom exception classes.

This module contains test data that are safe for testing purposes.
All error messages and values in this file are test-only and not actual production data.
"""
import pytest
from unittest.mock import Mock, patch

from utils.exceptions import ApiException, SessionExpiredException, CustomException


# Test Constants - These are safe test values, not production data  
TEST_API_ERROR_MSG = "API error occurred"
TEST_SESSION_ERROR_MSG = "Session has expired"
TEST_CUSTOM_ERROR_MSG = "Custom error occurred"
TEST_NETWORK_ERROR_MSG = "Network error"
TEST_AUTH_ERROR_MSG = "Authentication failed"
TEST_VALIDATION_ERROR_MSG = "Validation failed"

# Complex Test Data
COMPLEX_API_ERROR = {"error": "Complex error", "code": 400}
COMPLEX_SESSION_ERROR = {"error": "Session timeout", "timestamp": "2023-01-01T00:00:00Z"}
COMPLEX_CUSTOM_ERROR = {"type": "validation", "field": "email", "value": "invalid"}

# Special Character Test Data
SPECIAL_CHARS_MSG = "Error with special chars: !@#$%^&*()_+{}[]|\\:;\"'<>,.?/~`"
UNICODE_MSG = "Unicode error: 世界 🌍 Здравствуй мир!"

# Test Cases Data
EXCEPTION_MESSAGE_CASES = [
    (TEST_API_ERROR_MSG, TEST_API_ERROR_MSG),
    (None, "None"),
    ("", ""),
    (COMPLEX_API_ERROR, str(COMPLEX_API_ERROR)),
    (SPECIAL_CHARS_MSG, SPECIAL_CHARS_MSG),
    (UNICODE_MSG, UNICODE_MSG),
]

# Exception Types for Testing
EXCEPTION_TYPES = [
    (ApiException, TEST_API_ERROR_MSG),
    (SessionExpiredException, TEST_SESSION_ERROR_MSG),
    (CustomException, TEST_CUSTOM_ERROR_MSG),
]


class TestApiException:
    """Test suite for ApiException class."""

    @pytest.mark.parametrize("message,expected_str", EXCEPTION_MESSAGE_CASES)
    def test_api_exception_with_various_messages(self, message, expected_str):
        """Test ApiException with various message types."""
        # Act
        exception = ApiException(message)

        # Assert
        assert str(exception) == expected_str
        assert isinstance(exception, Exception)
        assert isinstance(exception, ApiException)

    def test_api_exception_inheritance(self):
        """Test ApiException inherits from Exception."""
        # Arrange
        exception = ApiException(TEST_API_ERROR_MSG)

        # Assert
        assert isinstance(exception, Exception)
        assert isinstance(exception, ApiException)

    def test_api_exception_can_be_raised(self):
        """Test ApiException can be raised and caught."""
        # Act & Assert
        with pytest.raises(ApiException) as exc_info:
            raise ApiException(TEST_API_ERROR_MSG)
        
        assert str(exc_info.value) == TEST_API_ERROR_MSG

    def test_api_exception_with_complex_message(self):
        """Test ApiException with complex message."""
        # Act
        exception = ApiException(COMPLEX_API_ERROR)

        # Assert
        assert str(exception) == str(COMPLEX_API_ERROR)


class TestSessionExpiredException:
    """Test suite for SessionExpiredException class."""

    @pytest.mark.parametrize("message,expected_str", [
        (TEST_SESSION_ERROR_MSG, TEST_SESSION_ERROR_MSG),
        (None, "None"),
        ("", ""),
        (COMPLEX_SESSION_ERROR, str(COMPLEX_SESSION_ERROR)),
    ])
    def test_session_expired_exception_with_various_messages(self, message, expected_str):
        """Test SessionExpiredException with various message types."""
        # Act
        exception = SessionExpiredException(message)

        # Assert
        assert str(exception) == expected_str
        assert isinstance(exception, Exception)
        assert isinstance(exception, SessionExpiredException)

    def test_session_expired_exception_inheritance(self):
        """Test SessionExpiredException inherits from Exception."""
        # Arrange
        exception = SessionExpiredException(TEST_SESSION_ERROR_MSG)

        # Assert
        assert isinstance(exception, Exception)
        assert isinstance(exception, SessionExpiredException)

    def test_session_expired_exception_can_be_raised(self):
        """Test SessionExpiredException can be raised and caught."""
        # Act & Assert
        with pytest.raises(SessionExpiredException) as exc_info:
            raise SessionExpiredException(TEST_SESSION_ERROR_MSG)
        
        assert str(exc_info.value) == TEST_SESSION_ERROR_MSG


class TestCustomException:
    """Test suite for CustomException class."""

    @pytest.mark.parametrize("message,expected_str", [
        (TEST_CUSTOM_ERROR_MSG, TEST_CUSTOM_ERROR_MSG),
        (None, "None"),
        ("", ""),
        (COMPLEX_CUSTOM_ERROR, str(COMPLEX_CUSTOM_ERROR)),
    ])
    def test_custom_exception_with_various_messages(self, message, expected_str):
        """Test CustomException with various message types."""
        # Act
        exception = CustomException(message)

        # Assert
        assert str(exception) == expected_str
        assert isinstance(exception, Exception)
        assert isinstance(exception, CustomException)

    def test_custom_exception_inheritance(self):
        """Test CustomException inherits from Exception."""
        # Arrange
        exception = CustomException(TEST_CUSTOM_ERROR_MSG)

        # Assert
        assert isinstance(exception, Exception)
        assert isinstance(exception, CustomException)

    def test_custom_exception_can_be_raised(self):
        """Test CustomException can be raised and caught."""
        # Act & Assert
        with pytest.raises(CustomException) as exc_info:
            raise CustomException(TEST_CUSTOM_ERROR_MSG)
        
        assert str(exc_info.value) == TEST_CUSTOM_ERROR_MSG


class TestExceptionInteractions:
    """Test suite for exception interactions and edge cases."""

    def test_all_exceptions_are_different_classes(self):
        """Test that all exception classes are distinct."""
        # Assert
        assert ApiException != SessionExpiredException
        assert ApiException != CustomException
        assert SessionExpiredException != CustomException

    @pytest.mark.parametrize("exception_class,test_message", EXCEPTION_TYPES)
    def test_exception_types_in_try_catch(self, exception_class, test_message):
        """Test different exception types in try-catch blocks."""
        # Act & Assert
        with pytest.raises(exception_class):
            try:
                raise exception_class(test_message)
            except exception_class as e:
                assert str(e) == test_message
                raise

    @pytest.mark.parametrize("exception_class,test_message", EXCEPTION_TYPES)
    def test_exception_caught_as_base_exception(self, exception_class, test_message):
        """Test that custom exceptions can be caught as base Exception."""
        # Act & Assert
        with pytest.raises(Exception):
            try:
                raise exception_class(test_message)
            except Exception as e:
                assert isinstance(e, exception_class)
                assert str(e) == test_message
                raise

    @pytest.mark.parametrize("exception_class", [ApiException, SessionExpiredException, CustomException])
    @pytest.mark.parametrize("message_type,test_value", [
        ("string", TEST_API_ERROR_MSG),
        ("dict", COMPLEX_API_ERROR),
        ("int", 404),
        ("list", ["error1", "error2"]),
    ])
    def test_exception_message_types(self, exception_class, message_type, test_value):
        """Test exceptions with different message types."""
        # Act
        exception = exception_class(test_value)

        # Assert
        assert str(exception) == str(test_value)

    @pytest.mark.parametrize("exception_class,test_message", EXCEPTION_TYPES)
    def test_exception_repr(self, exception_class, test_message):
        """Test exception __repr__ methods."""
        # Act
        exception = exception_class(test_message)

        # Assert
        repr_str = repr(exception)
        assert exception_class.__name__ in repr_str

    @pytest.mark.parametrize("exception_class", [ApiException, SessionExpiredException, CustomException])
    def test_exception_equality(self, exception_class):
        """Test exception equality comparison."""
        # Arrange
        exception1 = exception_class(TEST_API_ERROR_MSG)
        exception2 = exception_class(TEST_API_ERROR_MSG)
        exception3 = exception_class(TEST_CUSTOM_ERROR_MSG)

        # Assert
        # Exceptions with same message should not be equal (different instances)
        assert exception1 is not exception2
        # Exceptions with different messages should not be equal
        assert exception1 is not exception3

    @pytest.mark.parametrize("exception_class", [ApiException, SessionExpiredException, CustomException])
    def test_exception_with_special_characters(self, exception_class):
        """Test exceptions with special characters in messages."""
        # Act
        exception = exception_class(SPECIAL_CHARS_MSG)

        # Assert
        assert str(exception) == SPECIAL_CHARS_MSG
        # Ensure special characters don't break the exception
        with pytest.raises(exception_class, match=r"Error with special chars"):
            raise exception

    @pytest.mark.parametrize("exception_class", [ApiException, SessionExpiredException, CustomException])
    def test_exception_with_unicode_message(self, exception_class):
        """Test exceptions with Unicode characters."""
        # Act
        exception = exception_class(UNICODE_MSG)

        # Assert
        assert str(exception) == UNICODE_MSG
        # Ensure Unicode characters are handled properly
        with pytest.raises(exception_class):
            raise exception

    @pytest.mark.parametrize("exception_class", [ApiException, SessionExpiredException, CustomException])
    def test_exception_chaining(self, exception_class):
        """Test exception chaining functionality."""
        # Arrange
        original_error = ValueError("Original error")
        
        # Act & Assert
        with pytest.raises(exception_class) as exc_info:
            try:
                raise original_error
            except ValueError as e:
                raise exception_class("Chained error") from e
        
        # Check that exception chaining works
        assert exc_info.value.__cause__ is original_error

    @pytest.mark.parametrize("exception_class,test_message", EXCEPTION_TYPES)
    def test_exception_args_attribute(self, exception_class, test_message):
        """Test that exceptions have proper args attribute."""
        # Act
        exception = exception_class(test_message)

        # Assert
        assert hasattr(exception, 'args')
        assert exception.args == (test_message,)

    def test_exception_hierarchy_integrity(self):
        """Test that exception hierarchy is maintained correctly."""
        # Create instances
        api_exc = ApiException(TEST_API_ERROR_MSG)
        session_exc = SessionExpiredException(TEST_SESSION_ERROR_MSG)
        custom_exc = CustomException(TEST_CUSTOM_ERROR_MSG)

        # Assert hierarchy
        assert isinstance(api_exc, Exception)
        assert isinstance(session_exc, Exception)
        assert isinstance(custom_exc, Exception)
        
        # Assert they are different types
        assert type(api_exc) != type(session_exc)
        assert type(api_exc) != type(custom_exc)
        assert type(session_exc) != type(custom_exc)

    @pytest.mark.parametrize("exception_class", [ApiException, SessionExpiredException, CustomException])
    def test_exception_with_none_and_empty_values(self, exception_class):
        """Test exceptions with None and empty values."""
        test_cases = [None, "", [], {}, 0, False]
        
        for test_value in test_cases:
            # Act
            exception = exception_class(test_value)
            
            # Assert
            assert str(exception) == str(test_value)
            
            # Ensure it can be raised
            with pytest.raises(exception_class):
                raise exception 