"""
Comprehensive tests for utils/common.py module.

This module contains test data that are safe for testing purposes.
All user data, emails, phone numbers, and IDs in this file are test-only and not actual personal information.
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime
from fastapi import HTTPException, Request, status
from starlette.requests import Request as StarletteRequest

from utils.common import get_user_data_from_request
from utils.exceptions import SessionExpiredException
from clerk_integration.utils import UserData


# Test Constants - These are safe test values, not real user data
TEST_USER_ID = "user_2wAfohyRcJAYvyHN9iwoWMsniHP"
TEST_ORG_ID = "org_2wM6P0x3exs5fK4VVX4VY2EtqYA"
TEST_FIRST_NAME = "John"
TEST_LAST_NAME = "Doe"
TEST_EMAIL = "<EMAIL>"
TEST_USERNAME = "johndoe"
TEST_PHONE_NUMBER = "1234567890"
TEST_PROFILE_URL = "https://example.com/profile.jpg"
TEST_ROLE_IDS = [1, 2]
TEST_META_DATA = {"key": "value"}
TEST_WORKSPACE = []

# Fallback Test Data
FALLBACK_USER_ID = "fallback_user_id"
FALLBACK_ORG_ID = "fallback_org_id"
FALLBACK_FIRST_NAME = "Fallback"
FALLBACK_LAST_NAME = "User"
FALLBACK_EMAIL = "<EMAIL>"
FALLBACK_USERNAME = "fallbackuser"
FALLBACK_PHONE_NUMBER = "9876543210"
FALLBACK_PROFILE_URL = "https://example.com/fallback.jpg"
FALLBACK_ROLE_IDS = [3]
FALLBACK_META_DATA = {"fallback": "data"}

# Test Error Messages
SIMULATED_EXCEPTION_MSG = "Simulated exception"
STATE_ACCESS_FAILED_MSG = "State access failed"
USER_DATA_CREATION_FAILED_MSG = "UserData creation failed"
ERROR_KEY = "error"

# HTTP Status Constants
HTTP_401_STATUS = status.HTTP_401_UNAUTHORIZED


class TestGetUserDataFromRequest:
    """Test cases for get_user_data_from_request function."""

    @pytest.fixture
    def mock_request(self):
        """Create a mock request with state"""
        mock_request = Mock(spec=Request)
        mock_request.state = Mock()
        return mock_request

    @pytest.fixture
    def mock_starlette_request(self):
        """Create a mock Starlette request with state"""
        mock_request = Mock(spec=StarletteRequest)
        mock_request.state = Mock()
        return mock_request

    @pytest.fixture
    def expected_user_data_fields(self):
        """Expected UserData fields for testing"""
        return [
            'userId', 'orgId', 'firstName', 'lastName', 'email', 'username',
            'phoneNumber', 'profilePicUrl', 'active', 'roleIds', 'meta',
            'createdAt', 'updatedAt', 'workspace'
        ]

    @pytest.fixture
    def fallback_user_data(self):
        """Create fallback UserData for testing"""
        return UserData(
            _id=FALLBACK_USER_ID,
            orgId=FALLBACK_ORG_ID,
            firstName=FALLBACK_FIRST_NAME,
            lastName=FALLBACK_LAST_NAME,
            email=FALLBACK_EMAIL,
            username=FALLBACK_USERNAME,
            phoneNumber=FALLBACK_PHONE_NUMBER,
            profilePicUrl=FALLBACK_PROFILE_URL,
            active=True,
            roleIds=FALLBACK_ROLE_IDS,
            meta=FALLBACK_META_DATA,
            createdAt=datetime.now(),
            updatedAt=datetime.now(),
            workspace=TEST_WORKSPACE
        )

    @pytest.mark.asyncio
    async def test_get_user_data_from_request_success_with_clerk(self, mock_request):
        """Test get_user_data_from_request returns hardcoded UserData successfully."""
        # Act
        result = await get_user_data_from_request(mock_request)
        
        # Assert
        assert isinstance(result, UserData)
        assert result.userId == TEST_USER_ID
        assert result.orgId == TEST_ORG_ID
        assert result.firstName == TEST_FIRST_NAME
        assert result.lastName == TEST_LAST_NAME
        assert result.email == TEST_EMAIL
        assert result.username == TEST_USERNAME
        assert result.phoneNumber == TEST_PHONE_NUMBER
        assert result.profilePicUrl == TEST_PROFILE_URL
        assert result.active is True
        assert result.roleIds == TEST_ROLE_IDS
        assert result.meta == TEST_META_DATA
        assert isinstance(result.createdAt, datetime)
        assert isinstance(result.updatedAt, datetime)
        assert result.workspace == TEST_WORKSPACE

    @pytest.mark.asyncio
    async def test_get_user_data_from_request_fallback_to_request_state(self, mock_request, fallback_user_data):
        """Test get_user_data_from_request falls back to request state when exception occurs."""
        # Arrange
        mock_request.state.user_data = fallback_user_data
        
        # Patch the UserData constructor to raise an exception in the first try block
        with patch('utils.common.UserData') as mock_user_data_class:
            mock_user_data_class.side_effect = Exception(SIMULATED_EXCEPTION_MSG)
            
            # Act
            result = await get_user_data_from_request(mock_request)
            
            # Assert
            assert result == fallback_user_data

    @pytest.mark.asyncio
    async def test_get_user_data_from_request_raises_http_exception_when_all_fail(self, mock_request):
        """Test get_user_data_from_request raises HTTPException when all methods fail."""
        # Arrange
        # Mock request.state.user_data to raise an exception when accessed
        def side_effect():
            raise Exception(STATE_ACCESS_FAILED_MSG)
        
        type(mock_request.state).user_data = property(lambda self: side_effect())
        
        # Patch the UserData constructor to raise an exception in the first try block
        with patch('utils.common.UserData') as mock_user_data_class:
            mock_user_data_class.side_effect = Exception(USER_DATA_CREATION_FAILED_MSG)
            
            # Act & Assert
            with pytest.raises(HTTPException) as exc_info:
                await get_user_data_from_request(mock_request)
            
            assert exc_info.value.status_code == HTTP_401_STATUS
            assert ERROR_KEY in exc_info.value.detail
            assert exc_info.value.detail[ERROR_KEY] == SessionExpiredException.DEFAULT_MESSAGE

    @pytest.mark.asyncio
    @patch('utils.common.logger')
    async def test_get_user_data_from_request_logs_error_on_fallback_failure(self, mock_logger, mock_request):
        """Test get_user_data_from_request logs error when fallback fails."""
        # Arrange
        # Mock request.state.user_data to raise an exception when accessed
        def side_effect():
            raise Exception(STATE_ACCESS_FAILED_MSG)
        
        type(mock_request.state).user_data = property(lambda self: side_effect())
        
        # Patch the UserData constructor to raise an exception in the first try block
        with patch('utils.common.UserData') as mock_user_data_class:
            mock_user_data_class.side_effect = Exception(USER_DATA_CREATION_FAILED_MSG)
            
            # Act
            with pytest.raises(HTTPException):
                await get_user_data_from_request(mock_request)
            
            # Assert
            mock_logger.error.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_user_data_from_request_with_different_request_types(self, mock_starlette_request):
        """Test get_user_data_from_request with different request types."""
        # Act
        result = await get_user_data_from_request(mock_starlette_request)
        
        # Assert
        assert isinstance(result, UserData)
        assert result.userId == TEST_USER_ID

    @pytest.mark.asyncio
    async def test_get_user_data_from_request_returns_consistent_hardcoded_data(self):
        """Test get_user_data_from_request returns consistent hardcoded data."""
        # Arrange
        mock_request1 = Mock(spec=Request)
        mock_request1.state = Mock()
        mock_request2 = Mock(spec=Request)
        mock_request2.state = Mock()
        
        # Act
        result1 = await get_user_data_from_request(mock_request1)
        result2 = await get_user_data_from_request(mock_request2)
        
        # Assert
        expected_fields = [
            ('userId', TEST_USER_ID),
            ('orgId', TEST_ORG_ID),
            ('firstName', TEST_FIRST_NAME),
            ('lastName', TEST_LAST_NAME),
            ('email', TEST_EMAIL),
            ('username', TEST_USERNAME),
            ('phoneNumber', TEST_PHONE_NUMBER),
            ('profilePicUrl', TEST_PROFILE_URL),
            ('active', True),
            ('roleIds', TEST_ROLE_IDS),
            ('meta', TEST_META_DATA),
            ('workspace', TEST_WORKSPACE)
        ]
        
        for field_name, expected_value in expected_fields:
            assert getattr(result1, field_name) == getattr(result2, field_name)
            assert getattr(result1, field_name) == expected_value

    @pytest.mark.asyncio
    async def test_get_user_data_from_request_user_data_fields(self, mock_request, expected_user_data_fields):
        """Test get_user_data_from_request returns UserData with all required fields."""
        # Act
        result = await get_user_data_from_request(mock_request)
        
        # Assert
        for field_name in expected_user_data_fields:
            assert hasattr(result, field_name), f"Missing field: {field_name}"

    @pytest.mark.asyncio
    async def test_get_user_data_from_request_handles_request_state_exception(self, mock_request):
        """Test get_user_data_from_request handles exception when accessing request.state."""
        # Arrange
        del mock_request.state.user_data  # Remove the attribute to trigger AttributeError
        
        # Act
        result = await get_user_data_from_request(mock_request)
        
        # Assert
        assert isinstance(result, UserData)
        assert result.userId == TEST_USER_ID

    @pytest.mark.asyncio
    async def test_get_user_data_from_request_exception_detail_structure(self, mock_request):
        """Test get_user_data_from_request HTTPException detail structure."""
        # Arrange
        def side_effect():
            raise Exception(STATE_ACCESS_FAILED_MSG)
        
        type(mock_request.state).user_data = property(lambda self: side_effect())
        
        with patch('utils.common.UserData') as mock_user_data_class:
            mock_user_data_class.side_effect = Exception(USER_DATA_CREATION_FAILED_MSG)
            
            # Act & Assert
            with pytest.raises(HTTPException) as exc_info:
                await get_user_data_from_request(mock_request)
            
            # Verify the exception detail structure
            assert isinstance(exc_info.value.detail, dict)
            assert ERROR_KEY in exc_info.value.detail
            assert exc_info.value.detail[ERROR_KEY] == SessionExpiredException.DEFAULT_MESSAGE

    @pytest.mark.asyncio
    async def test_get_user_data_from_request_with_none_request(self):
        """Test get_user_data_from_request behavior with None request."""
        # Act & Assert
        with pytest.raises(AttributeError):
            await get_user_data_from_request(None)

    @pytest.mark.asyncio
    async def test_get_user_data_from_request_workspace_field(self, mock_request):
        """Test get_user_data_from_request workspace field is empty list."""
        # Act
        result = await get_user_data_from_request(mock_request)
        
        # Assert
        assert result.workspace == TEST_WORKSPACE
        assert isinstance(result.workspace, list)
        assert len(result.workspace) == 0

    @pytest.mark.asyncio
    async def test_get_user_data_from_request_datetime_fields(self, mock_request):
        """Test get_user_data_from_request datetime fields are valid."""
        # Act
        result = await get_user_data_from_request(mock_request)
        
        # Assert
        assert isinstance(result.createdAt, datetime)
        assert isinstance(result.updatedAt, datetime)
        # Verify the datetime fields are reasonable (within last minute)
        now = datetime.now()
        time_diff_created = (now - result.createdAt).total_seconds()
        time_diff_updated = (now - result.updatedAt).total_seconds()
        assert 0 <= time_diff_created <= 60  # Within last minute
        assert 0 <= time_diff_updated <= 60  # Within last minute 