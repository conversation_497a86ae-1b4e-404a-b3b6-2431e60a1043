"""
Comprehensive tests for utils/vector_db/embeddings.py

This module contains test credentials and mock data that are safe for testing purposes.
All API keys and models in this file are test values only and not actual production credentials.
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch
from typing import List
from openai import <PERSON>ync<PERSON>penAI, APIError, APITimeoutError, RateLimitError
from openai.types import CreateEmbeddingResponse, Embedding
from openai.types.create_embedding_response import Usage

from utils.vector_db.embeddings import EmbeddingGenerator
from utils.vector_db.exceptions import SearchError


# Test Constants - These are safe test values, not production credentials
TEST_API_KEY = "sk-test-api-key-for-testing-only"  # Safe test credential
DIFFERENT_API_KEY = "sk-different-test-key"  # Another safe test credential
TEST_EMBEDDING_MODEL = "text-embedding-ada-002"
ALTERNATE_EMBEDDING_MODEL = "text-embedding-3-small"
TEST_TEXT = "test text"

# Technical Constants
EMBEDDING_DIMENSIONS = 1536  # OpenAI embedding dimensions
ALTERNATE_DIMENSIONS = 512  # Alternative dimensions for testing
DEFAULT_TIMEOUT = 30  # Default API timeout
TOKEN_COUNT = 10  # Test token count

# Test Messages
API_ERROR_MSG = "API Error"
TIMEOUT_ERROR_MSG = "Timeout error"
RATE_LIMIT_ERROR_MSG = "Rate limit error"
MALFORMED_RESPONSE_ERROR = "Malformed response"
NO_DATA_ERROR = "No data in response"
ATTRIBUTE_ERROR_MSG = "Error processing embedding response"


class TestEmbeddingGenerator:
    """Test cases for EmbeddingGenerator class"""

    @pytest.fixture
    def mock_openai_client(self):
        """Create a mock AsyncOpenAI client"""
        client = Mock(spec=AsyncOpenAI)
        client.embeddings = Mock()
        client.embeddings.create = AsyncMock()
        return client

    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration"""
        config = Mock()
        config.embedding_model = TEST_EMBEDDING_MODEL
        return config

    @pytest.fixture
    def embedding_generator(self, mock_openai_client):
        """Create EmbeddingGenerator instance with mocked OpenAI client"""
        with patch('utils.vector_db.embeddings.AsyncOpenAI', return_value=mock_openai_client):
            return EmbeddingGenerator(api_key=TEST_API_KEY)

    @pytest.fixture
    def mock_embedding_data(self):
        """Create mock embedding data"""
        return [0.1, 0.2, 0.3, 0.4]

    @pytest.fixture
    def mock_embedding_response(self, mock_embedding_data):
        """Create a mock embedding response"""
        mock_embedding = Mock(spec=Embedding)
        mock_embedding.embedding = mock_embedding_data
        
        mock_response = Mock(spec=CreateEmbeddingResponse)
        mock_response.data = [mock_embedding]
        return mock_response

    @pytest.fixture
    def mock_usage_response(self, mock_embedding_data):
        """Create a mock response with usage data"""
        mock_embedding = Mock(spec=Embedding)
        mock_embedding.embedding = mock_embedding_data
        
        mock_usage = Mock(spec=Usage)
        mock_usage.prompt_tokens = TOKEN_COUNT
        mock_usage.total_tokens = TOKEN_COUNT
        
        mock_response = Mock(spec=CreateEmbeddingResponse)
        mock_response.data = [mock_embedding]
        mock_response.usage = mock_usage
        return mock_response

    @pytest.mark.parametrize("api_key", [TEST_API_KEY, DIFFERENT_API_KEY])
    def test_init_with_different_api_keys(self, mock_openai_client, api_key):
        """Test EmbeddingGenerator initialization with different API keys"""
        with patch('utils.vector_db.embeddings.AsyncOpenAI', return_value=mock_openai_client) as mock_openai:
            generator = EmbeddingGenerator(api_key=api_key)
            
            assert generator.client is mock_openai_client
            mock_openai.assert_called_once_with(api_key=api_key)

    @pytest.mark.asyncio
    @pytest.mark.parametrize("model,expected_embedding", [
        (TEST_EMBEDDING_MODEL, [0.1, 0.2, 0.3, 0.4]),
        (ALTERNATE_EMBEDDING_MODEL, [0.5, 0.6, 0.7]),
    ])
    async def test_generate_embedding_different_models(self, embedding_generator, mock_openai_client, 
                                                      mock_config, model, expected_embedding):
        """Test embedding generation with different models"""
        mock_embedding = Mock(spec=Embedding)
        mock_embedding.embedding = expected_embedding
        
        mock_response = Mock(spec=CreateEmbeddingResponse)
        mock_response.data = [mock_embedding]
        
        mock_openai_client.embeddings.create.return_value = mock_response
        mock_config.embedding_model = model
        
        with patch('utils.vector_db.embeddings.loaded_config', mock_config):
            result = await embedding_generator.generate_embedding(TEST_TEXT)
            
            assert result == expected_embedding
            mock_openai_client.embeddings.create.assert_called_once_with(
                input=[TEST_TEXT],
                model=model,
                timeout=DEFAULT_TIMEOUT
            )

    @pytest.mark.asyncio
    @pytest.mark.parametrize("input_text,expected_embedding", [
        ("", [0.0] * EMBEDDING_DIMENSIONS),
        ("This is a very long text " * 100, [0.8, 0.9, 1.0]),
        ("Hello! @#$%^&*()_+{}[]|\\:;\"'<>,.?/~`", [0.3, 0.4, 0.5]),
        ("Hello 世界! 🌍 Здравствуй мир!", [0.7, 0.8, 0.9]),
        ("Test with\nnewlines\nand\ttabs", [0.4, 0.5, 0.6]),
    ])
    async def test_generate_embedding_various_inputs(self, embedding_generator, mock_openai_client, 
                                                    mock_config, input_text, expected_embedding):
        """Test embedding generation with various input types"""
        mock_embedding = Mock(spec=Embedding)
        mock_embedding.embedding = expected_embedding
        
        mock_response = Mock(spec=CreateEmbeddingResponse)
        mock_response.data = [mock_embedding]
        
        mock_openai_client.embeddings.create.return_value = mock_response
        
        with patch('utils.vector_db.embeddings.loaded_config', mock_config):
            result = await embedding_generator.generate_embedding(input_text)
            
            assert result == expected_embedding
            mock_openai_client.embeddings.create.assert_called_once_with(
                input=[input_text],
                model=TEST_EMBEDDING_MODEL,
                timeout=DEFAULT_TIMEOUT
            )

    @pytest.mark.asyncio
    async def test_generate_embedding_api_error(self, embedding_generator, mock_openai_client, mock_config):
        """Test embedding generation with APIError"""
        # Create APIError with required parameters
        error_message = "API request failed"
        mock_openai_client.embeddings.create.side_effect = Exception(error_message)
        
        with patch('utils.vector_db.embeddings.loaded_config', mock_config):
            with pytest.raises(SearchError) as exc_info:
                await embedding_generator.generate_embedding(TEST_TEXT)
            
            assert error_message in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_generate_embedding_timeout_error(self, embedding_generator, mock_openai_client, mock_config):
        """Test embedding generation with timeout error"""
        error_message = "Request timed out"
        mock_openai_client.embeddings.create.side_effect = Exception(error_message)
        
        with patch('utils.vector_db.embeddings.loaded_config', mock_config):
            with pytest.raises(SearchError) as exc_info:
                await embedding_generator.generate_embedding(TEST_TEXT)
            
            assert "Request timed out" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_generate_embedding_rate_limit_error(self, embedding_generator, mock_openai_client, mock_config):
        """Test embedding generation with rate limit error"""
        error_message = "Rate limit exceeded"
        mock_openai_client.embeddings.create.side_effect = Exception(error_message)
        
        with patch('utils.vector_db.embeddings.loaded_config', mock_config):
            with pytest.raises(SearchError) as exc_info:
                await embedding_generator.generate_embedding(TEST_TEXT)
            
            assert "Rate limit exceeded" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_generate_embedding_generic_exception(self, embedding_generator, mock_openai_client, mock_config):
        """Test embedding generation with generic exception"""
        error_message = "Generic error"
        mock_openai_client.embeddings.create.side_effect = Exception(error_message)
        
        with patch('utils.vector_db.embeddings.loaded_config', mock_config):
            with pytest.raises(SearchError) as exc_info:
                await embedding_generator.generate_embedding(TEST_TEXT)
            
            assert error_message in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_generate_embedding_with_usage_data(self, embedding_generator, mock_openai_client, 
                                                     mock_config, mock_usage_response):
        """Test embedding generation with usage data"""
        mock_openai_client.embeddings.create.return_value = mock_usage_response
        
        with patch('utils.vector_db.embeddings.loaded_config', mock_config):
            result = await embedding_generator.generate_embedding(TEST_TEXT)
            
            assert result == [0.1, 0.2, 0.3, 0.4]
            mock_openai_client.embeddings.create.assert_called_once_with(
                input=[TEST_TEXT],
                model=TEST_EMBEDDING_MODEL,
                timeout=DEFAULT_TIMEOUT
            )

    @pytest.mark.asyncio
    async def test_generate_embedding_multiple_calls(self, embedding_generator, mock_openai_client, mock_config):
        """Test multiple embedding generation calls"""
        mock_embedding = Mock(spec=Embedding)
        mock_embedding.embedding = [0.1, 0.2, 0.3]
        
        mock_response = Mock(spec=CreateEmbeddingResponse)
        mock_response.data = [mock_embedding]
        
        mock_openai_client.embeddings.create.return_value = mock_response
        
        with patch('utils.vector_db.embeddings.loaded_config', mock_config):
            # First call
            result1 = await embedding_generator.generate_embedding("text1")
            assert result1 == [0.1, 0.2, 0.3]
            
            # Second call
            result2 = await embedding_generator.generate_embedding("text2")
            assert result2 == [0.1, 0.2, 0.3]
            
            # Verify both calls were made
            assert mock_openai_client.embeddings.create.call_count == 2

    @pytest.mark.asyncio
    async def test_generate_embedding_concurrent_calls(self, embedding_generator, mock_openai_client, mock_config):
        """Test concurrent embedding generation calls"""
        import asyncio
        
        mock_embedding = Mock(spec=Embedding)
        mock_embedding.embedding = [0.1, 0.2, 0.3]
        
        mock_response = Mock(spec=CreateEmbeddingResponse)
        mock_response.data = [mock_embedding]
        
        mock_openai_client.embeddings.create.return_value = mock_response
        
        with patch('utils.vector_db.embeddings.loaded_config', mock_config):
            # Run concurrent calls
            concurrent_count = 3
            tasks = [
                embedding_generator.generate_embedding(f"text{i}")
                for i in range(concurrent_count)
            ]
            results = await asyncio.gather(*tasks)
            
            # Verify all results are correct
            for result in results:
                assert result == [0.1, 0.2, 0.3]
            
            # Verify all calls were made
            assert mock_openai_client.embeddings.create.call_count == concurrent_count

    @pytest.mark.asyncio
    @pytest.mark.parametrize("response_data,expected_error", [
        (None, NO_DATA_ERROR),
        ([], NO_DATA_ERROR),
    ])
    async def test_generate_embedding_malformed_responses(self, embedding_generator, mock_openai_client, 
                                                         mock_config, response_data, expected_error):
        """Test embedding generation with malformed responses"""
        mock_response = Mock(spec=CreateEmbeddingResponse)
        mock_response.data = response_data
        
        mock_openai_client.embeddings.create.return_value = mock_response
        
        with patch('utils.vector_db.embeddings.loaded_config', mock_config):
            with pytest.raises(SearchError) as exc_info:
                await embedding_generator.generate_embedding(TEST_TEXT)
            
            assert expected_error in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_generate_embedding_response_attribute_error(self, embedding_generator, 
                                                              mock_openai_client, mock_config):
        """Test embedding generation when response has attribute error"""
        mock_response = Mock(spec=CreateEmbeddingResponse)
        # Create mock without embedding attribute to trigger AttributeError
        mock_data = Mock()
        del mock_data.embedding  # Remove the attribute to cause error
        mock_response.data = [mock_data]
        
        mock_openai_client.embeddings.create.return_value = mock_response
        
        with patch('utils.vector_db.embeddings.loaded_config', mock_config):
            with pytest.raises(SearchError) as exc_info:
                await embedding_generator.generate_embedding(TEST_TEXT)
            
            # The actual implementation converts AttributeError to SearchError
            assert "Failed to generate embedding" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_generate_embedding_none_response(self, embedding_generator, mock_openai_client, mock_config):
        """Test embedding generation with None response"""
        mock_openai_client.embeddings.create.return_value = None
        
        with patch('utils.vector_db.embeddings.loaded_config', mock_config):
            with pytest.raises(SearchError) as exc_info:
                await embedding_generator.generate_embedding(TEST_TEXT)
            
            assert "No response from OpenAI" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_generate_embedding_preserves_original_exception(self, embedding_generator, 
                                                                  mock_openai_client, mock_config):
        """Test that original exception is preserved in SearchError"""
        original_error = ValueError("Original error message")
        mock_openai_client.embeddings.create.side_effect = original_error
        
        with patch('utils.vector_db.embeddings.loaded_config', mock_config):
            with pytest.raises(SearchError) as exc_info:
                await embedding_generator.generate_embedding(TEST_TEXT)
            
            assert "Original error message" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_generate_embedding_numeric_text_input(self, embedding_generator, 
                                                        mock_openai_client, mock_config):
        """Test embedding generation with numeric input (should be handled as-is by the API)"""
        mock_embedding = Mock(spec=Embedding)
        mock_embedding.embedding = [0.1, 0.2, 0.3]
        
        mock_response = Mock(spec=CreateEmbeddingResponse)
        mock_response.data = [mock_embedding]
        
        mock_openai_client.embeddings.create.return_value = mock_response
        
        with patch('utils.vector_db.embeddings.loaded_config', mock_config):
            # The implementation should handle numeric input by passing it as-is
            numeric_input = 12345
            result = await embedding_generator.generate_embedding(numeric_input)
            
            assert result == [0.1, 0.2, 0.3]
            # The API call should receive the numeric input as-is
            mock_openai_client.embeddings.create.assert_called_once_with(
                input=[numeric_input],
                model=TEST_EMBEDDING_MODEL,
                timeout=DEFAULT_TIMEOUT
            )

    @pytest.mark.asyncio
    async def test_generate_embedding_error_scenarios(self, embedding_generator, mock_openai_client, mock_config):
        """Test comprehensive error scenarios in one test to reduce complexity"""
        test_scenarios = [
            (Exception("Network error"), "Network error"),
            (Exception("Authentication failed"), "Authentication failed"),
            (Exception("Service unavailable"), "Service unavailable"),
        ]
        
        with patch('utils.vector_db.embeddings.loaded_config', mock_config):
            for exception, expected_msg in test_scenarios:
                mock_openai_client.embeddings.create.side_effect = exception
                
                with pytest.raises(SearchError) as exc_info:
                    await embedding_generator.generate_embedding(TEST_TEXT)
                
                assert expected_msg in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_generate_embedding_response_validation(self, embedding_generator, 
                                                         mock_openai_client, mock_config):
        """Test response validation scenarios"""
        invalid_responses = [
            None,  # No response
            Mock(data=None),  # No data
            Mock(data=[]),  # Empty data
        ]
        
        with patch('utils.vector_db.embeddings.loaded_config', mock_config):
            for invalid_response in invalid_responses:
                mock_openai_client.embeddings.create.return_value = invalid_response
                
                with pytest.raises(SearchError) as exc_info:
                    await embedding_generator.generate_embedding(TEST_TEXT)
                
                error_msg = str(exc_info.value)
                assert any(msg in error_msg for msg in ["No response", "No data"])

    @pytest.mark.asyncio
    async def test_embedding_generator_initialization_scenarios(self):
        """Test different initialization scenarios"""
        test_keys = [TEST_API_KEY, DIFFERENT_API_KEY, "sk-another-test-key"]
        
        for api_key in test_keys:
            with patch('utils.vector_db.embeddings.AsyncOpenAI') as mock_openai:
                generator = EmbeddingGenerator(api_key=api_key)
                mock_openai.assert_called_once_with(api_key=api_key)
                assert generator.client is not None 