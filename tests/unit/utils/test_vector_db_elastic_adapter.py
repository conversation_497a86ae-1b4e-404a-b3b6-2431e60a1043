"""
Comprehensive tests for utils/vector_db/elastic_adapter.py

This module contains test credentials and mock data that are safe for testing purposes.
All API keys and URLs in this file are test values only and not actual production credentials.
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any, List
from elasticsearch import AsyncElasticsearch
from elasticsearch.exceptions import NotFoundError, ConflictError

from utils.vector_db.elastic_adapter import ElasticSearchAdapter
from utils.vector_db.exceptions import SearchError, ConnectionError as VectorDBConnectionError
from code_indexing.serializers import VectorSearchRequest, KeywordSearchRequest
from utils.exceptions import CustomException


# Test Constants - These are safe test values, not production credentials
TEST_INDEX_NAME = "test_index"
TEST_API_KEY = "test-api-key-for-testing-only"  # Safe test credential
TEST_ES_URL = "http://localhost:9200"  # Test URL only
TEST_EMBEDDING_MODEL = "text-embedding-ada-002"
TEST_KB_ID = 123
TEST_GRAPH_ID = "test-graph-id"
TEST_QUERY = "test query"
TEST_CONTENT = "test content"
TEST_TITLE = "test title"
TEST_FILE_PATH = "/test/path"
TEST_FOLDER_PATH = "/test/folder"

# Technical Constants
EMBEDDING_DIMENSIONS = 1536  # OpenAI embedding dimensions
DEFAULT_TIMEOUT = 30  # Default API timeout
DEFAULT_MAX_RESULTS = 5  # Default maximum results
LARGE_CANDIDATE_COUNT = 100  # Large candidate count for testing
THRESHOLD_HIGH = 90.0  # High threshold for testing
THRESHOLD_LOW = 10.0  # Low threshold for testing
MIN_CANDIDATES = 100  # Minimum KNN candidates

# Test Messages
CONNECTION_FAILED_MSG = "Failed to connect to Elasticsearch"
SEARCH_ERROR_MSG = "Search failed"
INSERT_ERROR_MSG = "Failed to insert document"
DELETE_ERROR_MSG = "Failed to delete document"
UPDATE_ERROR_MSG = "Failed to update document"


class TestElasticSearchAdapter:
    """Test cases for ElasticSearchAdapter class"""

    @pytest.fixture
    def mock_client(self):
        """Create a mock Elasticsearch client"""
        client = Mock(spec=AsyncElasticsearch)
        # Setup async methods
        for method in ['info', 'close', 'search', 'index', 'delete', 'update', 'bulk', 'delete_by_query', 'mget']:
            setattr(client, method, AsyncMock())
        
        # Setup indices sub-client
        client.indices = Mock()
        for method in ['exists', 'create', 'delete']:
            setattr(client.indices, method, AsyncMock())
        
        return client

    @pytest.fixture
    def mock_embedding_generator(self):
        """Create a mock embedding generator"""
        generator = Mock()
        generator.generate_embedding = AsyncMock(return_value=[0.1] * EMBEDDING_DIMENSIONS)
        return generator

    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration"""
        config = Mock()
        config.elastic_search_url = TEST_ES_URL
        config.openai_gpt4o_api_key = TEST_API_KEY
        config.embedding_model = TEST_EMBEDDING_MODEL
        return config

    @pytest.fixture
    def adapter(self, mock_config, mock_embedding_generator):
        """Create ElasticSearchAdapter instance with mocked dependencies"""
        with patch('utils.vector_db.elastic_adapter.loaded_config', mock_config), \
             patch('utils.vector_db.elastic_adapter.EmbeddingGenerator', return_value=mock_embedding_generator):
            return ElasticSearchAdapter()

    @pytest.fixture
    def mock_vector_search_request(self):
        """Create a mock VectorSearchRequest"""
        return VectorSearchRequest(
            graph_id=TEST_GRAPH_ID,
            query=TEST_QUERY,
            max_results=DEFAULT_MAX_RESULTS,
            entire_workspace=True,
            threshold=THRESHOLD_HIGH
        )

    @pytest.fixture
    def mock_keyword_search_request(self):
        """Create a mock KeywordSearchRequest"""
        return KeywordSearchRequest(
            graph_id=TEST_GRAPH_ID,
            keywords=["test", "keyword"],
            max_results=DEFAULT_MAX_RESULTS,
            entire_workspace=True
        )

    @pytest.fixture
    def mock_search_response(self):
        """Create a mock search response"""
        return {
            "hits": {
                "total": {"value": 2, "relation": "eq"},
                "hits": [
                    {
                        "_score": 0.95,
                        "_source": {
                            "id": "doc1",
                            "content": TEST_CONTENT,
                            "title": TEST_TITLE,
                            "file_path": TEST_FILE_PATH,
                            "knowledge_base_id": TEST_KB_ID
                        }
                    },
                    {
                        "_score": 0.85,
                        "_source": {
                            "id": "doc2",
                            "content": f"{TEST_CONTENT} 2",
                            "title": f"{TEST_TITLE} 2",
                            "file_path": f"{TEST_FILE_PATH}_2",
                            "knowledge_base_id": TEST_KB_ID
                        }
                    }
                ]
            }
        }

    @pytest.fixture
    def sample_documents(self):
        """Create sample documents for testing"""
        return [
            {
                "id": "doc1",
                "content": TEST_CONTENT,
                "title": TEST_TITLE,
                "file_path": TEST_FILE_PATH,
                "knowledge_base_id": TEST_KB_ID
            },
            {
                "id": "doc2",
                "content": f"{TEST_CONTENT} 2",
                "title": f"{TEST_TITLE} 2",
                "file_path": f"{TEST_FILE_PATH}_2",
                "knowledge_base_id": TEST_KB_ID
            }
        ]

    def test_init_basic(self, mock_config, mock_embedding_generator):
        """Test basic ElasticSearchAdapter initialization"""
        with patch('utils.vector_db.elastic_adapter.loaded_config', mock_config), \
             patch('utils.vector_db.elastic_adapter.EmbeddingGenerator', return_value=mock_embedding_generator):
            adapter = ElasticSearchAdapter()
            
            assert adapter.client is None  # Client is initially None until connected
            assert adapter.embedding_generator is mock_embedding_generator

    @pytest.mark.asyncio
    async def test_connect_success(self, adapter, mock_client, mock_config):
        """Test successful connection to Elasticsearch"""
        with patch('utils.vector_db.elastic_adapter.AsyncElasticsearch', return_value=mock_client), \
             patch('utils.vector_db.elastic_adapter.loaded_config', mock_config):
            
            await adapter.connect()
            
            assert adapter.client is mock_client
            mock_client.info.assert_called_once()

    @pytest.mark.asyncio
    async def test_connect_failure(self, adapter, mock_config):
        """Test connection failure with retries"""
        mock_client = Mock(spec=AsyncElasticsearch)
        mock_client.info.side_effect = Exception(CONNECTION_FAILED_MSG)
        
        with patch('utils.vector_db.elastic_adapter.AsyncElasticsearch', return_value=mock_client), \
             patch('utils.vector_db.elastic_adapter.loaded_config', mock_config), \
             patch('asyncio.sleep', new_callable=AsyncMock):
            
            with pytest.raises(VectorDBConnectionError, match=CONNECTION_FAILED_MSG):
                await adapter.connect(retries=1, delay=0.1)

    @pytest.mark.asyncio
    async def test_close_connection(self, adapter, mock_client):
        """Test closing Elasticsearch connection"""
        adapter.client = mock_client
        
        await adapter.close()
        
        mock_client.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_close_no_client(self, adapter):
        """Test closing when no client exists"""
        adapter.client = None
        
        await adapter.close()
        
        # Should not raise any error

    @pytest.mark.asyncio
    async def test_create_index_success(self, adapter, mock_client):
        """Test successful index creation"""
        adapter.client = mock_client
        mock_client.indices.exists.return_value = False
        mock_client.indices.create.return_value = {"acknowledged": True}
        
        result = await adapter.create_index(TEST_INDEX_NAME)
        
        assert result["acknowledged"] is True
        mock_client.indices.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_index_already_exists(self, adapter, mock_client):
        """Test index creation when index already exists"""
        adapter.client = mock_client
        mock_client.indices.exists.return_value = True
        
        result = await adapter.create_index(TEST_INDEX_NAME)
        
        assert result["acknowledged"] is False
        assert "already exists" in result["message"]
        mock_client.indices.create.assert_not_called()

    @pytest.mark.asyncio
    async def test_create_index_failure(self, adapter, mock_client):
        """Test index creation failure"""
        adapter.client = mock_client
        mock_client.indices.exists.return_value = False
        mock_client.indices.create.side_effect = Exception("Creation failed")
        
        with pytest.raises(SearchError, match="Failed to create index"):
            await adapter.create_index(TEST_INDEX_NAME)

    @pytest.mark.asyncio
    async def test_knn_similarity_search_success(self, adapter, mock_client, mock_embedding_generator, 
                                               mock_vector_search_request, mock_search_response):
        """Test successful KNN similarity search"""
        adapter.client = mock_client
        mock_client.search.return_value = mock_search_response
        
        result = await adapter.knn_similarity_search(
            mock_vector_search_request, TEST_INDEX_NAME, TEST_KB_ID
        )
        
        assert result["hits"]["total"]["value"] == 2
        assert len(result["hits"]["hits"]) == 2
        assert result["hits"]["hits"][0]["_score"] == 0.95
        mock_embedding_generator.generate_embedding.assert_called_once_with(TEST_QUERY)

    @pytest.mark.asyncio
    async def test_knn_similarity_search_no_workspace(self, adapter, mock_client, mock_embedding_generator):
        """Test KNN search when not searching entire workspace and no paths specified"""
        adapter.client = mock_client
        request = VectorSearchRequest(
            graph_id=TEST_GRAPH_ID,
            query=TEST_QUERY,
            max_results=DEFAULT_MAX_RESULTS,
            entire_workspace=False,
            file_paths=None,
            folder_paths=None
        )
        
        result = await adapter.knn_similarity_search(request, TEST_INDEX_NAME, TEST_KB_ID)
        
        assert result["hits"]["total"] == 0
        assert result["hits"]["hits"] == []
        mock_client.search.assert_not_called()

    @pytest.mark.asyncio
    @pytest.mark.parametrize("max_results,expected_candidates", [
        (5, 50),    # 5 * 10 = 50
        (15, 75),   # 15 * 5 = 75
        (30, 100),  # max(100, 30 * 3) = 100
        (50, 150),  # 50 * 3 = 150
    ])
    async def test_knn_similarity_search_candidate_calculation(self, adapter, mock_client, 
                                                             mock_embedding_generator, 
                                                             max_results, expected_candidates):
        """Test KNN search candidate calculation based on max_results"""
        adapter.client = mock_client
        request = VectorSearchRequest(
            graph_id=TEST_GRAPH_ID,
            query=TEST_QUERY,
            max_results=max_results,
            entire_workspace=True
        )
        
        mock_response = {"hits": {"hits": []}}
        mock_client.search.return_value = mock_response
        
        await adapter.knn_similarity_search(request, TEST_INDEX_NAME, TEST_KB_ID)
        
        search_call = mock_client.search.call_args[1]["body"]
        assert search_call["knn"]["num_candidates"] == expected_candidates

    @pytest.mark.asyncio
    async def test_knn_similarity_search_with_paths(self, adapter, mock_client, mock_embedding_generator, 
                                                   mock_search_response):
        """Test KNN search with file and folder paths"""
        adapter.client = mock_client
        request = VectorSearchRequest(
            graph_id=TEST_GRAPH_ID,
            query=TEST_QUERY,
            max_results=DEFAULT_MAX_RESULTS,
            entire_workspace=False,
            file_paths=[TEST_FILE_PATH],
            folder_paths=[TEST_FOLDER_PATH]
        )
        
        mock_client.search.return_value = mock_search_response
        
        result = await adapter.knn_similarity_search(request, TEST_INDEX_NAME, TEST_KB_ID)
        
        assert result["hits"]["total"]["value"] == 2
        
        # Verify filters were applied
        search_call = mock_client.search.call_args[1]["body"]
        filters = search_call["knn"]["filter"]
        assert any("title.keyword" in str(f) for f in filters)

    @pytest.mark.asyncio
    async def test_knn_similarity_search_exception(self, adapter, mock_client, mock_embedding_generator):
        """Test KNN search with exception"""
        adapter.client = mock_client
        request = VectorSearchRequest(
            graph_id=TEST_GRAPH_ID,
            query=TEST_QUERY,
            max_results=DEFAULT_MAX_RESULTS,
            entire_workspace=True
        )
        
        mock_client.search.side_effect = Exception(SEARCH_ERROR_MSG)
        
        with pytest.raises(SearchError, match="Failed to perform KNN similarity search"):
            await adapter.knn_similarity_search(request, TEST_INDEX_NAME, TEST_KB_ID)

    @pytest.mark.asyncio
    async def test_keyword_search_source_code_success(self, adapter, mock_client, 
                                                     mock_keyword_search_request, mock_search_response):
        """Test successful keyword search"""
        adapter.client = mock_client
        mock_client.search.return_value = mock_search_response
        
        result = await adapter.keyword_search_source_code(
            mock_keyword_search_request, TEST_INDEX_NAME, TEST_KB_ID
        )
        
        assert result["hits"]["total"]["value"] == 2
        assert len(result["hits"]["hits"]) == 2
        
        # Verify query structure
        search_call = mock_client.search.call_args[1]["body"]
        assert "query" in search_call
        assert "bool" in search_call["query"]

    @pytest.mark.asyncio
    async def test_keyword_search_source_code_no_workspace(self, adapter, mock_client):
        """Test keyword search when not searching entire workspace and no paths specified"""
        adapter.client = mock_client
        request = KeywordSearchRequest(
            graph_id=TEST_GRAPH_ID,
            keywords=["test"],
            max_results=DEFAULT_MAX_RESULTS,
            entire_workspace=False,
            file_paths=None,
            folder_paths=None
        )
        
        result = await adapter.keyword_search_source_code(request, TEST_INDEX_NAME, TEST_KB_ID)
        
        assert result["hits"]["total"] == 0
        assert result["hits"]["hits"] == []
        mock_client.search.assert_not_called()

    @pytest.mark.asyncio
    async def test_keyword_search_source_code_exception(self, adapter, mock_client):
        """Test keyword search with exception"""
        adapter.client = mock_client
        request = KeywordSearchRequest(
            graph_id=TEST_GRAPH_ID,
            keywords=["test"],
            max_results=DEFAULT_MAX_RESULTS,
            entire_workspace=True
        )
        
        mock_client.search.side_effect = Exception(SEARCH_ERROR_MSG)
        
        with pytest.raises(SearchError, match="Failed to perform keyword search"):
            await adapter.keyword_search_source_code(request, TEST_INDEX_NAME, TEST_KB_ID)

    @pytest.mark.asyncio
    async def test_search_and_fetch_content_success(self, adapter, mock_client, mock_embedding_generator):
        """Test successful search and fetch content"""
        from knowledge_base.serializers import GetVectorSearchRequest
        
        adapter.client = mock_client
        request = GetVectorSearchRequest(
            query=TEST_QUERY,
            knowledge_base_id=[TEST_KB_ID],
            matching_percentage=80.0,
            top_answer_count=3
        )
        
        mock_response = {
            "hits": {
                "hits": [
                    {
                        "_score": 0.95,
                        "_source": {
                            "id": "logical_id#1",
                            "content": TEST_CONTENT,
                            "is_chunked": True,
                            "title": TEST_TITLE,
                            "chunk_num": 1
                        }
                    }
                ]
            }
        }
        mock_client.search.return_value = mock_response
        
        result = await adapter.search_and_fetch_content(request, TEST_INDEX_NAME)
        
        assert len(result) == 1
        assert result[0]["content"] == TEST_CONTENT
        assert result[0]["title"] == TEST_TITLE

    @pytest.mark.asyncio
    async def test_search_and_fetch_content_wrong_dimensions(self, adapter, mock_embedding_generator):
        """Test search and fetch content with wrong embedding dimensions"""
        from knowledge_base.serializers import GetVectorSearchRequest
        
        request = GetVectorSearchRequest(
            query=TEST_QUERY,
            knowledge_base_id=[TEST_KB_ID],
            matching_percentage=80.0,
            top_answer_count=3
        )
        
        # Mock wrong dimensions
        mock_embedding_generator.generate_embedding.return_value = [0.1] * 512  # Wrong dimension
        
        with pytest.raises(CustomException, match="Query vector has incorrect dimensions"):
            await adapter.search_and_fetch_content(request, TEST_INDEX_NAME)

    @pytest.mark.asyncio
    async def test_search_and_fetch_content_exception(self, adapter, mock_client, mock_embedding_generator):
        """Test search and fetch content with exception"""
        from knowledge_base.serializers import GetVectorSearchRequest
        
        adapter.client = mock_client
        request = GetVectorSearchRequest(
            query=TEST_QUERY,
            knowledge_base_id=[TEST_KB_ID],
            matching_percentage=80.0,
            top_answer_count=3
        )
        
        mock_client.search.side_effect = Exception(SEARCH_ERROR_MSG)
        
        with pytest.raises(CustomException, match="Failed to perform KNN search and fetch content"):
            await adapter.search_and_fetch_content(request, TEST_INDEX_NAME)

    @pytest.mark.asyncio
    async def test_bulk_insert_success(self, adapter, mock_client, sample_documents):
        """Test successful bulk insert"""
        adapter.client = mock_client
        
        with patch('utils.vector_db.elastic_adapter.async_bulk') as mock_bulk:
            mock_bulk.return_value = (2, [])
            
            result = await adapter.bulk_insert(TEST_INDEX_NAME, sample_documents, {}, [])
            
            assert result["success"] == 2
            assert result["failed"] == []
            mock_bulk.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_file_content_success(self, adapter, mock_client):
        """Test successful file content retrieval"""
        adapter.client = mock_client
        
        # Mock the initial search response with chunk_references
        mock_search_response = {
            "hits": {
                "total": {"value": 1},
                "hits": [
                    {
                        "_source": {
                            "chunk_references": ["chunk1", "chunk2"]
                        }
                    }
                ]
            }
        }
        
        # Mock the mget response for chunk retrieval
        mock_mget_response = {
            "docs": [
                {
                    "_id": "chunk1",
                    "found": True,
                    "_source": {
                        "content": "Part 1 of content"
                    }
                },
                {
                    "_id": "chunk2",
                    "found": True,
                    "_source": {
                        "content": "Part 2 of content"
                    }
                }
            ]
        }
        
        # Setup mock responses
        mock_client.search.return_value = mock_search_response
        mock_client.mget.return_value = mock_mget_response
        
        result = await adapter.get_file_content(TEST_INDEX_NAME, TEST_FILE_PATH, TEST_KB_ID)
        
        assert result == "Part 1 of content\nPart 2 of content"
        assert mock_client.search.call_count == 1
        assert mock_client.mget.call_count == 1

    @pytest.mark.asyncio
    async def test_get_file_content_not_found(self, adapter, mock_client):
        """Test file content retrieval when file not found"""
        adapter.client = mock_client
        mock_response = {"hits": {"total": {"value": 0}, "hits": []}}
        mock_client.search.return_value = mock_response
        
        result = await adapter.get_file_content(TEST_INDEX_NAME, "nonexistent.py", TEST_KB_ID)
        
        assert result is None

    @pytest.mark.asyncio
    async def test_delete_documents_by_kb_id_success(self, adapter, mock_client):
        """Test successful deletion of documents by kb_id"""
        adapter.client = mock_client
        mock_response = {"deleted": 5, "total": 5, "failures": []}
        mock_client.delete_by_query.return_value = mock_response
        
        result = await adapter.delete_documents_by_kb_id(TEST_INDEX_NAME, TEST_KB_ID)
        
        assert result["deleted"] == 5
        assert result["total"] == 5
        assert result["failed"] == 0
        mock_client.delete_by_query.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_documents_by_kb_id_failure(self, adapter, mock_client):
        """Test deletion failure by kb_id"""
        adapter.client = mock_client
        mock_client.delete_by_query.side_effect = Exception("Delete failed")
        
        with pytest.raises(SearchError, match="Failed to delete documents"):
            await adapter.delete_documents_by_kb_id(TEST_INDEX_NAME, TEST_KB_ID)

    @pytest.mark.asyncio
    async def test_delete_document_by_id_success(self, adapter, mock_client):
        """Test successful document deletion by ID"""
        adapter.client = mock_client
        mock_response = {"result": "deleted", "_id": "doc1", "_index": TEST_INDEX_NAME}
        mock_client.delete.return_value = mock_response
        
        with patch.object(adapter, 'connect', new_callable=AsyncMock), \
             patch.object(adapter, 'close', new_callable=AsyncMock):
            
            result = await adapter.delete_document_by_id(TEST_INDEX_NAME, "doc1")
            
            assert result["result"] == "deleted"
            assert result["id"] == "doc1"

    @pytest.mark.asyncio
    async def test_delete_document_by_id_failure(self, adapter, mock_client):
        """Test document deletion failure"""
        adapter.client = mock_client
        mock_client.delete.side_effect = Exception("Delete failed")
        
        with patch.object(adapter, 'connect', new_callable=AsyncMock), \
             patch.object(adapter, 'close', new_callable=AsyncMock):
            
            with pytest.raises(SearchError, match="Failed to delete document"):
                await adapter.delete_document_by_id(TEST_INDEX_NAME, "doc1")

    @pytest.mark.asyncio
    async def test_delete_document_by_id_prefix_success(self, adapter, mock_client):
        """Test successful deletion by ID prefix"""
        adapter.client = mock_client
        mock_response = {"deleted": 3, "total": 3, "version_conflicts": 0}
        mock_client.delete_by_query.return_value = mock_response
        
        with patch.object(adapter, 'connect', new_callable=AsyncMock), \
             patch.object(adapter, 'close', new_callable=AsyncMock):
            
            result = await adapter.delete_document_by_id_prefix(TEST_INDEX_NAME, "doc_prefix")
            
            assert result["deleted"] == 3

    def test_title_dot_keyword_constant(self, adapter):
        """Test that TITLE_DOT_KEYWORD constant is properly defined"""
        assert hasattr(adapter, 'TITLE_DOT_KEYWORD')
        assert adapter.TITLE_DOT_KEYWORD == "title.keyword"

    @pytest.mark.asyncio
    async def test_embedding_generation_error_handling(self, adapter, mock_embedding_generator):
        """Test embedding generation error handling"""
        mock_embedding_generator.generate_embedding.side_effect = Exception("Embedding failed")
        
        request = VectorSearchRequest(
            graph_id=TEST_GRAPH_ID,
            query=TEST_QUERY,
            max_results=DEFAULT_MAX_RESULTS,
            entire_workspace=True
        )
        
        with pytest.raises(SearchError, match="Failed to perform KNN similarity search"):
            await adapter.knn_similarity_search(request, TEST_INDEX_NAME, TEST_KB_ID)

    @pytest.mark.asyncio
    async def test_search_with_complex_filters(self, adapter, mock_client, mock_embedding_generator):
        """Test search with complex file and folder filters"""
        adapter.client = mock_client
        request = VectorSearchRequest(
            graph_id=TEST_GRAPH_ID,
            query=TEST_QUERY,
            max_results=DEFAULT_MAX_RESULTS,
            entire_workspace=False,
            file_paths=["/path/file1.txt", "/path/file2.txt"],
            folder_paths=["/folder1", "/folder2"]
        )
        
        mock_response = {"hits": {"hits": []}}
        mock_client.search.return_value = mock_response
        
        result = await adapter.knn_similarity_search(request, TEST_INDEX_NAME, TEST_KB_ID)
        
        # Verify the complex filter was applied
        search_call = mock_client.search.call_args[1]["body"]
        filters = search_call["knn"]["filter"]
        
        # Should have multiple filters for files and folders
        assert len(filters) >= 2  # At least KB ID and path filters
        
        # Verify structure
        assert result["hits"]["hits"] == [] 