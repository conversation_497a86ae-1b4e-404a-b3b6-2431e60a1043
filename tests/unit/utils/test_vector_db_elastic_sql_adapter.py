"""
Comprehensive tests for utils/vector_db/elastic_sql_adapter.py

This module contains test credentials and mock data that are safe for testing purposes.
All API keys and URLs in this file are test values only and not actual production credentials.
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any, List
from elasticsearch import AsyncElasticsearch
from elasticsearch.exceptions import NotFoundError, ConflictError

from utils.vector_db.elastic_sql_adapter import ElasticsearchSQLAdapter
from utils.vector_db.exceptions import SearchError
from knowledge_base.serializers import GetVectorSearchRequest
from utils.exceptions import CustomException


# Test Constants - These are safe test values, not production credentials
TEST_INDEX_NAME = "test_index"
TEST_API_KEY = "test-api-key-for-testing-only"  # Safe test credential
TEST_ES_URL = "http://localhost:9200"  # Test URL only
TEST_EMBEDDING_MODEL = "text-embedding-ada-002"
TEST_KB_ID = 123
TEST_USER_QUERY = "test query"

# Technical Constants  
EMBEDDING_DIMENSIONS = 1536  # OpenAI embedding dimensions
DEFAULT_TIMEOUT = 30  # Default API timeout in seconds
DEFAULT_PORT = 9200  # Elasticsearch default port
MIN_CANDIDATES = 100  # Minimum KNN candidates
CANDIDATE_MULTIPLIER = 5  # Multiplier for calculating candidates

# Test Messages
CREATION_FAILED_MSG = "Creation failed"
ALREADY_EXISTS_MSG = "already exists"
CONNECTION_FAILED_MSG = "Failed to connect to Elasticsearch"


class TestElasticsearchSQLAdapter:
    """Test cases for ElasticsearchSQLAdapter class"""

    @pytest.fixture
    def mock_client(self):
        """Create a mock Elasticsearch client with common methods"""
        client = Mock(spec=AsyncElasticsearch)
        # Setup async methods
        for method in ['ping', 'close', 'search', 'index', 'delete_by_query']:
            setattr(client, method, AsyncMock())
        
        # Setup indices sub-client
        client.indices = Mock()
        for method in ['exists', 'create', 'delete']:
            setattr(client.indices, method, AsyncMock())
        
        return client

    @pytest.fixture
    def mock_embedding_generator(self):
        """Create a mock embedding generator"""
        generator = Mock()
        generator.generate_embedding = AsyncMock(return_value=[0.1] * EMBEDDING_DIMENSIONS)
        return generator

    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration"""
        config = Mock()
        config.elastic_search_url = TEST_ES_URL
        config.openai_gpt4o_api_key = TEST_API_KEY
        config.embedding_model = TEST_EMBEDDING_MODEL
        return config

    @pytest.fixture
    def adapter(self, mock_client, mock_embedding_generator, mock_config):
        """Create ElasticsearchSQLAdapter instance with mocked dependencies"""
        with patch('utils.vector_db.elastic_sql_adapter.AsyncElasticsearch', return_value=mock_client), \
             patch('utils.vector_db.elastic_sql_adapter.loaded_config', mock_config), \
             patch('utils.vector_db.elastic_sql_adapter.EmbeddingGenerator', return_value=mock_embedding_generator):
            return ElasticsearchSQLAdapter()

    @pytest.fixture
    def mock_successful_response(self):
        """Create a mock successful response with .body attribute"""
        response = Mock()
        response.body = {"acknowledged": True}
        return response

    @pytest.fixture
    def sample_documents(self):
        """Create sample documents for testing"""
        return [
            {"id": "1", "text": "Sample text 1", "metadata": {"kb_id": f"kb{TEST_KB_ID}"}},
            {"id": "2", "text": "Sample text 2", "metadata": {"kb_id": f"kb{TEST_KB_ID}"}},
        ]

    @pytest.fixture
    def mock_search_request(self):
        """Create a mock GetVectorSearchRequest"""
        return GetVectorSearchRequest(
            query=TEST_USER_QUERY,
            knowledge_base_id=[TEST_KB_ID],
            matching_percentage=80.0,
            top_answer_count=5
        )

    def test_init_basic(self, mock_client, mock_embedding_generator, mock_config):
        """Test basic ElasticsearchSQLAdapter initialization"""
        with patch('utils.vector_db.elastic_sql_adapter.AsyncElasticsearch', return_value=mock_client), \
             patch('utils.vector_db.elastic_sql_adapter.loaded_config', mock_config), \
             patch('utils.vector_db.elastic_sql_adapter.EmbeddingGenerator', return_value=mock_embedding_generator):
            adapter = ElasticsearchSQLAdapter()
            
            assert adapter.client is mock_client
            assert adapter.embedding_generator is mock_embedding_generator
            assert adapter._is_connected is False

    def test_init_with_kwargs(self, mock_client, mock_embedding_generator, mock_config):
        """Test ElasticsearchSQLAdapter initialization with additional kwargs"""
        custom_timeout = 45
        custom_retries = 5
        
        with patch('utils.vector_db.elastic_sql_adapter.AsyncElasticsearch', return_value=mock_client) as mock_es, \
             patch('utils.vector_db.elastic_sql_adapter.loaded_config', mock_config), \
             patch('utils.vector_db.elastic_sql_adapter.EmbeddingGenerator', return_value=mock_embedding_generator):
            
            ElasticsearchSQLAdapter(timeout=custom_timeout, max_retries=custom_retries)
            
            mock_es.assert_called_once_with(
                hosts=[TEST_ES_URL],
                timeout=custom_timeout,
                max_retries=custom_retries
            )

    @pytest.mark.asyncio
    @pytest.mark.parametrize("ping_result,expected_connected", [
        (True, True),
        (False, False),
    ])
    async def test_connect(self, adapter, mock_client, ping_result, expected_connected):
        """Test connection to Elasticsearch with different ping results"""
        mock_client.ping.return_value = ping_result
        
        if expected_connected:
            await adapter.connect()
            assert adapter._is_connected is True
        else:
            with pytest.raises(ConnectionError, match=CONNECTION_FAILED_MSG):
                await adapter.connect()
            assert adapter._is_connected is False

    @pytest.mark.asyncio
    async def test_connect_already_connected(self, adapter, mock_client):
        """Test connect when already connected"""
        adapter._is_connected = True
        
        await adapter.connect()
        
        mock_client.ping.assert_not_called()
        assert adapter._is_connected is True

    @pytest.mark.asyncio
    async def test_close_connection(self, adapter, mock_client):
        """Test closing Elasticsearch connection"""
        adapter._is_connected = True
        
        await adapter.close()
        
        mock_client.close.assert_called_once()
        assert adapter._is_connected is False

    @pytest.mark.asyncio
    async def test_close_no_client(self, adapter):
        """Test closing when no client exists"""
        adapter.client = None
        
        await adapter.close()
        
        assert adapter._is_connected is False

    @pytest.mark.asyncio
    async def test_create_index_success(self, adapter, mock_client, mock_successful_response):
        """Test successful index creation"""
        mock_client.indices.exists.return_value = False
        mock_client.indices.create.return_value = mock_successful_response
        
        result = await adapter.create_index(TEST_INDEX_NAME)
        
        assert result["acknowledged"] is True
        mock_client.indices.exists.assert_called_once_with(index=TEST_INDEX_NAME)
        mock_client.indices.create.assert_called_once_with(
            index=TEST_INDEX_NAME,
            body=adapter.INDEXING_DEFAULT_MAPPING_FOR_SQL
        )

    @pytest.mark.asyncio
    async def test_create_index_already_exists(self, adapter, mock_client):
        """Test index creation when index already exists"""
        mock_client.indices.exists.return_value = True
        
        result = await adapter.create_index(TEST_INDEX_NAME)
        
        assert result["acknowledged"] is False
        assert ALREADY_EXISTS_MSG in result["message"]
        mock_client.indices.create.assert_not_called()

    @pytest.mark.asyncio
    async def test_create_index_failure(self, adapter, mock_client):
        """Test index creation failure"""
        mock_client.indices.exists.return_value = False
        mock_client.indices.create.side_effect = Exception(CREATION_FAILED_MSG)
        
        result = await adapter.create_index(TEST_INDEX_NAME)
        
        assert result["acknowledged"] is False
        assert CREATION_FAILED_MSG in result["error"]

    @pytest.mark.asyncio
    async def test_delete_index_success(self, adapter, mock_client, mock_successful_response):
        """Test successful index deletion"""
        mock_client.indices.exists.return_value = True
        mock_client.indices.delete.return_value = mock_successful_response
        
        result = await adapter.delete_index(TEST_INDEX_NAME)
        
        assert result["acknowledged"] is True
        mock_client.indices.delete.assert_called_once_with(index=TEST_INDEX_NAME)

    @pytest.mark.asyncio
    async def test_delete_index_not_found(self, adapter, mock_client):
        """Test index deletion when index doesn't exist"""
        mock_client.indices.exists.return_value = False
        
        result = await adapter.delete_index(TEST_INDEX_NAME)
        
        assert result["acknowledged"] is False
        assert "not found" in result["error"]
        mock_client.indices.delete.assert_not_called()

    @pytest.mark.asyncio
    async def test_delete_index_with_exception(self, adapter, mock_client):
        """Test index deletion when NotFoundError is raised"""
        mock_client.indices.exists.return_value = True
        mock_meta = Mock()
        mock_meta.status = 404
        mock_client.indices.delete.side_effect = NotFoundError(
            message="Index not found", 
            meta=mock_meta, 
            body={"error": "not_found"}
        )
        
        result = await adapter.delete_index(TEST_INDEX_NAME)
        
        assert result["acknowledged"] is False
        assert "not found during deletion" in result["error"]

    @pytest.mark.asyncio
    async def test_delete_index_generic_failure(self, adapter, mock_client):
        """Test index deletion failure"""
        mock_client.indices.exists.return_value = True
        mock_client.indices.delete.side_effect = Exception("Deletion failed")
        
        result = await adapter.delete_index(TEST_INDEX_NAME)
        
        assert result["acknowledged"] is False
        assert "Deletion failed" in result["error"]

    @pytest.mark.asyncio
    @pytest.mark.parametrize("has_id_field", [True, False])
    async def test_insert_doc_success(self, adapter, mock_client, has_id_field):
        """Test successful document insertion with and without ID"""
        doc_id = "doc1" if has_id_field else None
        mock_response = Mock()
        mock_response.body = {
            "_id": doc_id or "generated_id", 
            "result": "created", 
            "_index": TEST_INDEX_NAME
        }
        mock_client.index.return_value = mock_response
        
        document = {"text": "Sample text", "metadata": {"kb_id": f"kb{TEST_KB_ID}"}}
        if has_id_field:
            document["id"] = doc_id
            
        result = await adapter.insert_doc(TEST_INDEX_NAME, document)
        
        expected_id = doc_id or "generated_id"
        assert result["id"] == expected_id
        assert result["result"] == "created"
        assert result["index"] == TEST_INDEX_NAME

    @pytest.mark.asyncio
    async def test_insert_doc_with_underscore_id(self, adapter, mock_client):
        """Test document insertion with _id in document"""
        doc_id = "from_doc"
        mock_response = Mock()
        mock_response.body = {"_id": doc_id, "result": "created", "_index": TEST_INDEX_NAME}
        mock_client.index.return_value = mock_response
        
        document = {"_id": doc_id, "text": "Sample text", "metadata": {"kb_id": f"kb{TEST_KB_ID}"}}
        result = await adapter.insert_doc(TEST_INDEX_NAME, document)
        
        assert result["id"] == doc_id
        expected_doc = {"text": "Sample text", "metadata": {"kb_id": f"kb{TEST_KB_ID}"}}
        mock_client.index.assert_called_once_with(
            index=TEST_INDEX_NAME,
            id=doc_id,
            document=expected_doc
        )

    @pytest.mark.asyncio
    async def test_insert_doc_conflict(self, adapter, mock_client):
        """Test document insertion with conflict exception"""
        mock_meta = Mock()
        mock_meta.explanation = "Version conflict"
        conflict_error = ConflictError(
            message="Document already exists",
            meta=mock_meta,
            body={"error": "version_conflict"}
        )
        mock_client.index.side_effect = conflict_error
        
        document = {"id": "existing_id", "text": "Sample text"}
        result = await adapter.insert_doc(TEST_INDEX_NAME, document)
        
        assert result["id"] == "existing_id"
        assert result["result"] == "conflict"
        assert "Version conflict" in result["error"]

    @pytest.mark.asyncio
    async def test_insert_doc_conflict_no_meta(self, adapter, mock_client):
        """Test document insertion with conflict but no meta"""
        class MockConflictError(Exception):
            """Custom mock for ConflictError without meta attribute issues"""
            def __init__(self, message, meta=None, body=None):
                super().__init__(message)
                self.meta = meta
                self.body = body
                self.message = message
            
            def __str__(self):
                return self.message
        
        with patch('utils.vector_db.elastic_sql_adapter.ConflictError', MockConflictError):
            conflict_error = MockConflictError(
                message="Document already exists",
                meta=None,
                body={"error": "version_conflict"}
            )
            mock_client.index.side_effect = conflict_error
            
            document = {"id": "existing_id", "text": "Sample text"}
            result = await adapter.insert_doc(TEST_INDEX_NAME, document)
            
            assert result["id"] == "existing_id"
            assert result["result"] == "conflict"
            assert "Document already exists" in result["error"]

    @pytest.mark.asyncio
    async def test_insert_doc_generic_error(self, adapter, mock_client):
        """Test document insertion with generic error"""
        mock_client.index.side_effect = Exception("Generic error")
        
        document = {"id": "test_id", "text": "Sample text"}
        result = await adapter.insert_doc(TEST_INDEX_NAME, document)
        
        assert result["id"] == "test_id"
        assert result["result"] == "error"
        assert "Generic error" in result["error"]

    @pytest.mark.asyncio
    @pytest.mark.parametrize("doc_count,expected_successful,expected_failed", [
        (2, 2, 0),
        (0, 0, 0),
        (3, 2, 1),
    ])
    async def test_bulk_insert_docs(self, adapter, mock_client, sample_documents, 
                                   doc_count, expected_successful, expected_failed):
        """Test bulk document insertion with different scenarios"""
        documents = sample_documents[:doc_count]
        
        if doc_count == 0:
            result = await adapter.bulk_insert_docs(TEST_INDEX_NAME, documents)
        else:
            with patch('utils.vector_db.elastic_sql_adapter.async_bulk') as mock_bulk:
                errors = ["error1"] if expected_failed > 0 else []
                mock_bulk.return_value = (expected_successful, errors)
                
                result = await adapter.bulk_insert_docs(TEST_INDEX_NAME, documents)
                
                if doc_count > 0:
                    mock_bulk.assert_called_once()
        
        assert result["successful"] == expected_successful
        assert result["failed"] == expected_failed

    @pytest.mark.asyncio
    async def test_knn_similarity_search_success(self, adapter, mock_client, 
                                                mock_embedding_generator, mock_search_request):
        """Test successful KNN similarity search"""
        mock_response = {
            "hits": {
                "hits": [
                    {
                        "_score": 0.95,
                        "_source": {
                            "table_name": "users",
                            "column_name": "name", 
                            "column_description": "User name",
                            "data_type": "varchar"
                        }
                    }
                ]
            }
        }
        mock_client.search.return_value = mock_response
        
        result = await adapter.knn_similarity_search(TEST_INDEX_NAME, mock_search_request)
        
        assert len(result) == 1
        assert result[0]["table_name"] == "users"
        assert result[0]["column_name"] == "name"
        assert result[0]["_score"] == 0.95
        mock_embedding_generator.generate_embedding.assert_called_once_with(TEST_USER_QUERY)

    @pytest.mark.asyncio
    async def test_knn_similarity_search_wrong_dimensions(self, adapter, mock_client, 
                                                        mock_embedding_generator, mock_search_request):
        """Test KNN search with wrong embedding dimensions"""
        wrong_dimensions = 512
        mock_embedding_generator.generate_embedding.return_value = [0.1] * wrong_dimensions
        
        with pytest.raises(CustomException, match="Query vector has incorrect dimensions"):
            await adapter.knn_similarity_search(TEST_INDEX_NAME, mock_search_request)

    @pytest.mark.asyncio
    async def test_knn_similarity_search_exception(self, adapter, mock_client, 
                                                  mock_embedding_generator, mock_search_request):
        """Test KNN search with exception"""
        mock_client.search.side_effect = Exception("Search failed")
        
        with pytest.raises(CustomException, match="Failed to perform KNN search"):
            await adapter.knn_similarity_search(TEST_INDEX_NAME, mock_search_request)

    @pytest.mark.asyncio
    @pytest.mark.parametrize("deleted_count,total_count,failures", [
        (5, 10, []),
        (3, 5, ["failure1", "failure2"]),
        (0, 0, []),
    ])
    async def test_delete_documents_by_kb_id(self, adapter, mock_client, 
                                           deleted_count, total_count, failures):
        """Test deletion of documents by kb_id with different scenarios"""
        mock_response = {
            "deleted": deleted_count,
            "total": total_count,
            "failures": failures
        }
        mock_client.delete_by_query.return_value = mock_response
        
        result = await adapter.delete_documents_by_kb_id(TEST_INDEX_NAME, TEST_KB_ID)
        
        assert result["deleted"] == deleted_count
        assert result["total"] == total_count
        assert result["failed"] == len(failures)
        assert result["failures"] == failures

    @pytest.mark.asyncio
    async def test_delete_documents_by_kb_id_failure(self, adapter, mock_client):
        """Test deletion failure by kb_id"""
        mock_client.delete_by_query.side_effect = Exception("Delete failed")
        
        with pytest.raises(SearchError, match="Failed to delete documents"):
            await adapter.delete_documents_by_kb_id(TEST_INDEX_NAME, TEST_KB_ID)

    @pytest.mark.asyncio
    async def test_get_documents_by_kb_id_success(self, adapter, mock_client):
        """Test successful retrieval of documents by kb_id"""
        mock_response = {
            "hits": {
                "hits": [
                    {
                        "_score": 1.0,
                        "_source": {
                            "table_name": "users",
                            "column_name": "id",
                            "column_description": "User ID",
                            "data_type": "integer"
                        }
                    }
                ]
            }
        }
        mock_client.search.return_value = mock_response
        
        result = await adapter.get_documents_by_kb_id(TEST_INDEX_NAME, TEST_KB_ID)
        
        assert len(result) == 1
        assert result[0]["table_name"] == "users"
        assert result[0]["column_name"] == "id"
        assert result[0]["_score"] == 1.0

    @pytest.mark.asyncio
    async def test_get_documents_by_kb_id_failure(self, adapter, mock_client):
        """Test retrieval failure by kb_id"""
        mock_client.search.side_effect = Exception("Search failed")
        
        with pytest.raises(SearchError, match="Failed to get documents"):
            await adapter.get_documents_by_kb_id(TEST_INDEX_NAME, TEST_KB_ID)

    def test_default_mapping_structure(self, adapter):
        """Test that default mapping has correct structure"""
        mapping = adapter.INDEXING_DEFAULT_MAPPING_FOR_SQL
        
        assert "mappings" in mapping
        assert "properties" in mapping["mappings"]
        assert "table_name" in mapping["mappings"]["properties"]
        assert "column_description_embedding" in mapping["mappings"]["properties"]
        assert "knowledge_base_id" in mapping["mappings"]["properties"]
        
        # Verify embedding dimensions are correct
        embedding_field = mapping["mappings"]["properties"]["column_description_embedding"]
        assert embedding_field["dims"] == EMBEDDING_DIMENSIONS

    @pytest.mark.asyncio
    async def test_knn_similarity_search_candidate_calculation(self, adapter, mock_client, 
                                                             mock_embedding_generator):
        """Test KNN search with large top_answer_count for candidate calculation"""
        large_count = 50
        mock_response = {"hits": {"hits": []}}
        mock_client.search.return_value = mock_response
        
        request = Mock(spec=GetVectorSearchRequest)
        request.query = TEST_USER_QUERY
        request.top_answer_count = large_count
        request.knowledge_base_id = [TEST_KB_ID]
        
        await adapter.knn_similarity_search(TEST_INDEX_NAME, request)
        
        # Verify num_candidates calculation
        expected_candidates = max(large_count * CANDIDATE_MULTIPLIER, MIN_CANDIDATES)
        search_call = mock_client.search.call_args[1]["body"]
        assert search_call["knn"]["num_candidates"] == expected_candidates

    @pytest.mark.asyncio
    async def test_delete_documents_custom_field(self, adapter, mock_client):
        """Test deletion with custom ID field"""
        custom_field = "custom_id"
        mock_response = {"deleted": 3, "total": 3, "failures": []}
        mock_client.delete_by_query.return_value = mock_response
        
        result = await adapter.delete_documents_by_kb_id(TEST_INDEX_NAME, TEST_KB_ID, custom_field)
        
        assert result["deleted"] == 3
        
        # Verify custom field was used
        search_call = mock_client.delete_by_query.call_args[1]["body"]
        assert custom_field in search_call["query"]["term"]

    @pytest.mark.asyncio
    async def test_get_documents_missing_fields(self, adapter, mock_client):
        """Test retrieval with documents missing some fields"""
        mock_response = {
            "hits": {
                "hits": [
                    {
                        "_source": {
                            "table_name": "users",
                            "column_name": "id",
                            # Missing fields should get default values
                        },
                        "_score": 1.0
                    }
                ]
            }
        }
        mock_client.search.return_value = mock_response
        
        result = await adapter.get_documents_by_kb_id(TEST_INDEX_NAME, TEST_KB_ID)
        
        assert len(result) == 1
        assert result[0]["table_name"] == "users"
        assert result[0]["column_name"] == "id"
        # Check default values for missing fields
        assert result[0]["table_description"] == ""
        assert result[0]["column_description"] is None
        assert result[0]["column_data_type"] is None 