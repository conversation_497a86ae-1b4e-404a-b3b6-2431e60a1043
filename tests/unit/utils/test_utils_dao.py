"""
Tests for utils/dao.py module - Base Data Access Object for SQLAlchemy ORM operations.

This module contains test data that are safe for testing purposes.
All database values and mock data in this file are test-only and not actual production data.
"""
import pytest
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from sqlalchemy import inspect, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm.attributes import flag_modified, flag_dirty
from sqlalchemy.exc import IntegrityError

from utils.dao import BaseDao


# Test Constants - These are safe test values, not production data
TEST_NAME = "test"
TEST_AGE = 25
TEST_DICT_NAME = "dict_name"
TEST_DICT_AGE = 30
TEST_KWARG_NAME = "kwarg_name"
TEST_FIELD_NAME = "test_field"
TEST_CONTENT = "test content"
TEST_PRESERVED_CONTENT = "preserved content"
TEST_EXCEPTION_MSG = "Test exception"
TEST_ERROR_MSG = "test error"
TEST_SAME_MSG = "same message"
TEST_MESSAGE = "test message"

# Database Constants
DB_ID = 1
DB_ID_2 = 2
DB_VALUE = "test_value"
DB_UPDATED_VALUE = "updated_value"
PRIMARY_KEY_ID = "id"
PRIMARY_KEY_UUID = "uuid"
PRIMARY_KEY_COMPOSITE = ["id", "company_id"]

# Mock Data
MOCK_MAPPINGS = [
    {"name": TEST_NAME, "age": TEST_AGE},
    {"name": TEST_DICT_NAME, "age": TEST_DICT_AGE}
]

# SQL Update Constants
UPDATE_FIELD = "name"
UPDATE_VALUE = "updated_name"

# Common Test Cases
CREATE_OBJECT_CASES = [
    ({"name": TEST_NAME, "age": TEST_AGE}, None, TEST_NAME),
    (None, {"name": TEST_KWARG_NAME, "age": TEST_AGE}, TEST_KWARG_NAME),
    ({"name": TEST_DICT_NAME, "age": TEST_DICT_AGE}, {"name": TEST_KWARG_NAME, "age": TEST_AGE}, TEST_DICT_NAME),
]

UPDATE_CASES = [
    (DB_ID, {UPDATE_FIELD: UPDATE_VALUE}, None),
    (DB_ID, None, {UPDATE_FIELD: UPDATE_VALUE}),
    (DB_ID, {"name": TEST_DICT_NAME}, {"name": TEST_KWARG_NAME}),
]


class TestBaseDao:
    """Test suite for BaseDao class."""

    @pytest.fixture
    def mock_session(self):
        """Create a mock AsyncSession"""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def mock_model(self):
        """Create a mock model class"""
        return Mock()

    @pytest.fixture
    def mock_instance(self):
        """Create a mock model instance"""
        return Mock()

    @pytest.fixture
    def dao(self, mock_session, mock_model):
        """Create BaseDao instance"""
        return BaseDao(mock_session, mock_model)

    @pytest.fixture
    def configured_dao(self, dao, mock_instance):
        """Create BaseDao with configured model that returns mock_instance"""
        dao.db_model.return_value = mock_instance
        return dao

    def test_base_dao_initialization(self, mock_session, mock_model):
        """Test BaseDao initialization with session and model."""
        # Act
        dao = BaseDao(mock_session, mock_model)

        # Assert
        assert dao.session == mock_session
        assert dao.db_model == mock_model

    async def test_flush(self, dao, mock_session):
        """Test _flush method calls session.flush()."""
        # Act
        await dao._flush()

        # Assert
        mock_session.flush.assert_called_once()

    async def test_commit(self, dao, mock_session):
        """Test _commit method calls session.commit()."""
        # Act
        await dao._commit()

        # Assert
        mock_session.commit.assert_called_once()

    async def test_execute_query(self, dao, mock_session):
        """Test _execute_query method calls session.execute()."""
        # Arrange
        mock_query = Mock()
        mock_result = Mock()
        mock_session.execute.return_value = mock_result

        # Act
        result = await dao._execute_query(mock_query)

        # Assert
        mock_session.execute.assert_called_once_with(mock_query)
        assert result == mock_result

    def test_get_orm_object(self, dao, mock_model, mock_instance):
        """Test get_orm_object creates instance with kwargs."""
        # Arrange
        mock_model.return_value = mock_instance

        # Act
        result = dao.get_orm_object(name=TEST_NAME, age=TEST_AGE)

        # Assert
        mock_model.assert_called_once_with(name=TEST_NAME, age=TEST_AGE)
        assert result == mock_instance

    @pytest.mark.parametrize("create_dict,kwargs,expected_name", CREATE_OBJECT_CASES)
    def test_add_object_variations(self, dao, mock_session, mock_model, mock_instance, 
                                  create_dict, kwargs, expected_name):
        """Test add_object with different parameter combinations."""
        # Arrange
        mock_model.return_value = mock_instance

        # Act
        if create_dict and kwargs:
            result = dao.add_object(create_object_dict=create_dict, **kwargs)
        elif create_dict:
            result = dao.add_object(create_object_dict=create_dict)
        else:
            result = dao.add_object(**kwargs)

        # Assert
        if create_dict:
            mock_model.assert_called_once_with(name=expected_name, age=create_dict.get("age"))
        else:
            mock_model.assert_called_once_with(name=expected_name, age=kwargs.get("age"))
        mock_session.add.assert_called_once_with(mock_instance)
        assert result == mock_instance

    @patch('utils.dao.flag_modified')
    def test_flag_modified_static_method(self, mock_flag_modified):
        """Test flag_modified static method."""
        # Arrange
        mock_obj = Mock()

        # Act
        BaseDao.flag_modified(mock_obj, TEST_FIELD_NAME)

        # Assert
        mock_flag_modified.assert_called_once_with(mock_obj, key=TEST_FIELD_NAME)

    @patch('utils.dao.flag_dirty')
    def test_flag_dirty_static_method(self, mock_flag_dirty):
        """Test flag_dirty static method."""
        # Arrange
        mock_obj = Mock()

        # Act
        BaseDao.flag_dirty(mock_obj)

        # Assert
        mock_flag_dirty.assert_called_once_with(mock_obj)

    @pytest.mark.parametrize("create_dict,kwargs,expected_name", CREATE_OBJECT_CASES)
    async def test_create_variations(self, dao, mock_session, mock_model, mock_instance,
                                   create_dict, kwargs, expected_name):
        """Test create method with different parameter combinations."""
        # Arrange
        mock_model.return_value = mock_instance

        # Act
        if create_dict and kwargs:
            result = await dao.create(create_object_dict=create_dict, **kwargs)
        elif create_dict:
            result = await dao.create(create_object_dict=create_dict)
        else:
            result = await dao.create(**kwargs)

        # Assert
        if create_dict:
            mock_model.assert_called_once_with(name=expected_name, age=create_dict.get("age"))
        else:
            mock_model.assert_called_once_with(name=expected_name, age=kwargs.get("age"))
        mock_session.add.assert_called_once_with(mock_instance)
        mock_session.commit.assert_called_once()
        assert result == mock_instance

    @pytest.mark.parametrize("pk_value,pk_count,pk_names", [
        (DB_ID, 1, [PRIMARY_KEY_ID]),
        ([DB_ID, DB_ID_2], 2, PRIMARY_KEY_COMPOSITE),
    ])
    @patch('utils.dao.inspect')
    @patch('utils.dao.update')
    async def test_update_by_pk_with_different_pk_types(self, mock_update, mock_inspect, dao, mock_session,
                                                       pk_value, pk_count, pk_names):
        """Test update_by_pk with different primary key configurations."""
        # Arrange
        mock_pk_columns = [Mock() for _ in range(pk_count)]
        for i, mock_col in enumerate(mock_pk_columns):
            mock_col.name = pk_names[i]
        
        mock_inspect.return_value.primary_key = mock_pk_columns
        mock_update_query = Mock()
        mock_update.return_value = mock_update_query

        # Act
        await dao.update_by_pk(pk_value, {UPDATE_FIELD: UPDATE_VALUE})

        # Assert
        mock_inspect.assert_called_once_with(dao.db_model)
        mock_update.assert_called_once_with(dao.db_model)
        mock_session.execute.assert_called_once_with(mock_update_query)

    @pytest.mark.parametrize("pk_value,update_dict,kwargs", UPDATE_CASES)
    @patch('utils.dao.inspect')
    @patch('utils.dao.update')
    async def test_update_by_pk_parameter_variations(self, mock_update, mock_inspect, dao, mock_session,
                                                   pk_value, update_dict, kwargs):
        """Test update_by_pk with different parameter combinations."""
        # Arrange
        mock_pk_column = Mock()
        mock_pk_column.name = PRIMARY_KEY_ID
        mock_inspect.return_value.primary_key = [mock_pk_column]
        mock_update_query = Mock()
        mock_update.return_value = mock_update_query

        # Act
        if update_dict and kwargs:
            await dao.update_by_pk(pk_value, update_values_dict=update_dict, **kwargs)
        elif update_dict:
            await dao.update_by_pk(pk_value, update_values_dict=update_dict)
        else:
            await dao.update_by_pk(pk_value, **kwargs)

        # Assert
        mock_session.execute.assert_called_once_with(mock_update_query)

    async def test_get_by_pk_success(self, dao, mock_session):
        """Test get_by_pk returns object when found."""
        # Arrange
        mock_result = Mock()
        mock_session.get.return_value = mock_result

        # Act
        result = await dao.get_by_pk(DB_ID)

        # Assert
        mock_session.get.assert_called_once_with(dao.db_model, DB_ID)
        assert result == mock_result

    async def test_get_by_pk_not_found(self, dao, mock_session):
        """Test get_by_pk returns None when not found."""
        # Arrange
        mock_session.get.return_value = None

        # Act
        result = await dao.get_by_pk(DB_ID)

        # Assert
        mock_session.get.assert_called_once_with(dao.db_model, DB_ID)
        assert result is None

    @patch('utils.dao.bulk_insert_mappings')
    async def test_bulk_insert_success(self, mock_bulk_insert, dao, mock_session):
        """Test bulk_insert with successful insertion."""
        # Arrange
        def mock_run_sync(func):
            func(mock_session)
            return Mock()
        
        mock_session.run_sync = mock_run_sync

        # Act
        await dao.bulk_insert(MOCK_MAPPINGS)

        # Assert
        mock_bulk_insert.assert_called_once()

    @patch('utils.dao.bulk_insert_mappings')
    async def test_bulk_insert_with_integrity_error(self, mock_bulk_insert, dao, mock_session):
        """Test bulk_insert handles IntegrityError."""
        # Arrange
        def mock_run_sync(func):
            func(mock_session)
            raise IntegrityError("statement", "params", "orig")
        
        mock_session.run_sync = mock_run_sync

        # Act & Assert
        with pytest.raises(IntegrityError):
            await dao.bulk_insert(MOCK_MAPPINGS)

    @patch('utils.dao.bulk_insert_mappings')
    async def test_bulk_insert_with_empty_mappings(self, mock_bulk_insert, dao, mock_session):
        """Test bulk_insert with empty mappings."""
        # Arrange
        def mock_run_sync(func):
            func(mock_session)
            return Mock()
        
        mock_session.run_sync = mock_run_sync

        # Act
        await dao.bulk_insert([])

        # Assert
        mock_bulk_insert.assert_called_once()

    @pytest.mark.parametrize("session_type", [AsyncSession, Mock])
    def test_base_dao_with_different_session_types(self, session_type, mock_model):
        """Test BaseDao with different session types."""
        # Arrange
        mock_session = Mock(spec=session_type)

        # Act
        dao = BaseDao(mock_session, mock_model)

        # Assert
        assert dao.session == mock_session
        assert dao.db_model == mock_model

    def test_base_dao_with_none_values(self, mock_session):
        """Test BaseDao with None model."""
        # Act
        dao = BaseDao(mock_session, None)

        # Assert
        assert dao.session == mock_session
        assert dao.db_model is None

    def test_get_orm_object_with_no_kwargs(self, dao, mock_model, mock_instance):
        """Test get_orm_object with no keyword arguments."""
        # Arrange
        mock_model.return_value = mock_instance

        # Act
        result = dao.get_orm_object()

        # Assert
        mock_model.assert_called_once_with()
        assert result == mock_instance

    def test_add_object_with_no_args(self, dao, mock_session, mock_model, mock_instance):
        """Test add_object with no arguments."""
        # Arrange
        mock_model.return_value = mock_instance

        # Act
        result = dao.add_object()

        # Assert
        mock_model.assert_called_once_with()
        mock_session.add.assert_called_once_with(mock_instance)
        assert result == mock_instance

    async def test_create_with_no_args(self, dao, mock_session, mock_model, mock_instance):
        """Test create with no arguments."""
        # Arrange
        mock_model.return_value = mock_instance

        # Act
        result = await dao.create()

        # Assert
        mock_model.assert_called_once_with()
        mock_session.add.assert_called_once_with(mock_instance)
        mock_session.commit.assert_called_once()
        assert result == mock_instance

    def test_dao_methods_are_correctly_defined(self, dao):
        """Test that DAO methods are correctly defined."""
        # Assert
        assert hasattr(dao, '_flush')
        assert hasattr(dao, '_commit')
        assert hasattr(dao, '_execute_query')
        assert hasattr(dao, 'get_orm_object')
        assert hasattr(dao, 'add_object')
        assert hasattr(dao, 'create')
        assert hasattr(dao, 'update_by_pk')
        assert hasattr(dao, 'get_by_pk')
        assert hasattr(dao, 'bulk_insert')
        
        # Check that async methods are coroutines
        assert callable(dao._flush)
        assert callable(dao._commit)
        assert callable(dao._execute_query)
        assert callable(dao.create)
        assert callable(dao.update_by_pk)
        assert callable(dao.get_by_pk)
        assert callable(dao.bulk_insert)

    def test_dao_static_methods_exist(self):
        """Test that static methods exist and are callable."""
        # Assert
        assert hasattr(BaseDao, 'flag_modified')
        assert hasattr(BaseDao, 'flag_dirty')
        assert callable(BaseDao.flag_modified)
        assert callable(BaseDao.flag_dirty)

    @patch('utils.dao.inspect')
    async def test_update_by_pk_with_complex_primary_key(self, mock_inspect, dao, mock_session):
        """Test update_by_pk with complex primary key scenarios."""
        # Arrange
        mock_pk_columns = [Mock(), Mock(), Mock()]
        for i, mock_col in enumerate(mock_pk_columns):
            mock_col.name = f"pk_col_{i}"
        mock_inspect.return_value.primary_key = mock_pk_columns

        with patch('utils.dao.update') as mock_update:
            mock_update_query = Mock()
            mock_update.return_value = mock_update_query

            # Act
            await dao.update_by_pk([DB_ID, DB_ID_2, "pk3"], {UPDATE_FIELD: UPDATE_VALUE})

            # Assert
            mock_inspect.assert_called_once_with(dao.db_model)
            mock_update.assert_called_once_with(dao.db_model)
            mock_session.execute.assert_called_once_with(mock_update_query)

    async def test_dao_can_handle_async_context_manager(self, dao, mock_session):
        """Test that DAO can work with async context managers."""
        # Arrange
        mock_session.__aenter__ = AsyncMock(return_value=mock_session)
        mock_session.__aexit__ = AsyncMock(return_value=None)

        # Act & Assert
        async with mock_session:
            await dao._flush()
            await dao._commit()

        mock_session.__aenter__.assert_called_once()
        mock_session.__aexit__.assert_called_once()

    def test_dao_instance_attributes(self, dao, mock_session, mock_model):
        """Test that DAO instance has correct attributes."""
        # Assert
        assert dao.session is mock_session
        assert dao.db_model is mock_model
        assert hasattr(dao, 'session')
        assert hasattr(dao, 'db_model')

    async def test_all_async_methods_are_awaitable(self, dao, mock_session):
        """Test that all async methods are properly awaitable."""
        # Arrange
        mock_session.get.return_value = Mock()

        # Act & Assert - These should not raise TypeError
        await dao._flush()
        await dao._commit()
        await dao._execute_query(Mock())
        await dao.create()
        await dao.get_by_pk(DB_ID)
        await dao.bulk_insert([])

    def test_dao_with_custom_model_class(self, mock_session):
        """Test DAO with custom model class."""
        # Arrange
        class CustomModel:
            def __init__(self, **kwargs):
                self.data = kwargs

        # Act
        dao = BaseDao(mock_session, CustomModel)

        # Assert
        assert dao.db_model == CustomModel
        
        # Test that it can create instances
        instance = dao.get_orm_object(name=TEST_NAME, age=TEST_AGE)
        assert isinstance(instance, CustomModel)
        assert instance.data == {"name": TEST_NAME, "age": TEST_AGE}

    @pytest.mark.parametrize("method_name,args", [
        ("_flush", []),
        ("_commit", []),
        ("_execute_query", [Mock()]),
        ("create", []),
        ("get_by_pk", [DB_ID]),
        ("bulk_insert", [[]]),
    ])
    async def test_async_methods_with_different_args(self, dao, method_name, args):
        """Test async methods with different argument patterns."""
        # Arrange
        method = getattr(dao, method_name)
        
        # Act & Assert - Should not raise errors
        try:
            await method(*args)
        except Exception as e:
            # Some methods might raise due to mocking, but they should be awaitable
            assert not isinstance(e, TypeError)

    def test_dao_immutability_of_core_attributes(self, dao, mock_session, mock_model):
        """Test that core attributes maintain their references."""
        # Arrange
        original_session = dao.session
        original_model = dao.db_model

        # Act - Try to modify (this should be the same references)
        dao.session = mock_session
        dao.db_model = mock_model

        # Assert
        assert dao.session is mock_session
        assert dao.db_model is mock_model

    def test_dao_string_representation(self, dao, mock_model):
        """Test DAO string representation."""
        # Arrange
        mock_model.__name__ = "TestModel"

        # Act
        dao_str = str(dao)

        # Assert
        assert dao_str is not None
        assert isinstance(dao_str, str) 