"""
Comprehensive tests for utils/custom_middleware.py module.

This module contains test data that are safe for testing purposes.
All URLs and values in this file are test-only and not actual production data.
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response as StarletteResponse

from utils.custom_middleware import SecurityHeadersMiddleware


# Test Constants - These are safe test values, not production data
TEST_PATH = "/test"
TEST_API_PATH = "/api/test"
TEST_STREAM_PATH = "/stream"
TEST_OLD_PATH = "/old-path"
TEST_NEW_PATH = "/new-path"
TEST_CONTENT = "test content"
TEST_JSON_CONTENT = {"message": "success"}
CUSTOM_HEADER_VALUE = "custom-value"

# HTTP Status Codes
STATUS_OK = 200
STATUS_CREATED = 201
STATUS_FOUND = 302
STATUS_NOT_FOUND = 404

# HTTP Methods
HTTP_GET = "GET"
HTTP_POST = "POST"
HTTP_OPTIONS = "OPTIONS"

# Response Types
CONTENT_TYPE_JSON = "application/json"
CONTENT_TYPE_TEXT = "text/plain"
MEDIA_TYPE_PLAIN = "text/plain"

# Security Headers Constants - Updated to match actual implementation
HEADER_CONTENT_TYPE_OPTIONS = "X-Content-Type-Options"
HEADER_TRANSPORT_SECURITY = "Strict-Transport-Security"
HEADER_CONTENT_SECURITY = "Content-Security-Policy"
HEADER_REFERRER_POLICY = "Referrer-Policy"
HEADER_PERMISSIONS_POLICY = "Permissions-Policy"
HEADER_CONTENT_TYPE = "Content-Type"
HEADER_CUSTOM = "Custom-Header"

# Security Header Values - Updated to match actual implementation
VALUE_NOSNIFF = "nosniff"
VALUE_HSTS = "max-age=31536000; includeSubDomains"
VALUE_CSP = "default-src 'self'; script-src 'self'"
VALUE_REFERRER = "no-referrer"
VALUE_PERMISSIONS = "geolocation=(), microphone=()"

# Test Messages
NOT_FOUND_MSG = "Not Found"
SUCCESS_MSG = "success"


class TestSecurityHeadersMiddleware:
    """Test cases for SecurityHeadersMiddleware class."""

    @pytest.fixture
    def mock_app(self):
        """Create a mock application"""
        return Mock()

    @pytest.fixture
    def middleware(self, mock_app):
        """Create SecurityHeadersMiddleware instance"""
        return SecurityHeadersMiddleware(mock_app)

    @pytest.fixture
    def mock_request(self):
        """Create a mock request"""
        request = Mock(spec=Request)
        request.method = HTTP_GET
        request.url = Mock()
        request.url.path = TEST_PATH
        return request

    @pytest.fixture
    def expected_security_headers(self):
        """Expected security headers for testing - Updated to match actual implementation"""
        return {
            HEADER_CONTENT_TYPE_OPTIONS: VALUE_NOSNIFF,
            HEADER_TRANSPORT_SECURITY: VALUE_HSTS,
            HEADER_CONTENT_SECURITY: VALUE_CSP,
            HEADER_REFERRER_POLICY: VALUE_REFERRER,
            HEADER_PERMISSIONS_POLICY: VALUE_PERMISSIONS
        }

    def test_security_headers_middleware_initialization(self, mock_app):
        """Test SecurityHeadersMiddleware initialization."""
        # Act
        middleware = SecurityHeadersMiddleware(mock_app)

        # Assert
        assert middleware.app == mock_app
        assert isinstance(middleware, BaseHTTPMiddleware)

    @pytest.mark.asyncio
    async def test_dispatch_adds_security_headers(self, middleware, mock_request, expected_security_headers):
        """Test dispatch method adds security headers to response."""
        # Arrange
        async def mock_call_next(request):
            return StarletteResponse(content=TEST_CONTENT, status_code=STATUS_OK)
        
        # Act
        response = await middleware.dispatch(mock_request, mock_call_next)
        
        # Assert
        assert isinstance(response, StarletteResponse)
        assert response.status_code == STATUS_OK
        
        # Check that security headers are added
        for header_name, header_value in expected_security_headers.items():
            assert header_name in response.headers
            assert response.headers[header_name] == header_value

    @pytest.mark.asyncio
    async def test_dispatch_preserves_existing_headers(self, middleware, mock_request):
        """Test dispatch method preserves existing response headers."""
        # Arrange
        async def mock_call_next(request):
            response = StarletteResponse(content=TEST_CONTENT, status_code=STATUS_OK)
            response.headers[HEADER_CUSTOM] = CUSTOM_HEADER_VALUE
            response.headers[HEADER_CONTENT_TYPE] = CONTENT_TYPE_JSON
            return response
        
        # Act
        response = await middleware.dispatch(mock_request, mock_call_next)
        
        # Assert
        assert response.headers[HEADER_CUSTOM] == CUSTOM_HEADER_VALUE
        assert response.headers[HEADER_CONTENT_TYPE] == CONTENT_TYPE_JSON
        
        # Security headers should also be present
        assert HEADER_CONTENT_TYPE_OPTIONS in response.headers
        assert HEADER_TRANSPORT_SECURITY in response.headers

    @pytest.mark.asyncio
    @pytest.mark.parametrize("request_method,request_path,status_code,expected_content", [
        (HTTP_POST, TEST_API_PATH, STATUS_CREATED, TEST_JSON_CONTENT),
        (HTTP_GET, TEST_PATH, STATUS_OK, TEST_CONTENT),
        (HTTP_OPTIONS, TEST_PATH, STATUS_OK, TEST_CONTENT),
    ])
    async def test_dispatch_with_different_request_types(self, middleware, request_method, 
                                                        request_path, status_code, expected_content):
        """Test dispatch with different request types."""
        # Arrange
        mock_request = Mock(spec=Request)
        mock_request.method = request_method
        mock_request.url = Mock()
        mock_request.url.path = request_path
        
        from fastapi.responses import JSONResponse
        
        async def mock_call_next(request):
            if isinstance(expected_content, dict):
                return JSONResponse(content=expected_content, status_code=status_code)
            return StarletteResponse(content=expected_content, status_code=status_code)
        
        # Act
        response = await middleware.dispatch(mock_request, mock_call_next)
        
        # Assert
        assert response.status_code == status_code
        assert HEADER_CONTENT_TYPE_OPTIONS in response.headers
        assert HEADER_TRANSPORT_SECURITY in response.headers

    @pytest.mark.asyncio
    async def test_dispatch_with_error_response(self, middleware, mock_request):
        """Test dispatch with error response."""
        # Arrange
        async def mock_call_next(request):
            return StarletteResponse(content=NOT_FOUND_MSG, status_code=STATUS_NOT_FOUND)
        
        # Act
        response = await middleware.dispatch(mock_request, mock_call_next)
        
        # Assert
        assert response.status_code == STATUS_NOT_FOUND
        
        # Security headers should still be added even for error responses
        assert HEADER_CONTENT_TYPE_OPTIONS in response.headers
        assert response.headers[HEADER_CONTENT_TYPE_OPTIONS] == VALUE_NOSNIFF

    @pytest.mark.asyncio
    async def test_dispatch_with_streaming_response(self, middleware, mock_request):
        """Test dispatch with streaming response."""
        # Arrange
        from fastapi.responses import StreamingResponse
        
        async def generate_data():
            yield b"chunk1"
            yield b"chunk2"
        
        async def mock_call_next(request):
            return StreamingResponse(generate_data(), media_type=MEDIA_TYPE_PLAIN)
        
        # Act
        response = await middleware.dispatch(mock_request, mock_call_next)
        
        # Assert
        assert isinstance(response, StreamingResponse)
        
        # Security headers should be added to streaming responses too
        assert HEADER_CONTENT_TYPE_OPTIONS in response.headers
        assert HEADER_TRANSPORT_SECURITY in response.headers

    @pytest.mark.asyncio
    async def test_dispatch_with_redirect_response(self, middleware, mock_request):
        """Test dispatch with redirect response."""
        # Arrange
        from fastapi.responses import RedirectResponse
        
        async def mock_call_next(request):
            return RedirectResponse(url=TEST_NEW_PATH, status_code=STATUS_FOUND)
        
        # Act
        response = await middleware.dispatch(mock_request, mock_call_next)
        
        # Assert
        assert isinstance(response, RedirectResponse)
        assert response.status_code == STATUS_FOUND
        
        # Security headers should be added to redirect responses
        assert HEADER_CONTENT_TYPE_OPTIONS in response.headers

    @pytest.mark.asyncio
    async def test_dispatch_security_headers_values(self, middleware, mock_request, expected_security_headers):
        """Test that security headers have correct values."""
        # Arrange
        async def mock_call_next(request):
            return StarletteResponse(content=TEST_CONTENT, status_code=STATUS_OK)
        
        # Act
        response = await middleware.dispatch(mock_request, mock_call_next)
        
        # Assert
        for header_name, expected_value in expected_security_headers.items():
            assert response.headers[header_name] == expected_value

    @pytest.mark.asyncio
    async def test_dispatch_preserves_response_content(self, middleware, mock_request):
        """Test dispatch preserves response content."""
        # Arrange
        test_content = "preserved content"
        
        async def mock_call_next(request):
            return StarletteResponse(content=test_content, status_code=STATUS_OK)
        
        # Act
        response = await middleware.dispatch(mock_request, mock_call_next)
        
        # Assert
        assert response.body.decode() == test_content

    @pytest.mark.asyncio
    async def test_dispatch_with_exception_in_call_next(self, middleware, mock_request):
        """Test dispatch when call_next raises an exception."""
        # Arrange
        async def mock_call_next_with_exception(request):
            raise ValueError("Test exception")
        
        # Act & Assert
        with pytest.raises(ValueError, match="Test exception"):
            await middleware.dispatch(mock_request, mock_call_next_with_exception)

    @pytest.mark.asyncio
    async def test_dispatch_with_multiple_requests(self, middleware):
        """Test dispatch with multiple requests to ensure no state pollution."""
        # Arrange
        request_data = [
            (HTTP_GET, TEST_PATH, STATUS_OK),
            (HTTP_POST, TEST_API_PATH, STATUS_CREATED),
            (HTTP_OPTIONS, TEST_STREAM_PATH, STATUS_OK),
        ]
        
        async def mock_call_next(request):
            return StarletteResponse(content=TEST_CONTENT, status_code=STATUS_OK)
        
        # Act & Assert
        for method, path, expected_status in request_data:
            mock_request = Mock(spec=Request)
            mock_request.method = method
            mock_request.url = Mock()
            mock_request.url.path = path
            
            response = await middleware.dispatch(mock_request, mock_call_next)
            
            assert HEADER_CONTENT_TYPE_OPTIONS in response.headers
            assert response.headers[HEADER_CONTENT_TYPE_OPTIONS] == VALUE_NOSNIFF

    @pytest.mark.asyncio
    async def test_dispatch_does_not_override_existing_security_headers(self, middleware, mock_request):
        """Test dispatch behavior with existing security headers (actual implementation always sets headers)."""
        # Arrange
        custom_csp_value = "default-src 'none'"
        
        async def mock_call_next(request):
            response = StarletteResponse(content=TEST_CONTENT, status_code=STATUS_OK)
            response.headers[HEADER_CONTENT_SECURITY] = custom_csp_value
            return response
        
        # Act
        response = await middleware.dispatch(mock_request, mock_call_next)
        
        # Assert
        # The middleware always sets security headers, so it will override existing ones
        assert response.headers[HEADER_CONTENT_SECURITY] == VALUE_CSP  # Middleware value, not custom
        # Other security headers should still be added
        assert HEADER_CONTENT_TYPE_OPTIONS in response.headers
        assert response.headers[HEADER_CONTENT_TYPE_OPTIONS] == VALUE_NOSNIFF

    def test_middleware_can_be_instantiated_multiple_times(self, mock_app):
        """Test that middleware can be instantiated multiple times."""
        # Act
        middleware1 = SecurityHeadersMiddleware(mock_app)
        middleware2 = SecurityHeadersMiddleware(mock_app)
        
        # Assert
        assert middleware1 is not middleware2
        assert middleware1.app == mock_app
        assert middleware2.app == mock_app 