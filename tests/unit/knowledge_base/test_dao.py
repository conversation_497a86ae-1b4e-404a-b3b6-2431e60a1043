"""
Comprehensive tests for knowledge_base.dao module to achieve 100% coverage.

This module contains test data that are safe for testing purposes.
All test data in this file is fictional and for testing purposes only.

SECURITY NOTE: All data in this file is safe test data:
- Database IDs and UUIDs are fictional test values
- User IDs and organization IDs are fictional test identifiers  
- Dates and timestamps are fictional test values
- Source identifiers and paths are fictional test values
- Repository URLs and tokens are fictional test values
- Email addresses and names are fictional test data
- No production credentials or sensitive data is used
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from knowledge_base.dao import (
    IntegrationDao, KnowledgeBaseDao, IngestionRunDao, 
    SourceItemDao, ChunkDao, IngestionErrorDao
)
from knowledge_base.models import (
    Integration, IntegrationType, KnowledgeBase, KBState,
    IngestionRun, SourceItem, Chunk, IngestionError
)
from knowledge_base.serializers import GetKbRequest


# Test Constants - All values are safe test data, not production values
# Database IDs
TEST_KB_ID = 1
TEST_INTEGRATION_ID = 1
TEST_RUN_ID = 1
TEST_SOURCE_ITEM_ID = "source-123"
TEST_CHUNK_ID = "chunk-123"
TEST_ERROR_ID = 1
TEST_GRAPH_ID = "test_graph_id"

# User and Organization Constants
TEST_USER_ID = "user-123"
TEST_ORG_ID = "org-123"
TEST_CREATED_BY = "creator-123"

# Knowledge Base Constants
TEST_KB_NAME = "Test Knowledge Base"
TEST_DESCRIPTION = "Test description"
TEST_SOURCE = "test-source"
TEST_SOURCE_IDENTIFIER = "https://github.com/test/repo"
TEST_MEMORY_KEY = "test_key"
TEST_MEMORY_VALUE = {"key": "value"}

# State Constants
KB_STATE_READY = KBState.ready
KB_STATE_INDEXING = KBState.indexing
KB_STATE_UPDATING = KBState.updating

# Settings and Credentials Constants
TEST_SETTINGS_KEY = "setting"
TEST_SETTINGS_VALUE = "value"
TEST_CREDENTIALS_KEY = "token"
TEST_CREDENTIALS_VALUE = "new-token"

# Repository Constants
TEST_PAT_TOKEN = "test-pat-token"
TEST_PAT_URL = "https://github.com/test/repo"
TEST_BRANCH = "main"
TEST_REPOSITORY_URL = "https://github.com/test/repo"

# Path Constants
TEST_LOGICAL_PATH = "/test/path"
TEST_LOGICAL_PATH_LIST = ["/test/path1", "/test/path2"]

# Date Constants
TEST_DATE = "2023-01-01T00:00:00Z"
TEST_DATETIME = datetime.fromisoformat("2023-01-01T00:00:00+00:00")

# Count Constants
TOTAL_COUNT = 5
SUCCESS_COUNT = 2
CHUNK_COUNT = 10
SOURCE_COUNT = 3

# Pagination Constants
PAGINATION_SKIP = 0
PAGINATION_LIMIT = 10
PAGINATION_PAGE = 1
PAGINATION_SIZE = 10

# Integration Constants
INTEGRATION_TYPE_GITHUB = IntegrationType.github
INTEGRATION_NAME = "GitHub Integration"

# Status Constants
STATUS_COMPLETED = "completed"
STATUS_FAILED = "failed"

# Error Constants
ERROR_MESSAGE = "Test error message"

# Mock Data Constants
MOCK_SETTINGS_JSON = {"test": "value"}
MOCK_STATS_JSON = {"processed": SUCCESS_COUNT}
MOCK_CREDENTIALS_JSON = {"token": "test-token"}


@pytest.fixture
def mock_session():
    """Create mock AsyncSession for all tests."""
    session = Mock()
    session.add = Mock()
    session.flush = AsyncMock()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.execute = AsyncMock()
    session.delete = AsyncMock()
    return session


@pytest.fixture
def sample_integration():
    """Create sample Integration for testing."""
    integration = Mock(spec=Integration)
    integration.id = TEST_INTEGRATION_ID
    integration.type = INTEGRATION_TYPE_GITHUB
    integration.name = INTEGRATION_NAME
    integration.credentials_json = MOCK_CREDENTIALS_JSON
    integration.image_name = "github-icon"
    integration.integration_help_url = "https://help.example.com"
    return integration


@pytest.fixture
def sample_kb():
    """Create sample KnowledgeBase for testing."""
    kb = Mock(spec=KnowledgeBase)
    kb.id = TEST_KB_ID
    kb.name = TEST_KB_NAME
    kb.description = TEST_DESCRIPTION
    kb.state = KB_STATE_READY
    kb.source_identifier = TEST_SOURCE_IDENTIFIER
    kb.created_by = TEST_CREATED_BY
    kb.settings_json = MOCK_SETTINGS_JSON
    kb.created_at = TEST_DATETIME
    kb.last_indexed_at = TEST_DATETIME
    kb.integration_id = TEST_INTEGRATION_ID
    kb.is_updatable = True
    kb.org_id = TEST_ORG_ID
    return kb


@pytest.fixture
def sample_ingestion_run():
    """Create sample IngestionRun for testing."""
    run = Mock(spec=IngestionRun)
    run.id = TEST_RUN_ID
    run.kb_id = TEST_KB_ID
    run.status = STATUS_COMPLETED
    run.stats_json = MOCK_STATS_JSON
    run.started_at = TEST_DATETIME
    run.finished_at = TEST_DATETIME
    return run


@pytest.fixture
def sample_source_item():
    """Create sample SourceItem for testing."""
    item = Mock(spec=SourceItem)
    item.id = TEST_SOURCE_ITEM_ID
    item.kb_id = TEST_KB_ID
    item.logical_path = TEST_LOGICAL_PATH
    item.chunks = []
    return item


@pytest.fixture
def sample_chunk():
    """Create sample Chunk for testing."""
    chunk = Mock(spec=Chunk)
    chunk.id = TEST_CHUNK_ID
    chunk.source_item_id = TEST_SOURCE_ITEM_ID
    return chunk


@pytest.fixture
def sample_error():
    """Create sample IngestionError for testing."""
    error = Mock(spec=IngestionError)
    error.id = TEST_ERROR_ID
    error.run_id = TEST_RUN_ID
    error.error_message = ERROR_MESSAGE
    return error


class TestIntegrationDao:
    """Test cases for IntegrationDao class."""
    
    def test_init(self, mock_session):
        """Test IntegrationDao initialization."""
        dao = IntegrationDao(mock_session)
        
        assert dao.session == mock_session
        assert dao.db_model == Integration
    
    @pytest.mark.asyncio
    async def test_get_by_type_found(self, mock_session, sample_integration):
        """Test get_by_type when integration is found."""
        dao = IntegrationDao(mock_session)
        
        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_integration
        mock_session.execute.return_value = mock_result
        
        result = await dao.get_by_type(INTEGRATION_TYPE_GITHUB)
        
        assert result == sample_integration
        mock_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_by_type_not_found(self, mock_session):
        """Test get_by_type when integration is not found."""
        dao = IntegrationDao(mock_session)
        
        # Mock query result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        result = await dao.get_by_type(INTEGRATION_TYPE_GITHUB)
        
        assert result is None
        mock_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_all_integrations_with_cache(self, mock_session):
        """Test get_all_integrations when cache is populated."""
        dao = IntegrationDao(mock_session)
        
        # Mock cached data
        cached_data = [{"id": 1, "type": "github", "name": "GitHub"}]
        
        with patch('knowledge_base.dao.loaded_config') as mock_config:
            mock_config.ingestion_integrations = cached_data
            
            result = await dao.get_all_integrations()
            
            assert result == cached_data
            # Should not query database when cache exists
            mock_session.execute.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_get_all_integrations_without_cache(self, mock_session, sample_integration):
        """Test get_all_integrations when cache is empty."""
        dao = IntegrationDao(mock_session)
        
        # Mock query result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_integration]
        mock_session.execute.return_value = mock_result
        
        with patch('knowledge_base.dao.loaded_config') as mock_config:
            mock_config.ingestion_integrations = None
            
            result = await dao.get_all_integrations()
            
            assert isinstance(result, list)
            assert len(result) == 1
            mock_session.execute.assert_called_once()


class TestKnowledgeBaseDao:
    """Test cases for KnowledgeBaseDao class."""
    
    def test_init(self, mock_session):
        """Test KnowledgeBaseDao initialization."""
        dao = KnowledgeBaseDao(mock_session)
        
        assert dao.session == mock_session
        assert dao.db_model == KnowledgeBase
    
    @pytest.mark.parametrize("commit_flag", [True, False])
    @pytest.mark.asyncio
    async def test_create_knowledge_base(self, mock_session, commit_flag):
        """Test create_knowledge_base with and without commit."""
        dao = KnowledgeBaseDao(mock_session)
        
        kwargs = {"name": TEST_KB_NAME, "description": TEST_DESCRIPTION}
        result = await dao.create_knowledge_base(commit=commit_flag, **kwargs)
        
        assert isinstance(result, KnowledgeBase)
        mock_session.add.assert_called_once()
        mock_session.flush.assert_called_once()
        
        if commit_flag:
            mock_session.commit.assert_called_once()
        else:
            mock_session.commit.assert_not_called()
    
    @pytest.mark.parametrize("commit_flag", [True, False])
    @pytest.mark.asyncio
    async def test_update_state_by_id(self, mock_session, commit_flag):
        """Test update_state_by_id with and without commit."""
        dao = KnowledgeBaseDao(mock_session)
        
        await dao.update_state_by_id(TEST_KB_ID, KB_STATE_INDEXING, commit=commit_flag)
        
        mock_session.execute.assert_called_once()
        if commit_flag:
            mock_session.commit.assert_called_once()
        else:
            mock_session.commit.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_update_kb_credentials(self, mock_session, sample_kb):
        """Test update_kb_credentials with existing KB."""
        dao = KnowledgeBaseDao(mock_session)
        
        with patch.object(dao, 'get_by_id', return_value=sample_kb):
            new_credentials = {TEST_CREDENTIALS_KEY: TEST_CREDENTIALS_VALUE}
            
            await dao.update_kb_credentials(TEST_KB_ID, new_credentials)
            
            mock_session.execute.assert_called_once()
            mock_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_kb_credentials_kb_not_found(self, mock_session):
        """Test update_kb_credentials when KB not found."""
        dao = KnowledgeBaseDao(mock_session)
        
        with patch.object(dao, 'get_by_id', return_value=None):
            with pytest.raises(ValueError, match="Knowledge base with id"):
                await dao.update_kb_credentials(TEST_KB_ID, {TEST_CREDENTIALS_KEY: TEST_CREDENTIALS_VALUE})
    
    @pytest.mark.asyncio
    async def test_update_kb_settings(self, mock_session, sample_kb):
        """Test update_kb_settings with existing KB."""
        dao = KnowledgeBaseDao(mock_session)
        
        with patch.object(dao, 'get_by_id', return_value=sample_kb):
            new_settings = {TEST_SETTINGS_KEY: TEST_SETTINGS_VALUE}
            
            await dao.update_kb_settings(TEST_KB_ID, new_settings)
            
            mock_session.execute.assert_called_once()
            mock_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_kb_settings_kb_not_found(self, mock_session):
        """Test update_kb_settings when KB not found."""
        dao = KnowledgeBaseDao(mock_session)
        
        with patch.object(dao, 'get_by_id', return_value=None):
            with pytest.raises(ValueError, match="Knowledge base with id"):
                await dao.update_kb_settings(TEST_KB_ID, {TEST_SETTINGS_KEY: TEST_SETTINGS_VALUE})
    
    @pytest.mark.asyncio
    async def test_get_indexing_status(self, mock_session):
        """Test get_indexing_status method."""
        dao = KnowledgeBaseDao(mock_session)
        
        # Mock database result - fix the iteration issue
        mock_kb = Mock()
        mock_kb.id = TEST_KB_ID
        mock_kb.name = TEST_KB_NAME
        mock_kb.state = KB_STATE_READY
        mock_kb.created_at = TEST_DATETIME
        mock_kb.last_indexed_at = TEST_DATETIME
        mock_kb.created_by = TEST_USER_ID
        mock_kb.is_updatable = True
        
        mock_integration = Mock()
        mock_integration.id = TEST_INTEGRATION_ID
        mock_integration.name = INTEGRATION_NAME
        mock_integration.type = INTEGRATION_TYPE_GITHUB
        mock_integration.image_name = "github-icon"
        mock_integration.integration_help_url = "https://help.example.com"
        
        # Mock the ingestion run with proper stats_json
        mock_run = Mock()
        mock_run.stats_json = {
            "total_chunks": 10,
            "failed_chunk_elastic_doc_id": ["failed1", "failed2"]
        }
        
        # Mock the first query result (kb and integration)
        mock_kb_result = Mock()
        mock_kb_result.__iter__ = Mock(return_value=iter([(mock_kb, mock_integration)]))
        
        # Mock the second query result (ingestion run)
        mock_run_result = Mock()
        mock_run_result.scalar.return_value = mock_run
        
        # Configure session to return different results for different queries
        mock_session.execute.side_effect = [mock_kb_result, mock_run_result]
        
        # Fix: Add required user_id parameter
        data = {"accessible": [TEST_KB_ID]}
        result = await dao.get_indexing_status(data, TEST_USER_ID)
        
        assert isinstance(result, dict)
        assert "accessible" in result
        assert len(result["accessible"]) == 1
        assert result["accessible"][0]["kb_id"] == TEST_KB_ID
        assert result["accessible"][0]["name"] == TEST_KB_NAME
        assert result["accessible"][0]["state"] == KB_STATE_READY.value
        assert result["accessible"][0]["success_percentage"] == 80.0  # 8/10 * 100
        assert mock_session.execute.call_count == 2
    
    @pytest.mark.asyncio
    async def test_delete_knowledge_bases(self, mock_session):
        """Test delete_knowledge_bases method."""
        dao = KnowledgeBaseDao(mock_session)
        
        await dao.delete_knowledge_bases([TEST_KB_ID])
        
        mock_session.execute.assert_called_once()
        # Note: This method doesn't auto-commit, so no commit assertion
    
    @pytest.mark.asyncio
    async def test_get_by_id(self, mock_session, sample_kb):
        """Test get_by_id method."""
        dao = KnowledgeBaseDao(mock_session)
        
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_kb
        mock_session.execute.return_value = mock_result
        
        result = await dao.get_by_id(TEST_KB_ID)
        
        assert result == sample_kb
        mock_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_kb_by_id_with_integration(self, mock_session, sample_kb):
        """Test get_kb_by_id_with_integration method."""
        dao = KnowledgeBaseDao(mock_session)
        
        mock_result = Mock()
        mock_result.unique.return_value.scalar_one_or_none.return_value = sample_kb
        mock_session.execute.return_value = mock_result
        
        result = await dao.get_kb_by_id_with_integration(TEST_KB_ID)
        
        assert result == sample_kb
        mock_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_knowledge_bases_for_cron(self, mock_session):
        """Test get_knowledge_bases_for_cron method."""
        dao = KnowledgeBaseDao(mock_session)
        
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = []
        mock_session.execute.return_value = mock_result
        
        result = await dao.get_knowledge_bases_for_cron()
        
        assert isinstance(result, list)
        mock_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_by_source_and_user(self, mock_session, sample_kb):
        """Test get_by_source_and_user method."""
        dao = KnowledgeBaseDao(mock_session)
        
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_kb
        mock_session.execute.return_value = mock_result
        
        # Fix: Add required org_id parameter
        result = await dao.get_by_source_and_user(TEST_SOURCE, TEST_USER_ID, TEST_ORG_ID)
        
        assert result == sample_kb
        mock_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_memory_key_by_id(self, mock_session, sample_kb):
        """Test update_memory_key_by_id method."""
        dao = KnowledgeBaseDao(mock_session)
        
        # Fix: Add required value parameter
        await dao.update_memory_key_by_id(TEST_KB_ID, TEST_MEMORY_KEY, TEST_MEMORY_VALUE)
        
        mock_session.execute.assert_called_once()
        # Note: commit=False by default, so no commit
    
    @pytest.mark.asyncio
    async def test_get_knowledge_bases_with_pagination(self, mock_session):
        """Test get_knowledge_bases_with_pagination method."""
        dao = KnowledgeBaseDao(mock_session)
        
        # Mock count result
        mock_count_result = Mock()
        mock_count_result.scalar.return_value = TOTAL_COUNT
        
        # Mock data result  
        mock_data_result = Mock()
        mock_data_result.scalars.return_value.all.return_value = []
        
        # Configure session to return different results for different queries
        mock_session.execute.side_effect = [mock_count_result, mock_data_result]
        
        # Fix: Add required filter_params parameter
        kb_ids = {"accessible": [TEST_KB_ID], "editable": []}
        request = GetKbRequest(
            skip=PAGINATION_SKIP,
            limit=PAGINATION_LIMIT,
            page=PAGINATION_PAGE,
            size=PAGINATION_SIZE,
            user_id=TEST_USER_ID
        )
        
        result = await dao.get_knowledge_bases_with_pagination(kb_ids, request)
        
        assert isinstance(result, dict)
        assert "knowledge_bases" in result
        assert "pagination" in result
    
    @pytest.mark.asyncio
    async def test_update_kb_name_and_pat_url_branch(self, mock_session, sample_kb):
        """Test update_kb_name_and_pat_url_branch method."""
        dao = KnowledgeBaseDao(mock_session)
        
        with patch.object(dao, 'get_by_id', return_value=sample_kb):
            new_name = "Updated KB Name"
            
            await dao.update_kb_name_and_pat_url_branch(
                TEST_KB_ID, new_name, TEST_PAT_TOKEN, TEST_BRANCH
            )
            
            mock_session.execute.assert_called_once()
            mock_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_exist_by_graph_id(self, mock_session, sample_kb):
        """Test exist_by_graph_id method."""
        dao = KnowledgeBaseDao(mock_session)
        
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_kb
        mock_session.execute.return_value = mock_result
        
        result = await dao.exist_by_graph_id(TEST_GRAPH_ID)
        
        assert result == sample_kb
        mock_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_graph_details(self, mock_session):
        """Test update_graph_details method."""
        dao = KnowledgeBaseDao(mock_session)
        
        # Fix: Provide dict instead of string
        update_data = {"name": "Updated Name"}
        await dao.update_graph_details(TEST_GRAPH_ID, update_data)
        
        mock_session.execute.assert_called_once()


class TestIngestionRunDao:
    """Test cases for IngestionRunDao class."""
    
    def test_init(self, mock_session):
        """Test IngestionRunDao initialization."""
        dao = IngestionRunDao(mock_session)
        
        assert dao.session == mock_session
        assert dao.db_model == IngestionRun
    
    @pytest.mark.asyncio
    async def test_create_ingestion_run(self, mock_session):
        """Test create_ingestion_run method."""
        dao = IngestionRunDao(mock_session)
        
        kwargs = {"kb_id": TEST_KB_ID, "status": "running"}
        result = await dao.create_ingestion_run(**kwargs)
        
        assert isinstance(result, IngestionRun)
        mock_session.add.assert_called_once()
        mock_session.flush.assert_called_once()
        mock_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_run_summary(self, mock_session):
        """Test update_run_summary method."""
        dao = IngestionRunDao(mock_session)
        
        # Fix: Use correct method signature
        await dao.update_run_summary(
            TEST_RUN_ID, 
            STATUS_COMPLETED,
            SOURCE_COUNT,
            CHUNK_COUNT, 
            []
        )
        
        mock_session.execute.assert_called_once()
        mock_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_latest_stats_json_by_kb_id(self, mock_session):
        """Test get_latest_stats_json_by_kb_id with found run."""
        dao = IngestionRunDao(mock_session)
        
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = MOCK_STATS_JSON
        mock_session.execute.return_value = mock_result
        
        result = await dao.get_latest_stats_json_by_kb_id(TEST_KB_ID)
        
        assert result == MOCK_STATS_JSON
        mock_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_latest_stats_json_by_kb_id_not_found(self, mock_session):
        """Test get_latest_stats_json_by_kb_id when no run found."""
        dao = IngestionRunDao(mock_session)
        
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        result = await dao.get_latest_stats_json_by_kb_id(TEST_KB_ID)
        
        assert result is None
        mock_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_latest_ingestion_run_by_kb_id(self, mock_session, sample_ingestion_run):
        """Test get_latest_ingestion_run_by_kb_id method."""
        dao = IngestionRunDao(mock_session)
        
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_ingestion_run
        mock_session.execute.return_value = mock_result
        
        result = await dao.get_latest_ingestion_run_by_kb_id(TEST_KB_ID)
        
        assert result == sample_ingestion_run
        mock_session.execute.assert_called_once()


class TestSourceItemDao:
    """Test cases for SourceItemDao class."""
    
    def test_init(self, mock_session):
        """Test SourceItemDao initialization."""
        dao = SourceItemDao(mock_session)
        
        assert dao.session == mock_session
        assert dao.db_model == SourceItem
    
    @pytest.mark.asyncio
    async def test_bulk_insert(self, mock_session):
        """Test bulk_insert method."""
        dao = SourceItemDao(mock_session)
        
        source_items = [{"id": "item1", "kb_id": TEST_KB_ID}]
        
        await dao.bulk_insert(source_items)
        
        mock_session.add_all.assert_called_once()
        mock_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_delete_by_id(self, mock_session):
        """Test delete_by_id method."""
        dao = SourceItemDao(mock_session)
        
        await dao.delete_by_id(TEST_SOURCE_ITEM_ID)
        
        mock_session.execute.assert_called_once()
        mock_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_all_by_kb_id(self, mock_session, sample_source_item):
        """Test get_all_by_kb_id method."""
        dao = SourceItemDao(mock_session)
        
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_source_item]
        mock_session.execute.return_value = mock_result
        
        result = await dao.get_all_by_kb_id(TEST_KB_ID)
        
        assert result == [sample_source_item]
        mock_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_delete_by_kb_id_logical_path(self, mock_session, sample_source_item):
        """Test delete_by_kb_id_logical_path method."""
        dao = SourceItemDao(mock_session)
        
        mock_result = Mock()
        mock_result.unique.return_value.scalars.return_value.all.return_value = [sample_source_item]
        mock_session.execute.return_value = mock_result
        
        result = await dao.delete_by_kb_id_logical_path(TEST_KB_ID, TEST_LOGICAL_PATH)
        
        assert isinstance(result, list)
        mock_session.execute.assert_called_once()
        mock_session.flush.assert_called_once()
        mock_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_by_kb_id_logical_path(self, mock_session, sample_source_item):
        """Test get_by_kb_id_logical_path method."""
        dao = SourceItemDao(mock_session)
        
        mock_result = Mock()
        mock_result.unique.return_value.scalars.return_value.all.return_value = [sample_source_item]
        mock_session.execute.return_value = mock_result
        
        result = await dao.get_by_kb_id_logical_path(TEST_KB_ID, TEST_LOGICAL_PATH)
        
        assert result == [sample_source_item]
        mock_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_kb_source_and_chunk_counts(self, mock_session):
        """Test get_kb_source_and_chunk_counts method."""
        dao = SourceItemDao(mock_session)
        
        # Mock source count result
        mock_source_result = Mock()
        mock_source_result.scalar_one_or_none.return_value = SOURCE_COUNT
        
        # Mock chunk count result
        mock_chunk_result = Mock()
        mock_chunk_result.scalar_one_or_none.return_value = CHUNK_COUNT
        
        # Mock paths result - fix the iteration issue
        mock_paths_result = Mock()
        mock_paths_result.__iter__ = Mock(return_value=iter([(TEST_LOGICAL_PATH,)]))
        
        # Configure session to return different results for different queries
        mock_session.execute.side_effect = [
            mock_source_result, 
            mock_chunk_result,
            mock_paths_result
        ]
        
        result = await dao.get_kb_source_and_chunk_counts(TEST_KB_ID)
        
        assert result["source_count"] == SOURCE_COUNT
        assert result["chunk_count"] == CHUNK_COUNT
        assert isinstance(result["paths"], list)


class TestChunkDao:
    """Test cases for ChunkDao class."""
    
    def test_init(self, mock_session):
        """Test ChunkDao initialization."""
        dao = ChunkDao(mock_session)
        
        assert dao.session == mock_session
        assert dao.db_model == Chunk
    
    @pytest.mark.asyncio
    async def test_bulk_insert(self, mock_session):
        """Test bulk_insert method."""
        dao = ChunkDao(mock_session)
        
        chunks = [{"id": "chunk1", "source_item_id": TEST_SOURCE_ITEM_ID}]
        
        await dao.bulk_insert(chunks)
        
        mock_session.add_all.assert_called_once()
        mock_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_count_by_source_item_ids(self, mock_session):
        """Test get_count_by_source_item_ids method."""
        dao = ChunkDao(mock_session)
        
        mock_result = Mock()
        mock_result.scalar_one.return_value = TOTAL_COUNT
        mock_session.execute.return_value = mock_result
        
        source_item_ids = [TEST_SOURCE_ITEM_ID]
        result = await dao.get_count_by_source_item_ids(source_item_ids)
        
        assert result == TOTAL_COUNT
        mock_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_delete_by_id(self, mock_session):
        """Test delete_by_id method."""
        dao = ChunkDao(mock_session)
        
        await dao.delete_by_id(TEST_CHUNK_ID)
        
        mock_session.execute.assert_called_once()
        mock_session.commit.assert_called_once()


class TestIngestionErrorDao:
    """Test cases for IngestionErrorDao class."""
    
    def test_init(self, mock_session):
        """Test IngestionErrorDao initialization."""
        dao = IngestionErrorDao(mock_session)
        
        assert dao.session == mock_session
        assert dao.db_model == IngestionError
    
    @pytest.mark.parametrize("commit_flag", [True, False])
    @pytest.mark.asyncio
    async def test_create_ingestion_error(self, mock_session, commit_flag):
        """Test create_ingestion_error with and without commit."""
        dao = IngestionErrorDao(mock_session)
        
        kwargs = {"run_id": TEST_RUN_ID, "error_message": ERROR_MESSAGE}
        result = await dao.create_ingestion_error(commit=commit_flag, **kwargs)
        
        assert isinstance(result, IngestionError)
        mock_session.add.assert_called_once()
        mock_session.flush.assert_called_once()
        
        if commit_flag:
            mock_session.commit.assert_called_once()
        else:
            mock_session.commit.assert_not_called()


class TestDaoIntegration:
    """Integration tests for DAO classes."""
    
    def test_all_daos_inherit_from_base(self):
        """Test that all DAO classes inherit from BaseDao."""
        from utils.dao import BaseDao
        
        daos = [
            IntegrationDao, KnowledgeBaseDao, IngestionRunDao,
            SourceItemDao, ChunkDao, IngestionErrorDao
        ]
        
        for dao_class in daos:
            # Create instance with mock session to test inheritance
            mock_session = Mock()
            dao_instance = dao_class(mock_session)
            assert isinstance(dao_instance, BaseDao)
    
    def test_dao_model_associations(self):
        """Test that DAO classes are associated with correct models."""
        mock_session = Mock()
        
        dao_model_pairs = [
            (IntegrationDao, Integration),
            (KnowledgeBaseDao, KnowledgeBase),
            (IngestionRunDao, IngestionRun),
            (SourceItemDao, SourceItem),
            (ChunkDao, Chunk),
            (IngestionErrorDao, IngestionError)
        ]
        
        for dao_class, model_class in dao_model_pairs:
            dao_instance = dao_class(mock_session)
            assert dao_instance.db_model == model_class
    
    def test_dao_session_consistency(self):
        """Test that all DAO instances use the same session."""
        mock_session = Mock()
        
        dao_classes = [
            IntegrationDao, KnowledgeBaseDao, IngestionRunDao,
            SourceItemDao, ChunkDao, IngestionErrorDao
        ]
        
        for dao_class in dao_classes:
            dao_instance = dao_class(mock_session)
            assert dao_instance.session == mock_session 