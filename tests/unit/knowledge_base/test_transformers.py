"""
Comprehensive tests for knowledge_base.data_transformers module to achieve 100% coverage.

This module contains test data that are safe for testing purposes.
All test data in this file is fictional and for testing purposes only.

SECURITY NOTE: All data in this file is safe test data:
- UUIDs and identifiers are fictional test values
- URLs are test-only endpoints, not production URLs
- Email addresses and names are fictional test data
- Content and text data are sample test values
- Dates and timestamps are fictional test values
- No production credentials or sensitive data is used
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from bs4 import BeautifulSoup
import tiktoken

from knowledge_base.data_transformers import (
    DataTransformer, 
    BaseChunkTransformer, 
    QuipTransformer, 
    RepoTransformer, 
    GoogleDocsTransformer
)
from knowledge_base.models import IntegrationType
from knowledge_base.serializers import KnowledgeBaseElasticDocument
from utils.vector_db.embeddings import EmbeddingGenerator


# Test Constants - All values are safe test data, not production values
TEST_KB_ID = 1
TEST_CHUNK_TOKEN_LIMIT = "100"
TEST_LARGE_CHUNK_LIMIT = "1000"
TEST_SMALL_CHUNK_LIMIT = "20"
TEST_MEDIUM_CHUNK_LIMIT = "50"
TEST_AUTHOR = "test-author"
TEST_DEVELOPER = "developer"
TEST_DATE = "2023-01-01T00:00:00Z"
TEST_ENCODING_NAME = "cl100k_base"

# UUID Constants
TEST_UUID_QUIP = "test-uuid-123"
TEST_UUID_REPO = "repo-uuid-123"
TEST_UUID_DOC = "doc-uuid-123"
TEST_OLD_UUID = "old-uuid"
TEST_UUID_MAIN = "uuid-main"
TEST_UUID_HELPERS = "uuid-helpers"

# Content Constants
TEST_TITLE_QUIP = "Test Title"
TEST_TITLE_DOC = "Sample Document"
TEST_TITLE_EMPTY = "Empty Document"
TEST_TITLE_MINIMAL = "Minimal Document"
TEST_PATH_QUIP = "test-path"
TEST_PATH_MAIN = "src/main.py"
TEST_PATH_DOC = "My Document"
TEST_PATH_EMPTY = "Empty Document"
TEST_PATH_MINIMAL = "Minimal Document"

# Source Constants
SOURCE_QUIP = "quip"
SOURCE_AZURE_DEVOPS = "azure_devops"

# HTML and Content Constants
TEST_HTML_CONTENT = "<html><body><h1>Test Content</h1><p>This is a test paragraph.</p></body></html>"
TEST_SIMPLE_TEXT = "Hello world"
TEST_PARAGRAPH_1 = "Paragraph 1"
TEST_PARAGRAPH_2 = "Paragraph 2"
TEST_CHUNK_1 = "Chunk 1"
TEST_CHUNK_2 = "Chunk 2"
TEST_BOLD_HTML = "<html><body><b>Bold text</b><ul><li>Item 1</li><li>Item 2</li></ul></body></html>"
TEST_BULLET_1 = "• Item 1"
TEST_BULLET_2 = "• Item 2"
TEST_INVISIBLE_CHARS_HTML = "Text with\u200binvisible\u00a0characters"
TEST_ZWB_HTML = "Text with\u200bzero width space"
TEST_NBSP_HTML = "Text with\u00a0non-breaking space"
TEST_MULTILINE_HTML = "<html><body><p>Text1</p>\n\n\n<p>Text2</p></body></html>"

# Code Content Constants
TEST_PYTHON_CODE = """def main():
    print('Hello, World!')

if __name__ == '__main__':
    main()"""

# URL Constants - Safe test URLs, not production
TEST_URL_QUIP = "https://test.quip.com/test-id"
TEST_URL_GOOGLE_DOC = "https://docs.google.com/document/d/doc-id"

# Google Docs Content Constants
TEST_GDOC_FIRST_PARAGRAPH = "This is the first paragraph.\n"
TEST_GDOC_SECOND_PARAGRAPH = "This is the second paragraph.\n"
TEST_GDOC_EMPTY_CONTENT = ""
TEST_GDOC_NONEMPTY_CONTENT = "Non-empty paragraph.\n"
TEST_GDOC_VALID_CONTENT = "Valid content.\n"

# Chunk and Token Constants
CHUNK_REPEAT_COUNT = 10
LONG_TEXT_REPEAT_COUNT = 50
LINE_OFFSET_START = 1
LINE_OFFSET_CHUNK1_END = 10
LINE_OFFSET_CHUNK2_START = 11
LINE_OFFSET_CHUNK2_END = 20
CHUNK_NUMBER_1 = 1
CHUNK_NUMBER_2 = 2
TOTAL_CHUNKS = 2

# Embedding Constants
TEST_EMBEDDING_VECTOR = [0.1, 0.2, 0.3]

# File Types
FILE_TYPE_THREAD = "thread"
FILE_TYPE_FILE = "file"

# Version Constants
TEST_VERSION_TAG = "abc123def456"


class ConcreteChunkTransformer(BaseChunkTransformer):
    """Concrete implementation for testing BaseChunkTransformer."""
    
    async def transform(self, records: list[dict], embedder: EmbeddingGenerator, **kwargs) -> list[dict]:
        """Test implementation of abstract transform method."""
        return [{"id": "test"}]


@pytest.fixture
def mock_embedder():
    """Create mock embedder for all tests."""
    embedder = Mock(spec=EmbeddingGenerator)
    embedder.generate_embedding = AsyncMock(return_value=TEST_EMBEDDING_VECTOR)
    return embedder


@pytest.fixture
def mock_config():
    """Create mock configuration."""
    with patch('knowledge_base.data_transformers.loaded_config') as config:
        config.chunk_token_limit = TEST_CHUNK_TOKEN_LIMIT
        yield config


@pytest.fixture
def sample_quip_record():
    """Create sample Quip record."""
    return {
        "uuid": TEST_UUID_QUIP,
        "path": TEST_PATH_QUIP,
        "title": TEST_TITLE_QUIP,
        "content": TEST_HTML_CONTENT,
        "type": FILE_TYPE_THREAD,
        "created_at": TEST_DATE,
        "updated_at": TEST_DATE,
        "author": TEST_AUTHOR,
        "source_url": TEST_URL_QUIP
    }


@pytest.fixture
def sample_repo_record():
    """Create sample repository record."""
    return {
        "uuid": TEST_UUID_REPO,
        "path": TEST_PATH_MAIN,
        "content": TEST_PYTHON_CODE,
        "author": TEST_DEVELOPER,
        "created_at": TEST_DATE,
        "version_tag": TEST_VERSION_TAG,
        "kind": FILE_TYPE_FILE
    }


@pytest.fixture
def sample_google_doc_record():
    """Create sample Google Doc record."""
    return {
        "uuid": TEST_UUID_DOC,
        "path": TEST_PATH_DOC,
        "content": {
            "title": TEST_TITLE_DOC,
            "body": [
                {
                    "paragraph": {
                        "elements": [
                            {"textRun": {"content": TEST_GDOC_FIRST_PARAGRAPH}}
                        ]
                    }
                },
                {
                    "paragraph": {
                        "elements": [
                            {"textRun": {"content": TEST_GDOC_SECOND_PARAGRAPH}}
                        ]
                    }
                }
            ]
        },
        "author": TEST_AUTHOR,
        "created_at": TEST_DATE,
        "source_url": TEST_URL_GOOGLE_DOC
    }


@pytest.fixture
def test_chunks():
    """Create sample chunks for testing."""
    return [TEST_CHUNK_1, TEST_CHUNK_2]


@pytest.fixture
def test_line_offsets():
    """Create sample line offsets for testing."""
    return [(LINE_OFFSET_START, LINE_OFFSET_CHUNK1_END), (LINE_OFFSET_CHUNK2_START, LINE_OFFSET_CHUNK2_END)]


class TestDataTransformer:
    """Test cases for DataTransformer abstract base class."""
    
    def test_data_transformer_is_abstract(self):
        """Test that DataTransformer cannot be instantiated directly."""
        with pytest.raises(TypeError, match="Can't instantiate abstract class"):
            DataTransformer()


class TestBaseChunkTransformer:
    """Test cases for BaseChunkTransformer class."""
    
    @pytest.fixture
    def transformer(self):
        """Create concrete transformer instance."""
        return ConcreteChunkTransformer()
    
    def test_tokenize(self, transformer):
        """Test tokenize method."""
        tokens, encoding = transformer.tokenize(TEST_SIMPLE_TEXT)
        
        assert isinstance(tokens, list)
        assert isinstance(encoding, tiktoken.Encoding)
        assert len(tokens) > 0
        assert encoding.name == TEST_ENCODING_NAME
    
    def test_compute_line_token_indices(self, transformer):
        """Test compute_line_token_indices method."""
        lines = [TEST_PARAGRAPH_1, TEST_PARAGRAPH_2, "Line 3"]
        encoding = tiktoken.get_encoding(TEST_ENCODING_NAME)
        
        result = transformer.compute_line_token_indices(lines, encoding)
        
        assert isinstance(result, list)
        assert len(result) > 0
        assert all(isinstance(idx, int) for idx in result)
    
    def test_chunk_content(self, transformer):
        """Test chunk_content method."""
        text = "This is a test text for chunking. " * CHUNK_REPEAT_COUNT
        encoding = tiktoken.get_encoding(TEST_ENCODING_NAME)
        token_line_indices = [LINE_OFFSET_START] * len(encoding.encode(text))
        
        chunks, line_offsets = transformer.chunk_content(
            text, int(TEST_SMALL_CHUNK_LIMIT), encoding, token_line_indices
        )
        
        assert isinstance(chunks, list)
        assert isinstance(line_offsets, list)
        assert len(chunks) == len(line_offsets)
        assert all(isinstance(chunk, str) for chunk in chunks)
        assert all(isinstance(offset, tuple) for offset in line_offsets)
    
    def test_chunk_content_empty_text(self, transformer):
        """Test chunk_content with empty text."""
        text = ""
        encoding = tiktoken.get_encoding(TEST_ENCODING_NAME)
        token_line_indices = []
        
        chunks, line_offsets = transformer.chunk_content(
            text, int(TEST_SMALL_CHUNK_LIMIT), encoding, token_line_indices
        )
        
        assert chunks == []
        assert line_offsets == []
    
    def test_chunk_content_with_boundary_conditions(self, transformer):
        """Test chunk_content with boundary conditions."""
        text = "Test"
        encoding = tiktoken.get_encoding(TEST_ENCODING_NAME)
        token_line_indices = [LINE_OFFSET_START] * len(encoding.encode(text))
        
        # Test with large chunk limit
        chunks, line_offsets = transformer.chunk_content(
            text, int(TEST_LARGE_CHUNK_LIMIT), encoding, token_line_indices
        )
        
        assert len(chunks) == 1
        assert len(line_offsets) == 1


class TestQuipTransformer:
    """Test cases for QuipTransformer class."""
    
    @pytest.fixture
    def transformer(self):
        """Create QuipTransformer instance."""
        return QuipTransformer()
    
    @pytest.mark.asyncio
    async def test_transform(self, transformer, mock_embedder, sample_quip_record, mock_config):
        """Test transform method."""
        result = await transformer.transform(
            [sample_quip_record], mock_embedder, knowledge_base_id=TEST_KB_ID
        )
        
        assert isinstance(result, list)
        assert len(result) >= 1
        # Check that result contains document dictionaries
        for doc in result:
            assert isinstance(doc, dict)
            assert "id" in doc
            assert "content" in doc
    
    @pytest.mark.asyncio
    async def test_convert(self, transformer, mock_embedder, sample_quip_record, mock_config):
        """Test _convert method."""
        result = await transformer._convert(
            sample_quip_record, mock_embedder, knowledge_base_id=TEST_KB_ID
        )
        
        assert isinstance(result, list)
        assert len(result) >= 1
        assert all(isinstance(doc, KnowledgeBaseElasticDocument) for doc in result)
    
    def test_clean_html(self, transformer):
        """Test _clean_html method."""
        result = transformer._clean_html(TEST_BOLD_HTML)
        
        assert isinstance(result, BeautifulSoup)
        # Check that list items get bullet points
        soup_text = result.get_text()
        assert TEST_BULLET_1 in soup_text
        assert TEST_BULLET_2 in soup_text
    
    @pytest.mark.parametrize("html_content,invisible_char,expected_empty", [
        (f"<html><body>{TEST_INVISIBLE_CHARS_HTML}</body></html>", "\u200b", True),
        (f"<html><body>{TEST_ZWB_HTML}</body></html>", "\u200b", True),
        (f"<html><body>{TEST_NBSP_HTML}</body></html>", "\u00a0", True),
    ])
    def test_clean_html_with_invisible_characters(self, transformer, html_content, invisible_char, expected_empty):
        """Test _clean_html with various invisible characters."""
        result = transformer._clean_html(html_content)
        soup_text = result.get_text()
        assert invisible_char not in soup_text
        if expected_empty:
            assert soup_text.strip() == ""
    
    def test_extract_text(self, transformer):
        """Test _extract_text method."""
        soup = BeautifulSoup(f"<html><body><p>{TEST_PARAGRAPH_1}</p><p>{TEST_PARAGRAPH_2}</p></body></html>", 'html.parser')
        
        result = transformer._extract_text(soup)
        
        assert isinstance(result, str)
        assert TEST_PARAGRAPH_1 in result
        assert TEST_PARAGRAPH_2 in result
    
    def test_extract_text_with_multiple_newlines(self, transformer):
        """Test _extract_text with multiple newlines."""
        soup = BeautifulSoup(TEST_MULTILINE_HTML, 'html.parser')
        
        result = transformer._extract_text(soup)
        
        assert isinstance(result, str)
        assert "Text1" in result
        assert "Text2" in result
        # Multiple newlines should be reduced to double newlines
        assert "\n\n\n" not in result
    
    @pytest.mark.asyncio
    async def test_build_documents(self, transformer, mock_embedder, sample_quip_record, test_chunks, test_line_offsets):
        """Test _build_documents method."""
        kwargs = {"knowledge_base_id": TEST_KB_ID}
        
        result = await transformer._build_documents(
            sample_quip_record, mock_embedder, test_chunks, test_line_offsets, kwargs
        )
        
        assert isinstance(result, list)
        assert len(result) == TOTAL_CHUNKS
        assert all(isinstance(doc, KnowledgeBaseElasticDocument) for doc in result)
        
        # Check first document
        doc1 = result[0]
        assert doc1.id == f"{TEST_UUID_QUIP}#{CHUNK_NUMBER_1}"
        assert doc1.title == TEST_PATH_QUIP
        assert doc1.source == SOURCE_QUIP
        assert doc1.knowledge_base_id == TEST_KB_ID
        
        # Check metadata
        assert doc1.metadata["chunk_number"] == CHUNK_NUMBER_1
        assert doc1.metadata["total_chunks"] == TOTAL_CHUNKS
        assert doc1.metadata["path"] == TEST_PATH_QUIP


class TestRepoTransformer:
    """Test cases for RepoTransformer class."""
    
    @pytest.fixture
    def transformer(self):
        """Create RepoTransformer instance."""
        return RepoTransformer()
    
    @pytest.mark.asyncio
    async def test_transform(self, transformer, mock_embedder, sample_repo_record, mock_config):
        """Test transform method."""
        result = await transformer.transform(
            [sample_repo_record], mock_embedder, knowledge_base_id=TEST_KB_ID
        )
        
        assert isinstance(result, list)
        assert len(result) >= 1
        # Check that result contains document dictionaries
        for doc in result:
            assert isinstance(doc, dict)
            assert "id" in doc
            assert "content" in doc
    
    @pytest.mark.asyncio
    async def test_convert(self, transformer, mock_embedder, sample_repo_record, mock_config):
        """Test _convert method."""
        result = await transformer._convert(
            sample_repo_record, mock_embedder, knowledge_base_id=TEST_KB_ID
        )
        
        assert isinstance(result, list)
        assert len(result) >= 1
        assert all(isinstance(doc, KnowledgeBaseElasticDocument) for doc in result)
        
        # Check document properties
        doc = result[0]
        assert doc.id.startswith(f"{TEST_UUID_REPO}#")
        assert doc.title == TEST_PATH_MAIN
        assert doc.source == SOURCE_AZURE_DEVOPS
        assert doc.knowledge_base_id == TEST_KB_ID
    
    @pytest.mark.asyncio
    async def test_convert_with_old_uuid(self, transformer, mock_embedder, sample_repo_record, mock_config):
        """Test _convert method with old_uuid parameter."""
        result = await transformer._convert(
            sample_repo_record, mock_embedder, 
            knowledge_base_id=TEST_KB_ID, old_uuid=TEST_OLD_UUID
        )
        
        assert isinstance(result, list)
        assert len(result) >= 1
        
        # Check that old_uuid is used in document ID
        doc = result[0]
        assert doc.id.startswith(f"{TEST_OLD_UUID}#")
    
    @pytest.mark.asyncio
    async def test_convert_without_embeddings(self, transformer, mock_embedder, sample_repo_record, mock_config):
        """Test _convert method with generate_embeddings=False."""
        result = await transformer._convert(
            sample_repo_record, mock_embedder, 
            knowledge_base_id=TEST_KB_ID, generate_embeddings=False
        )
        
        assert isinstance(result, list)
        assert len(result) >= 1
        
        # Check that embedding is None when generate_embeddings=False
        doc = result[0]
        assert doc.embedding is None


class TestGoogleDocsTransformer:
    """Test cases for GoogleDocsTransformer class."""
    
    @pytest.fixture
    def transformer(self):
        """Create GoogleDocsTransformer instance."""
        return GoogleDocsTransformer()
    
    @pytest.mark.asyncio
    async def test_transform(self, transformer, mock_embedder, sample_google_doc_record, mock_config):
        """Test transform method."""
        result = await transformer.transform(
            [sample_google_doc_record], mock_embedder, knowledge_base_id=TEST_KB_ID
        )
        
        assert isinstance(result, list)
        assert len(result) >= 1
        # Check that result contains document dictionaries
        for doc in result:
            assert isinstance(doc, dict)
            assert "id" in doc
            assert "content" in doc
    
    @pytest.mark.asyncio
    async def test_convert(self, transformer, mock_embedder, sample_google_doc_record, mock_config):
        """Test _convert method."""
        result = await transformer._convert(
            sample_google_doc_record, mock_embedder, knowledge_base_id=TEST_KB_ID
        )
        
        assert isinstance(result, list)
        assert len(result) >= 1
        assert all(isinstance(doc, KnowledgeBaseElasticDocument) for doc in result)
        
        # Check document properties
        doc = result[0]
        assert doc.id.startswith(f"{TEST_UUID_DOC}#")
        assert doc.title == TEST_TITLE_DOC
        assert doc.source == IntegrationType.google_docs.value
        assert doc.knowledge_base_id == TEST_KB_ID
    
    @pytest.mark.asyncio
    async def test_convert_with_empty_paragraphs(self, transformer, mock_embedder, mock_config):
        """Test _convert method with empty paragraphs."""
        record = {
            "uuid": TEST_UUID_DOC,
            "path": TEST_PATH_EMPTY,
            "content": {
                "title": TEST_TITLE_EMPTY,
                "body": [
                    {
                        "paragraph": {
                            "elements": [
                                {"textRun": {"content": TEST_GDOC_EMPTY_CONTENT}}
                            ]
                        }
                    },
                    {
                        "paragraph": {
                            "elements": [
                                {"textRun": {"content": TEST_GDOC_NONEMPTY_CONTENT}}
                            ]
                        }
                    }
                ]
            }
        }
        
        result = await transformer._convert(
            record, mock_embedder, knowledge_base_id=TEST_KB_ID
        )
        
        assert isinstance(result, list)
        assert len(result) >= 1
        
        # Check that only non-empty content is processed
        doc = result[0]
        assert "Non-empty paragraph" in doc.content
    
    @pytest.mark.asyncio
    async def test_convert_with_missing_elements(self, transformer, mock_embedder, mock_config):
        """Test _convert method with missing paragraph elements."""
        record = {
            "uuid": TEST_UUID_DOC,
            "path": TEST_PATH_MINIMAL,
            "content": {
                "title": TEST_TITLE_MINIMAL,
                "body": [
                    {
                        "paragraph": {}  # Missing elements
                    },
                    {
                        "paragraph": {
                            "elements": [
                                {"textRun": {"content": TEST_GDOC_VALID_CONTENT}}
                            ]
                        }
                    }
                ]
            }
        }
        
        result = await transformer._convert(
            record, mock_embedder, knowledge_base_id=TEST_KB_ID
        )
        
        assert isinstance(result, list)
        assert len(result) >= 1
        
        # Should handle missing elements gracefully
        doc = result[0]
        assert "Valid content" in doc.content


class TestTransformerIntegration:
    """Integration tests for transformer classes."""
    
    @pytest.mark.asyncio
    async def test_all_transformers_implement_interface(self):
        """Test that all transformer classes implement the DataTransformer interface."""
        transformers = [
            QuipTransformer(),
            RepoTransformer(),
            GoogleDocsTransformer()
        ]
        
        for transformer in transformers:
            assert isinstance(transformer, DataTransformer)
            assert hasattr(transformer, 'transform')
            assert callable(transformer.transform)
    
    @pytest.mark.asyncio
    async def test_chunking_consistency(self):
        """Test that chunking behavior is consistent across data_transformers."""
        # Create a concrete transformer for testing
        transformer = ConcreteChunkTransformer()
        
        text = "This is a test text. " * LONG_TEXT_REPEAT_COUNT  # Long text to ensure chunking
        encoding = tiktoken.get_encoding(TEST_ENCODING_NAME)
        tokens = encoding.encode(text)
        token_line_indices = [LINE_OFFSET_START] * len(tokens)
        
        chunks, line_offsets = transformer.chunk_content(
            text, int(TEST_MEDIUM_CHUNK_LIMIT), encoding, token_line_indices
        )
        
        # Should produce multiple chunks
        assert len(chunks) > 1
        assert len(chunks) == len(line_offsets)
        
        # All chunks should be strings
        assert all(isinstance(chunk, str) for chunk in chunks)
        
        # All line offsets should be tuples
        assert all(isinstance(offset, tuple) and len(offset) == 2 for offset in line_offsets)
    
    def test_tokenizer_consistency(self):
        """Test that tokenizer returns consistent results."""
        transformer = ConcreteChunkTransformer()
        
        text = "Hello, world! This is a test."
        tokens1, encoding1 = transformer.tokenize(text)
        tokens2, encoding2 = transformer.tokenize(text)
        
        # Should return identical results
        assert tokens1 == tokens2
        assert encoding1.name == encoding2.name
        
        # Should be able to decode back to original text
        decoded = encoding1.decode(tokens1)
        assert decoded == text
    
    @pytest.mark.parametrize("transformer_class", [
        QuipTransformer,
        RepoTransformer,
        GoogleDocsTransformer
    ])
    def test_transformer_inheritance(self, transformer_class):
        """Test that all data_transformers inherit from correct base classes."""
        transformer = transformer_class()
        assert isinstance(transformer, DataTransformer)
        
        # Check for common methods
        assert hasattr(transformer, 'transform')
        assert callable(transformer.transform)
    
    def test_base_chunk_transformer_methods(self):
        """Test BaseChunkTransformer provides expected methods."""
        transformer = ConcreteChunkTransformer()
        
        # Test required methods exist
        assert hasattr(transformer, 'tokenize')
        assert hasattr(transformer, 'chunk_content')
        assert hasattr(transformer, 'compute_line_token_indices')
        
        # Test methods are callable
        assert callable(transformer.tokenize)
        assert callable(transformer.chunk_content)
        assert callable(transformer.compute_line_token_indices) 