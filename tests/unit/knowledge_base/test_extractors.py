"""
Comprehensive tests for knowledge_base.extractors module to achieve 100% coverage.

This module contains test data that are safe for testing purposes.
All test data in this file is fictional and for testing purposes only.

SECURITY NOTE: All data in this file is safe test data:
- URLs are test-only endpoints, not production URLs
- Repository URLs are fictional test repositories
- API tokens are fictional test values, not real credentials
- User names and identifiers are fictional test data
- Organization names and project names are fictional test values
- Commit SHAs and blob SHAs are fictional test values
- No production credentials or sensitive data is used
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import asyncio
import os
import tempfile
import json
from pathlib import Path

from knowledge_base.extractors import (
    DataExtractor,
    GitRepoExtractor,
    GitLabRepoExtractor,
    GitHubRepoExtractor,
    AzureDevopsRepoExtractor,
    GoogleDocsExtractor,
    QuipExtractor
)
from knowledge_base.constants import (
    REPO_URL_REQUIRED,
    INVALID_REPO_URL_FORMAT,
    <PERSON><PERSON><PERSON>_NAME_REQUIRED,
    ALL_CRED_ARE_VALID,
    INVALID_FOLDER_CHARS_REGEX
)
from knowledge_base.models import SourceItemKind
from utils.exceptions import CustomException


# Test Constants - All values are safe test data, not production values
# URL Constants - All fictional test URLs
TEST_GITHUB_URL = "https://github.com/test/repo"
TEST_GITLAB_URL = "https://gitlab.com/test/repo"
TEST_AZURE_URL = "https://<EMAIL>/org/project/_git/repo"
TEST_GOOGLE_DOC_URL = "https://docs.google.com/document/d/doc1"
TEST_QUIP_URL = "https://quip.com/folder1"
TEST_QUIP_THREAD_URL = "https://quip.com/thread1"
TEST_QUIP_EXTRACT_URL = "https://quip.com/test123"
TEST_QUIP_EXTRACT_TITLE_URL = "https://quip.com/test123/some-title"
TEST_QUIP_EMPTY_URL = "https://quip.com/"

# Branch and Git Constants
TEST_BRANCH = "main"
TEST_TOKEN = "token"
TEST_COMMIT_SHA = "abc123def456"
TEST_SHORT_COMMIT_SHA = "abc123"
TEST_BLOB_SHA = "abc123def456"
TEST_REVISION_ID = "rev123"

# File and Path Constants  
TEST_FILE_NAME = "test.py"
TEST_FILE_CONTENT = "print('hello')"
TEST_TEMP_DIR_PREFIX = "/Users/<USER>/repo_temp/"
TEST_CLONE_PATH_PARTS = 4

# HTTP Status Codes
HTTP_OK = 200
HTTP_UNAUTHORIZED = 401
HTTP_NOT_FOUND = 404
HTTP_SERVER_ERROR = 500
GIT_SUCCESS_CODE = 0
GIT_FAILURE_CODE = 1

# User and Organization Constants
TEST_USER = "test-user"
TEST_ORG = "org"
TEST_PROJECT = "project"
TEST_REPO = "repo"
TEST_AUTHOR = "test-author"

# Document Constants
TEST_DOC_ID = "doc1"
TEST_DOC_ID_2 = "doc2"
TEST_DOC_TITLE = "Test Doc"
TEST_DOC_CONTENT = "Test content"
TEST_FOLDER_ID = "folder1"
TEST_THREAD_ID = "thread1"
TEST_THREAD_123 = "thread123"
TEST_FOLDER_123 = "folder123"

# Quip Constants
TEST_QUIP_MAX_DOCS = 10
TEST_QUIP_EXTRACT_ID = "test123"
TEST_QUIP_FOLDER_NAME = "Folder-folder12"
TEST_QUIP_USER_NAME = "Test User"

# Error Messages
ERROR_INVALID_URL = "Invalid URL format"
ERROR_URL_REQUIRED = "URL is required"
ERROR_GIT_CLONE_FAILED = "Git clone failed"
ERROR_CLONE_REPO = "Error cloning repository"
ERROR_SUBPROCESS = "subprocess error"
ERROR_INVALID_AZURE_URL = "Invalid Azure DevOps repository URL"
ERROR_INVALID_GITHUB_URL = "Invalid GitHub repository URL"
ERROR_INVALID_GITLAB_URL = "Invalid GitLab repository URL"
ERROR_QUIP_AUTH_FAILED = "Failed to authenticate Quip"
ERROR_NO_DOCUMENTS = "You haven't selected any document."

# Response Data Constants
RESPONSE_SUCCESS = b"success"
RESPONSE_ERROR = b"error"
RESPONSE_EMPTY = b""
RESPONSE_COMMIT_SHA = "abc123def456\n"
RESPONSE_BLOB_INFO = "100644 blob abc123def456 file.txt\n"

# JSON Response Constants
JSON_LOGIN_USER = {"login": TEST_USER}
JSON_USER_NAME = {"user": {"name": TEST_QUIP_USER_NAME}}
JSON_PRIVATE_REPO = {"private": True}
JSON_PUBLIC_REPO = {"private": False}
JSON_PUBLIC_VISIBILITY = {"visibility": "public"}
JSON_BRANCHES = {"value": [{"name": TEST_BRANCH}]}
JSON_DOC_RESPONSE = {
    "title": TEST_DOC_TITLE,
    "revisionId": TEST_REVISION_ID,
    "body": {"content": [{"paragraph": {"elements": [{"textRun": {"content": TEST_DOC_CONTENT}}]}}]}
}
JSON_DOC_SIMPLE = {"title": TEST_DOC_TITLE, "body": {"content": []}}

# Validation Messages
MSG_VALID_TOKEN = "Token is valid"
MSG_INVALID_CREDS = "Invalid credentials"
MSG_VALID_CREDS = "Valid credentials"
MSG_NOT_FOUND = "Not found"


@pytest.fixture
def mock_http_client():
    """Create mock HTTP client with common configuration."""
    with patch('httpx.AsyncClient') as mock_client:
        mock_response = Mock()
        mock_response.status_code = HTTP_OK
        mock_response.json.return_value = JSON_LOGIN_USER
        mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
        yield mock_client


@pytest.fixture
def mock_subprocess_success():
    """Create mock subprocess with successful response."""
    with patch('asyncio.create_subprocess_exec') as mock_subprocess:
        mock_process = Mock()
        mock_process.returncode = GIT_SUCCESS_CODE
        mock_process.communicate = AsyncMock(return_value=(RESPONSE_SUCCESS, RESPONSE_EMPTY))
        mock_subprocess.return_value = mock_process
        yield mock_subprocess


@pytest.fixture
def mock_subprocess_failure():
    """Create mock subprocess with failure response."""
    with patch('asyncio.create_subprocess_exec') as mock_subprocess:
        mock_process = Mock()
        mock_process.returncode = GIT_FAILURE_CODE
        mock_process.communicate = AsyncMock(return_value=(RESPONSE_EMPTY, RESPONSE_ERROR))
        mock_subprocess.return_value = mock_process
        yield mock_subprocess


@pytest.fixture
def mock_subprocess_run_success():
    """Create mock subprocess run with successful response."""
    with patch('subprocess.run') as mock_run:
        mock_run.return_value.returncode = GIT_SUCCESS_CODE
        mock_run.return_value.stdout = RESPONSE_COMMIT_SHA
        yield mock_run


@pytest.fixture
def mock_subprocess_run_failure():
    """Create mock subprocess run with failure response."""
    with patch('subprocess.run') as mock_run:
        mock_run.return_value.returncode = GIT_FAILURE_CODE
        yield mock_run


@pytest.fixture
def test_docs_list():
    """Create test documents list."""
    return [
        {"id": TEST_DOC_ID, "url": TEST_GOOGLE_DOC_URL},
        {"id": TEST_DOC_ID_2, "url": "https://docs.google.com/document/d/doc2"}
    ]


@pytest.fixture
def test_quip_urls():
    """Create test Quip URLs list."""
    return [TEST_QUIP_URL, TEST_QUIP_THREAD_URL]


@pytest.fixture
def temp_directory():
    """Create temporary directory for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test files
        test_file = os.path.join(temp_dir, TEST_FILE_NAME)
        with open(test_file, "w") as f:
            f.write(TEST_FILE_CONTENT)
        yield temp_dir


class TestDataExtractor:
    """Test cases for DataExtractor abstract base class."""
    
    def test_data_extractor_is_abstract(self):
        """Test that DataExtractor cannot be instantiated directly."""
        with pytest.raises(TypeError, match="Can't instantiate abstract class"):
            DataExtractor()


class ConcreteGitRepoExtractor(GitRepoExtractor):
    """Concrete implementation for testing GitRepoExtractor."""
    
    async def _validate_url(self):
        """Test implementation of _validate_url."""
        if not self.repo_url:
            raise CustomException(ERROR_URL_REQUIRED)
        if "invalid" in self.repo_url:
            raise CustomException(ERROR_INVALID_URL)
    
    async def _get_clone_url(self) -> str:
        """Test implementation of _get_clone_url."""
        if self.pat:
            return f"https://{self.pat}@{self.repo_url.replace('https://', '')}"
        return self.repo_url
    
    async def is_project_public(self) -> bool:
        """Test implementation of is_project_public."""
        return "public" in self.repo_url
    
    async def validate_credentials(self) -> dict:
        """Test implementation of validate_credentials."""
        return {"is_valid": True, "message": MSG_VALID_CREDS}


class TestGitRepoExtractor:
    """Test cases for GitRepoExtractor base class."""
    
    def test_init(self):
        """Test GitRepoExtractor initialization."""
        extractor = ConcreteGitRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN)
        
        assert extractor.repo_url == TEST_GITHUB_URL
        assert extractor.branch_name == TEST_BRANCH
        assert extractor.pat == TEST_TOKEN
        assert not extractor._url_validated
        assert extractor.clone_url is None
    
    @pytest.mark.asyncio
    async def test_async_init(self):
        """Test async initialization."""
        extractor = ConcreteGitRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN)
        
        result = await extractor.async_init()
        
        assert result is extractor
        assert extractor._url_validated
        assert extractor.clone_url == f"https://{TEST_TOKEN}@github.com/test/repo"
    
    @pytest.mark.asyncio
    async def test_clone_repo_success(self, mock_subprocess_success):
        """Test successful repository cloning."""
        extractor = ConcreteGitRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN)
        extractor.clone_url = f"https://{TEST_TOKEN}@github.com/test/repo"
        
        result = await extractor.clone_repo()
        
        assert result.startswith(TEST_TEMP_DIR_PREFIX)
        assert len(result.split("/")) >= TEST_CLONE_PATH_PARTS
        mock_subprocess_success.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_clone_repo_failure(self, mock_subprocess_failure):
        """Test repository cloning failure."""
        extractor = ConcreteGitRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN)
        extractor.clone_url = f"https://{TEST_TOKEN}@github.com/test/repo"
        
        with pytest.raises(CustomException, match=ERROR_GIT_CLONE_FAILED):
            await extractor.clone_repo()
    
    @pytest.mark.asyncio
    async def test_clone_repo_exception(self):
        """Test repository cloning with exception."""
        extractor = ConcreteGitRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN)
        extractor.clone_url = f"https://{TEST_TOKEN}@github.com/test/repo"
        
        with patch('asyncio.create_subprocess_exec', side_effect=Exception(ERROR_SUBPROCESS)):
            with pytest.raises(CustomException, match=ERROR_CLONE_REPO):
                await extractor.clone_repo()
    
    @pytest.mark.asyncio
    async def test_get_commit_sha_success(self, mock_subprocess_run_success):
        """Test successful commit SHA retrieval."""
        extractor = ConcreteGitRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN)
        
        result = await extractor.get_commit_sha("/path/to/repo")
        
        assert result == TEST_COMMIT_SHA
    
    @pytest.mark.asyncio
    async def test_get_commit_sha_failure(self, mock_subprocess_run_failure):
        """Test commit SHA retrieval failure."""
        extractor = ConcreteGitRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN)
        
        result = await extractor.get_commit_sha("/path/to/repo")
        
        assert result == ""
    
    @pytest.mark.asyncio
    async def test_get_git_blob_sha_success(self):
        """Test successful Git blob SHA retrieval."""
        extractor = ConcreteGitRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN)
        
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = GIT_SUCCESS_CODE
            mock_run.return_value.stdout = RESPONSE_BLOB_INFO
            
            result = await extractor.get_git_blob_sha("/path/to/repo", "/path/to/repo/file.txt")
            
            assert result == TEST_BLOB_SHA
    
    @pytest.mark.asyncio
    async def test_get_git_blob_sha_failure(self, mock_subprocess_run_failure):
        """Test Git blob SHA retrieval failure."""
        extractor = ConcreteGitRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN)
        
        result = await extractor.get_git_blob_sha("/path/to/repo", "/path/to/repo/file.txt")
        
        assert result == ""
    
    @pytest.mark.asyncio
    async def test_extract(self, temp_directory):
        """Test extract method."""
        extractor = ConcreteGitRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN)
        
        with patch.object(extractor, 'clone_repo', return_value=temp_directory):
            with patch.object(extractor, 'get_commit_sha', return_value=TEST_SHORT_COMMIT_SHA):
                with patch.object(extractor, 'get_git_blob_sha', return_value=TEST_BLOB_SHA):
                    with patch('code_indexing.file_utils.is_ignored', return_value=False):
                        result = await extractor.extract()
                        
                        assert isinstance(result, list)


class TestGitLabRepoExtractor:
    """Test cases for GitLabRepoExtractor class."""
    
    def test_init(self):
        """Test GitLabRepoExtractor initialization."""
        extractor = GitLabRepoExtractor(TEST_GITLAB_URL, TEST_BRANCH, TEST_TOKEN)
        
        assert extractor.repo_url == TEST_GITLAB_URL
        assert extractor.branch_name == TEST_BRANCH
        assert extractor.pat == TEST_TOKEN
    
    @pytest.mark.asyncio
    async def test_validate_url_valid(self):
        """Test URL validation with valid URL."""
        extractor = GitLabRepoExtractor(TEST_GITLAB_URL, TEST_BRANCH, TEST_TOKEN)
        
        await extractor._validate_url()  # Should not raise exception
    
    @pytest.mark.asyncio
    async def test_validate_url_invalid(self):
        """Test URL validation with invalid URL."""
        extractor = GitLabRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN)
        
        with pytest.raises(ValueError, match=ERROR_INVALID_GITLAB_URL):
            await extractor._validate_url()
    
    @pytest.mark.asyncio
    async def test_validate_url_missing(self):
        """Test URL validation with missing URL."""
        extractor = GitLabRepoExtractor("", TEST_BRANCH, TEST_TOKEN)
        
        with pytest.raises(ValueError, match=ERROR_INVALID_GITLAB_URL):
            await extractor._validate_url()
    
    @pytest.mark.asyncio
    async def test_get_clone_url_with_token(self):
        """Test clone URL generation with token."""
        extractor = GitLabRepoExtractor(TEST_GITLAB_URL, TEST_BRANCH, TEST_TOKEN)
        
        result = await extractor._get_clone_url()
        
        assert result == f"https://oauth2:{TEST_TOKEN}@gitlab.com/test/repo"
    
    @pytest.mark.asyncio
    async def test_get_clone_url_without_token(self):
        """Test clone URL generation without token."""
        extractor = GitLabRepoExtractor(TEST_GITLAB_URL, TEST_BRANCH, None)
        
        result = await extractor._get_clone_url()
        
        assert result == f"https://oauth2:<EMAIL>/test/repo"
    
    @pytest.mark.asyncio
    async def test_is_project_public_true(self, mock_http_client):
        """Test public project detection."""
        extractor = GitLabRepoExtractor(TEST_GITLAB_URL, TEST_BRANCH, TEST_TOKEN)
        
        mock_response = Mock()
        mock_response.status_code = HTTP_OK
        mock_response.json.return_value = JSON_PUBLIC_VISIBILITY
        mock_http_client.return_value.__aenter__.return_value.get.return_value = mock_response
        
        result = await extractor.is_project_public()
        
        assert result is True
    
    @pytest.mark.asyncio
    async def test_is_project_public_false(self, mock_http_client):
        """Test private project detection."""
        extractor = GitLabRepoExtractor(TEST_GITLAB_URL, TEST_BRANCH, TEST_TOKEN)
        
        mock_response = Mock()
        mock_response.status_code = HTTP_OK
        mock_response.json.return_value = {"visibility": "private"}
        mock_http_client.return_value.__aenter__.return_value.get.return_value = mock_response
        
        result = await extractor.is_project_public()
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_is_project_public_error(self, mock_http_client):
        """Test project visibility check with error."""
        extractor = GitLabRepoExtractor(TEST_GITLAB_URL, TEST_BRANCH, TEST_TOKEN)
        
        mock_response = Mock()
        mock_response.status_code = HTTP_NOT_FOUND
        mock_http_client.return_value.__aenter__.return_value.get.return_value = mock_response
        
        result = await extractor.is_project_public()
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_validate_credentials_valid(self, mock_http_client):
        """Test credential validation with valid credentials."""
        extractor = GitLabRepoExtractor(TEST_GITLAB_URL, TEST_BRANCH, TEST_TOKEN)
        
        mock_response = Mock()
        mock_response.status_code = HTTP_OK
        mock_response.json.return_value = {"username": TEST_USER}
        mock_http_client.return_value.__aenter__.return_value.get.return_value = mock_response
        
        result = await extractor.validate_credentials()
        
        assert result["is_valid"] is True
        assert result["message"] == ALL_CRED_ARE_VALID
    
    @pytest.mark.asyncio
    async def test_validate_credentials_invalid(self, mock_http_client):
        """Test credential validation with invalid credentials."""
        extractor = GitLabRepoExtractor(TEST_GITLAB_URL, TEST_BRANCH, TEST_TOKEN)
        
        mock_response = Mock()
        mock_response.status_code = HTTP_UNAUTHORIZED
        mock_http_client.return_value.__aenter__.return_value.get.return_value = mock_response
        
        result = await extractor.validate_credentials()
        
        assert result["is_valid"] is False


class TestGitHubRepoExtractor:
    """Test cases for GitHubRepoExtractor class."""
    
    def test_init(self):
        """Test GitHubRepoExtractor initialization."""
        extractor = GitHubRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN)
        
        assert extractor.repo_url == TEST_GITHUB_URL
        assert extractor.branch_name == TEST_BRANCH
        assert extractor.pat == TEST_TOKEN
    
    @pytest.mark.asyncio
    async def test_validate_url_valid(self):
        """Test URL validation with valid URL."""
        extractor = GitHubRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN)
        
        await extractor._validate_url()  # Should not raise exception
    
    @pytest.mark.asyncio
    async def test_validate_url_invalid(self):
        """Test URL validation with invalid URL."""
        extractor = GitHubRepoExtractor(TEST_GITLAB_URL, TEST_BRANCH, TEST_TOKEN)
        
        with pytest.raises(ValueError, match=ERROR_INVALID_GITHUB_URL):
            await extractor._validate_url()
    
    @pytest.mark.asyncio
    async def test_get_clone_url_with_token(self):
        """Test clone URL generation with token."""
        extractor = GitHubRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN)
        
        result = await extractor._get_clone_url()
        
        assert result == f"https://{TEST_TOKEN}@github.com/test/repo"
    
    @pytest.mark.asyncio
    async def test_get_clone_url_without_token(self):
        """Test clone URL generation without token."""
        extractor = GitHubRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, None)
        
        result = await extractor._get_clone_url()
        
        assert result == TEST_GITHUB_URL
    
    @pytest.mark.asyncio
    async def test_is_project_public_true(self, mock_http_client):
        """Test public project detection."""
        extractor = GitHubRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN)
        
        mock_response = Mock()
        mock_response.status_code = HTTP_OK
        mock_response.json.return_value = JSON_PUBLIC_REPO
        mock_http_client.return_value.__aenter__.return_value.get.return_value = mock_response
        
        result = await extractor.is_project_public()
        
        assert result is True
    
    @pytest.mark.asyncio
    async def test_is_project_public_false(self, mock_http_client):
        """Test private project detection."""
        extractor = GitHubRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN)
        
        mock_response = Mock()
        mock_response.status_code = HTTP_OK
        mock_response.json.return_value = JSON_PRIVATE_REPO
        mock_http_client.return_value.__aenter__.return_value.get.return_value = mock_response
        
        result = await extractor.is_project_public()
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_validate_credentials_valid(self, mock_http_client):
        """Test credential validation with valid credentials."""
        extractor = GitHubRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN)
        
        mock_response = Mock()
        mock_response.status_code = HTTP_OK
        mock_response.json.return_value = JSON_LOGIN_USER
        mock_http_client.return_value.__aenter__.return_value.get.return_value = mock_response
        
        result = await extractor.validate_credentials()
        
        assert result["is_valid"] is True
        assert result["message"] == ALL_CRED_ARE_VALID


class TestAzureDevopsRepoExtractor:
    """Test cases for AzureDevopsRepoExtractor class."""
    
    def test_init(self):
        """Test AzureDevopsRepoExtractor initialization."""
        extractor = AzureDevopsRepoExtractor(TEST_AZURE_URL, TEST_BRANCH, TEST_TOKEN)
        
        assert extractor.repo_url == TEST_AZURE_URL
        assert extractor.branch_name == TEST_BRANCH
        assert extractor.pat == TEST_TOKEN
    
    @pytest.mark.asyncio
    async def test_validate_url_valid(self):
        """Test URL validation with valid URL."""
        extractor = AzureDevopsRepoExtractor(TEST_AZURE_URL, TEST_BRANCH, TEST_TOKEN)
        
        await extractor._validate_url()  # Should not raise exception
    
    @pytest.mark.asyncio
    async def test_validate_url_invalid(self):
        """Test URL validation with invalid URL."""
        extractor = AzureDevopsRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN)
        
        with pytest.raises(ValueError, match=ERROR_INVALID_AZURE_URL):
            await extractor._validate_url()
    
    @pytest.mark.asyncio
    async def test_get_clone_url_with_token(self):
        """Test clone URL generation with token."""
        extractor = AzureDevopsRepoExtractor(TEST_AZURE_URL, TEST_BRANCH, TEST_TOKEN)
        
        result = await extractor._get_clone_url()
        
        assert result == f"https://{TEST_TOKEN}@dev.azure.com/org/project/_git/repo"
    
    @pytest.mark.asyncio
    async def test_get_clone_url_without_token(self):
        """Test clone URL generation without token."""
        extractor = AzureDevopsRepoExtractor(TEST_AZURE_URL, TEST_BRANCH, None)
        
        result = await extractor._get_clone_url()
        
        assert result == f"https://<EMAIL>/org/project/_git/repo"
    
    @pytest.mark.asyncio
    async def test_is_project_public_true(self, mock_http_client):
        """Test public project detection."""
        extractor = AzureDevopsRepoExtractor(TEST_AZURE_URL, TEST_BRANCH, TEST_TOKEN)
        
        mock_response = Mock()
        mock_response.status_code = HTTP_OK
        mock_response.json.return_value = JSON_PUBLIC_VISIBILITY
        mock_http_client.return_value.__aenter__.return_value.get.return_value = mock_response
        
        result = await extractor.is_project_public()
        
        assert result is True
    
    @pytest.mark.asyncio
    async def test_validate_credentials_valid(self, mock_http_client):
        """Test credential validation with valid credentials."""
        extractor = AzureDevopsRepoExtractor(TEST_AZURE_URL, TEST_BRANCH, TEST_TOKEN)
        
        mock_response = Mock()
        mock_response.status_code = HTTP_OK
        mock_response.json.return_value = JSON_BRANCHES
        mock_http_client.return_value.__aenter__.return_value.get.return_value = mock_response
        
        result = await extractor.validate_credentials()
        
        assert result["is_valid"] is True


class TestGoogleDocsExtractor:
    """Test cases for GoogleDocsExtractor class."""
    
    def test_init(self, test_docs_list):
        """Test GoogleDocsExtractor initialization."""
        extractor = GoogleDocsExtractor(test_docs_list, TEST_TOKEN)
        
        assert extractor.docs == test_docs_list
        assert extractor.token == TEST_TOKEN
    
    @pytest.mark.asyncio
    async def test_extract(self, test_docs_list):
        """Test extract method."""
        extractor = GoogleDocsExtractor(test_docs_list, TEST_TOKEN)
        
        with patch.object(extractor, 'get_document_content') as mock_get_content:
            mock_response = Mock()
            mock_response.json.return_value = JSON_DOC_RESPONSE
            mock_get_content.return_value = mock_response
            
            result = await extractor.extract()
            
            assert len(result) == 2
            assert result[0]["kind"] == SourceItemKind.document.value
            assert result[0]["version_tag"] == TEST_REVISION_ID
    
    @pytest.mark.asyncio
    async def test_get_document_content(self, mock_http_client):
        """Test get_document_content method."""
        extractor = GoogleDocsExtractor([], TEST_TOKEN)
        
        mock_response = Mock()
        mock_response.status_code = HTTP_OK
        mock_response.json.return_value = JSON_DOC_SIMPLE
        mock_http_client.return_value.__aenter__.return_value.get.return_value = mock_response
        
        result = await extractor.get_document_content("doc123")
        
        assert result.status_code == HTTP_OK
        assert result.json()["title"] == TEST_DOC_TITLE
    
    @pytest.mark.asyncio
    async def test_is_project_public(self):
        """Test is_project_public method."""
        extractor = GoogleDocsExtractor([], TEST_TOKEN)
        
        result = await extractor.is_project_public()
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_validate_credentials_valid(self, test_docs_list):
        """Test credential validation with valid credentials."""
        extractor = GoogleDocsExtractor(test_docs_list, TEST_TOKEN)
        
        with patch.object(extractor, 'get_document_content') as mock_get_content:
            mock_response = Mock()
            mock_response.status_code = HTTP_OK
            mock_get_content.return_value = mock_response
            
            result = await extractor.validate_credentials()
            
            assert result["is_valid"] is True
    
    @pytest.mark.asyncio
    async def test_validate_credentials_invalid(self):
        """Test credential validation with invalid credentials."""
        extractor = GoogleDocsExtractor([], TEST_TOKEN)
        
        result = await extractor.validate_credentials()
        
        assert result["is_valid"] is False
        assert ERROR_NO_DOCUMENTS in result["message"]


class TestQuipExtractor:
    """Test cases for QuipExtractor class."""
    
    def test_init(self, test_quip_urls):
        """Test QuipExtractor initialization."""
        extractor = QuipExtractor(TEST_TOKEN, test_quip_urls, TEST_QUIP_MAX_DOCS)
        
        assert extractor.pat == TEST_TOKEN
        assert extractor.urls == test_quip_urls
        assert extractor.max_docs_per_kb == TEST_QUIP_MAX_DOCS
    
    @pytest.mark.asyncio
    async def test_context_manager(self):
        """Test QuipExtractor context manager."""
        extractor = QuipExtractor(TEST_TOKEN, [], TEST_QUIP_MAX_DOCS)
        
        async with extractor as ctx:
            assert ctx is extractor
    
    @pytest.mark.asyncio
    async def test_extract(self, test_quip_urls):
        """Test extract method."""
        extractor = QuipExtractor(TEST_TOKEN, test_quip_urls, TEST_QUIP_MAX_DOCS)
        
        with patch.object(extractor, '_extract_id_from_url', return_value=TEST_FOLDER_ID):
            with patch.object(extractor, '_process_url', return_value=None):
                result = await extractor.extract()
                
                assert isinstance(result, list)
    
    @pytest.mark.parametrize("url,expected_id", [
        (TEST_QUIP_EXTRACT_URL, TEST_QUIP_EXTRACT_ID),
        (TEST_QUIP_EXTRACT_TITLE_URL, TEST_QUIP_EXTRACT_ID),
        (TEST_QUIP_EMPTY_URL, ""),
    ])
    @pytest.mark.asyncio
    async def test_extract_id_from_url(self, url, expected_id):
        """Test _extract_id_from_url method with various URL formats."""
        extractor = QuipExtractor(TEST_TOKEN, [], TEST_QUIP_MAX_DOCS)
        
        result = await extractor._extract_id_from_url(url)
        assert result == expected_id
    
    @pytest.mark.asyncio
    async def test_get_item_type(self):
        """Test _get_item_type method."""
        extractor = QuipExtractor(TEST_TOKEN, [], TEST_QUIP_MAX_DOCS)
        
        with patch.object(extractor, 'client') as mock_client:
            mock_response = Mock()
            mock_response.status_code = HTTP_NOT_FOUND
            mock_response.text = MSG_NOT_FOUND
            
            mock_client.get.return_value = mock_response
            
            result = await extractor._get_item_type(TEST_THREAD_123)
            
            assert result is None
    
    @pytest.mark.asyncio
    async def test_get_folder_name(self):
        """Test _get_folder_name method."""
        extractor = QuipExtractor(TEST_TOKEN, [], TEST_QUIP_MAX_DOCS)
        
        with patch.object(extractor, 'client') as mock_client:
            mock_response = Mock()
            mock_response.status_code = HTTP_NOT_FOUND
            mock_client.get.return_value = mock_response
            
            result = await extractor._get_folder_name(TEST_FOLDER_123)
            
            assert result == TEST_QUIP_FOLDER_NAME
    
    @pytest.mark.asyncio
    async def test_get_thread_title(self):
        """Test _get_thread_title method."""
        extractor = QuipExtractor(TEST_TOKEN, [], TEST_QUIP_MAX_DOCS)
        
        with patch.object(extractor, 'client') as mock_client:
            mock_response = Mock()
            mock_response.status_code = HTTP_NOT_FOUND
            mock_client.get.return_value = mock_response
            
            result = await extractor._get_thread_title(TEST_THREAD_123)
            
            assert result == ""
    
    @pytest.mark.asyncio
    async def test_get_thread_content(self):
        """Test _get_thread_content method."""
        extractor = QuipExtractor(TEST_TOKEN, [], TEST_QUIP_MAX_DOCS)
        
        with patch.object(extractor, 'client') as mock_client:
            mock_response = Mock()
            mock_response.status_code = HTTP_NOT_FOUND
            mock_client.get.return_value = mock_response
            
            result = await extractor._get_thread_content(TEST_THREAD_123)
            
            assert result is None
    
    @pytest.mark.asyncio
    async def test_is_project_public(self):
        """Test is_project_public method."""
        extractor = QuipExtractor(TEST_TOKEN, [], TEST_QUIP_MAX_DOCS)
        
        result = await extractor.is_project_public()
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_validate_credentials_valid(self, mock_http_client):
        """Test credential validation with valid credentials."""
        extractor = QuipExtractor(TEST_TOKEN, [], TEST_QUIP_MAX_DOCS)
        
        mock_response = Mock()
        mock_response.status_code = HTTP_OK
        mock_response.json.return_value = JSON_USER_NAME
        mock_http_client.return_value.__aenter__.return_value.get.return_value = mock_response
        
        result = await extractor.validate_credentials()
        
        assert result["is_valid"] is True
        assert result["message"] == MSG_VALID_TOKEN
    
    @pytest.mark.asyncio
    async def test_validate_credentials_invalid(self, mock_http_client):
        """Test credential validation with invalid credentials."""
        extractor = QuipExtractor(TEST_TOKEN, [], TEST_QUIP_MAX_DOCS)
        
        mock_response = Mock()
        mock_response.status_code = HTTP_UNAUTHORIZED
        mock_http_client.return_value.__aenter__.return_value.get.return_value = mock_response
        
        result = await extractor.validate_credentials()
        
        assert result["is_valid"] is False
        assert ERROR_QUIP_AUTH_FAILED in result["message"]


class TestExtractorIntegration:
    """Integration tests for extractor classes."""
    
    @pytest.mark.parametrize("extractor_class,args", [
        (GitLabRepoExtractor, [TEST_GITLAB_URL, TEST_BRANCH, TEST_TOKEN]),
        (GitHubRepoExtractor, [TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN]),
        (AzureDevopsRepoExtractor, [TEST_AZURE_URL, TEST_BRANCH, TEST_TOKEN]),
        (GoogleDocsExtractor, [[], TEST_TOKEN]),
        (QuipExtractor, [TEST_TOKEN, [], TEST_QUIP_MAX_DOCS]),
    ])
    def test_all_extractors_implement_interface(self, extractor_class, args):
        """Test that all extractor classes implement the DataExtractor interface."""
        extractor = extractor_class(*args)
        
        assert isinstance(extractor, DataExtractor)
        assert hasattr(extractor, 'extract')
        assert hasattr(extractor, 'is_project_public')
        assert hasattr(extractor, 'validate_credentials')
    
    @pytest.mark.parametrize("extractor_class,args", [
        (GitLabRepoExtractor, [TEST_GITLAB_URL, TEST_BRANCH, TEST_TOKEN]),
        (GitHubRepoExtractor, [TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN]),
        (AzureDevopsRepoExtractor, [TEST_AZURE_URL, TEST_BRANCH, TEST_TOKEN]),
    ])
    @pytest.mark.asyncio
    async def test_git_extractors_inheritance(self, extractor_class, args):
        """Test that Git extractors properly inherit from GitRepoExtractor."""
        extractor = extractor_class(*args)
        
        assert isinstance(extractor, GitRepoExtractor)
        assert hasattr(extractor, '_validate_url')
        assert hasattr(extractor, '_get_clone_url')
        assert hasattr(extractor, 'clone_repo')
        assert hasattr(extractor, 'get_commit_sha')
        assert hasattr(extractor, 'get_git_blob_sha')
    
    def test_extractor_method_signatures(self):
        """Test that all extractors have consistent method signatures."""
        extractors = [
            GitLabRepoExtractor(TEST_GITLAB_URL, TEST_BRANCH, TEST_TOKEN),
            GitHubRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN),
            AzureDevopsRepoExtractor(TEST_AZURE_URL, TEST_BRANCH, TEST_TOKEN),
            GoogleDocsExtractor([], TEST_TOKEN),
            QuipExtractor(TEST_TOKEN, [], TEST_QUIP_MAX_DOCS)
        ]
        
        for extractor in extractors:
            # Check that all required methods exist and are callable
            assert callable(extractor.extract)
            assert callable(extractor.is_project_public)
            assert callable(extractor.validate_credentials)
    
    def test_git_extractor_common_attributes(self):
        """Test that Git extractors share common attributes."""
        git_extractors = [
            GitLabRepoExtractor(TEST_GITLAB_URL, TEST_BRANCH, TEST_TOKEN),
            GitHubRepoExtractor(TEST_GITHUB_URL, TEST_BRANCH, TEST_TOKEN),
            AzureDevopsRepoExtractor(TEST_AZURE_URL, TEST_BRANCH, TEST_TOKEN)
        ]
        
        for extractor in git_extractors:
            assert hasattr(extractor, 'repo_url')
            assert hasattr(extractor, 'branch_name')
            assert hasattr(extractor, 'pat')
            assert hasattr(extractor, 'clone_url')
            assert hasattr(extractor, '_url_validated') 