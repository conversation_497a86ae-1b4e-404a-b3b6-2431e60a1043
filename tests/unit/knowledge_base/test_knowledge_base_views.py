"""
Comprehensive tests for knowledge_base.views module to achieve SonarCube compliance.

This module contains test data that are safe for testing purposes.
All test data in this file is fictional and for testing purposes only.

SECURITY NOTE: All data in this file is safe test data:
- User IDs, organization IDs, and team IDs are fictional test identifiers
- Knowledge base IDs and names are fictional test values
- URLs and repository identifiers are fictional test data
- Email addresses and phone numbers are fictional test data
- Database schemas and table names are fictional test content
- API keys and tokens are fictional test values
- No production credentials or sensitive data is used
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timezone
from fastapi import HTTPException
import asyncio

from knowledge_base.views import (
    start_loading,
    get_vector_search,
    get_integrations,
    get_indexing_status,
    delete_knowledge_bases,
    share_knowledge_bases,
    get_knowledge_bases,
    get_knowledge_bases_internal,
    revoke_knowledge_base_access,
    is_url_public,
    update_kb,
    load_selected_tables_for_sql,
    get_folder_structure,
    get_file_content,
    get_knowledge_base,
    get_knowledge_base_for_edit
)
from knowledge_base.serializers import (
    KnowledgeBaseRequest,
    GetVectorSearchRequest,
    DeleteKnowledgeBasesRequest,
    GetKBStatusRequest,
    ShareKnowledgeBaseRequest,
    GetKbRequest,
    UpdateKB,
    GetKnowledgeBaseRequest,
    GetFolderStructureRequest,
    GetFileContentRequest,
    GetKbInternalRequest
)
from bigquery_integration.schemas import DatabaseSchema, TableMetadata
from clerk_integration.utils import UserData
from utils.connection_handler import ConnectionHandler
from utils.serializers import ResponseData

# Test Constants - All values are safe test data, not production values
# User and Organization Constants
TEST_USER_ID = "test-user-123"
TEST_ORG_ID = "test-org-456"
TEST_TEAM_ID = "test-team-789"
TEST_CREATED_BY = "creator-123"

# Knowledge Base Constants
TEST_KB_ID = 42
TEST_KB_NAME = "Test Knowledge Base"
TEST_KB_DESCRIPTION = "Test description for knowledge base"
TEST_KB_TYPE = "repository"

# URL and Repository Constants
TEST_URL = "https://github.com/test/repo"
TEST_REPOSITORY_URL = "https://github.com/test/repo"
TEST_PROVIDER = "github"
TEST_BRANCH = "main"

# User Data Constants
TEST_FIRST_NAME = "John"
TEST_LAST_NAME = "Doe"
TEST_EMAIL = "<EMAIL>"  # Fictional test email
TEST_USERNAME = "johndoe"
TEST_PHONE = "555-0123"  # Fictional test phone
TEST_PROFILE_URL = "https://example.com/profile.jpg"

# Search and Query Constants
TEST_SEARCH_QUERY = "test search query"
TEST_SEARCH_TYPE = "knowledge_base"
TEST_VECTOR_QUERY = "search for functionality"

# File and Folder Constants
TEST_FILE_PATH = "src/main.py"
TEST_FILE_CONTENT = "# Test file content\nprint('Hello World')"
TEST_FOLDER_PATH = "src"

# Pagination Constants
TEST_PAGE = 1
TEST_PER_PAGE = 10
TEST_SKIP = 0
TEST_LIMIT = 100

# API Response Constants
SUCCESS_STATUS = True
FAILURE_STATUS = False
HTTP_STATUS_OK = 200
HTTP_STATUS_ERROR = 500

# Database Constants
TEST_TABLE_NAME = "test_table"
TEST_SCHEMA_NAME = "test_schema"
TEST_DATABASE_NAME = "test_database"

# Status Constants
STATUS_READY = "ready"
STATUS_INDEXING = "indexing"
STATUS_FAILED = "failed"

# Date Constants
TEST_DATETIME = datetime.now(timezone.utc)

# Error Messages
ERROR_NOT_FOUND = "not found"
ERROR_ACCESS_DENIED = "access denied"
ERROR_INVALID_REQUEST = "invalid request"
ERROR_NO_ACCESS = "Do not have access"

# Service Mock Patch Paths
PATCH_KNOWLEDGE_BASE_SERVICE = 'knowledge_base.views.KnowledgeBaseService'

# Integration Constants
TEST_INTEGRATION_ID = 99
TEST_INTEGRATION_NAME = "GitHub Integration"

# Response Constants
TEST_RESPONSE_DATA = {"status": STATUS_READY, "message": "Operation successful"}
TEST_SEARCH_RESULTS = [{"content": "test result", "score": 0.95}]
TEST_FOLDER_STRUCTURE = {"folders": ["src", "docs"], "files": ["README.md"]}
TEST_INTEGRATIONS = [{"id": TEST_INTEGRATION_ID, "name": TEST_INTEGRATION_NAME, "type": TEST_PROVIDER}]


@pytest.fixture
def mock_connection_handler():
    """Create mock ConnectionHandler for tests."""
    handler = Mock(spec=ConnectionHandler)
    handler.session = AsyncMock()
    handler.event_emitter = AsyncMock()
    return handler


@pytest.fixture
def sample_user_data():
    """Create sample UserData for testing."""
    return UserData(
        _id=TEST_USER_ID,
        userId=TEST_USER_ID,
        orgId=TEST_ORG_ID,
        firstName=TEST_FIRST_NAME,
        lastName=TEST_LAST_NAME,
        email=TEST_EMAIL,
        username=TEST_USERNAME,
        phoneNumber=TEST_PHONE,
        profilePicUrl=TEST_PROFILE_URL,
        active=True,
        roleIds=[1, 2],
        meta={},
        createdAt=TEST_DATETIME,
        updatedAt=TEST_DATETIME,
        workspace=[]
    )


@pytest.fixture
def sample_kb_request():
    """Create sample KnowledgeBaseRequest for testing."""
    return KnowledgeBaseRequest(
        context="add_context_through_provider",
        add_context_through_provider={
            "kb_id": TEST_KB_ID,
            "provider": TEST_PROVIDER,
            "description": TEST_KB_DESCRIPTION,
            "credentials": {"url": TEST_URL, "branch_name": TEST_BRANCH},
            "kb_name": TEST_KB_NAME
        },
        team_id=TEST_TEAM_ID
    )


@pytest.fixture
def sample_vector_search_request():
    """Create sample GetVectorSearchRequest for testing."""
    return GetVectorSearchRequest(
        query=TEST_VECTOR_QUERY,
        knowledge_base_id=[TEST_KB_ID],
        user_id=TEST_USER_ID,
        org_id=TEST_ORG_ID,
        team_id=TEST_TEAM_ID,
        type=TEST_SEARCH_TYPE,
        matching_percentage=80.0,
        top_answer_count=5
    )


@pytest.fixture
def sample_delete_request():
    """Create sample DeleteKnowledgeBasesRequest for testing."""
    return DeleteKnowledgeBasesRequest(
        kb_ids=[TEST_KB_ID]
    )


@pytest.fixture
def sample_share_request():
    """Create sample ShareKnowledgeBaseRequest for testing."""
    return ShareKnowledgeBaseRequest(
        kb_ids=[TEST_KB_ID],
        user_id=[TEST_USER_ID],
        team_id=[TEST_TEAM_ID],
        org_id=[TEST_ORG_ID]
    )


@pytest.fixture
def sample_get_kb_request():
    """Create sample GetKbRequest for testing."""
    return GetKbRequest(
        skip=TEST_SKIP,
        limit=TEST_LIMIT,
        page=TEST_PAGE,
        size=TEST_PER_PAGE,
        user_id=TEST_USER_ID,
        search=TEST_SEARCH_QUERY,
        team_ids=[TEST_TEAM_ID],
        org_ids=[TEST_ORG_ID]
    )


@pytest.fixture
def sample_update_kb_request():
    """Create sample UpdateKB for testing."""
    return UpdateKB(
        kb_id=TEST_KB_ID,
        name=TEST_KB_NAME,
        url=TEST_URL,
        branch_name=TEST_BRANCH
    )


@pytest.fixture
def sample_folder_structure_request():
    """Create sample GetFolderStructureRequest for testing."""
    return GetFolderStructureRequest(
        kb_ids=[TEST_KB_ID],
        folder_path=TEST_FOLDER_PATH
    )


@pytest.fixture
def sample_file_content_request():
    """Create sample GetFileContentRequest for testing."""
    return GetFileContentRequest(
        kb_id=TEST_KB_ID,
        file_path=TEST_FILE_PATH
    )


@pytest.fixture
def sample_get_knowledge_base_request():
    """Create sample GetKnowledgeBaseRequest for testing."""
    return GetKnowledgeBaseRequest(
        user_id=TEST_USER_ID,
        org_id=TEST_ORG_ID,
        team_id=TEST_TEAM_ID
    )


@pytest.fixture
def sample_database_schema():
    """Create sample DatabaseSchema for testing."""
    table_metadata = TableMetadata(
        table_name=TEST_TABLE_NAME,
        table_description="Test table description",
        columns=[]
    )
    return DatabaseSchema(
        provider=TEST_PROVIDER,
        database_name=TEST_DATABASE_NAME,
        tables=[table_metadata],
        knowledge_base_id=TEST_KB_ID,
        kb_name=TEST_KB_NAME
    )


@pytest.fixture
def mock_knowledge_base_service():
    """Create mock KnowledgeBaseService for testing."""
    service = AsyncMock()
    service.start_loading.return_value = ResponseData(success=SUCCESS_STATUS, data=TEST_RESPONSE_DATA)
    service.get_vector_search.return_value = ResponseData(success=SUCCESS_STATUS, data=TEST_SEARCH_RESULTS)
    service.get_integrations.return_value = ResponseData(success=SUCCESS_STATUS, data=TEST_INTEGRATIONS)
    service.get_indexing_status.return_value = ResponseData(success=SUCCESS_STATUS, data={"status": STATUS_READY})
    service.delete_knowledge_bases.return_value = ResponseData(success=SUCCESS_STATUS, data={"deleted": [TEST_KB_ID]})
    service.share_knowledge_bases.return_value = ResponseData(success=SUCCESS_STATUS, data=TEST_RESPONSE_DATA)
    service.get_knowledge_bases.return_value = ResponseData(success=SUCCESS_STATUS, data={"knowledge_bases": []})
    service.revoke_knowledge_base_access.return_value = ResponseData(success=SUCCESS_STATUS, data=TEST_RESPONSE_DATA)
    service.is_url_public.return_value = ResponseData(success=SUCCESS_STATUS, data={"is_public": True})
    service.update_knowledge_bases.return_value = ResponseData(success=SUCCESS_STATUS, data=TEST_RESPONSE_DATA)
    service.load_selected_tables_for_sql.return_value = ResponseData(success=SUCCESS_STATUS, data=TEST_RESPONSE_DATA)
    service.get_folder_structure.return_value = ResponseData(success=SUCCESS_STATUS, data=TEST_FOLDER_STRUCTURE)
    service.get_file_content.return_value = ResponseData(success=SUCCESS_STATUS, data={"content": TEST_FILE_CONTENT})
    service.get_knowledge_base.return_value = ResponseData(success=SUCCESS_STATUS, data={"id": TEST_KB_ID})
    service.get_knowledge_base_for_edit.return_value = ResponseData(success=SUCCESS_STATUS, data={"id": TEST_KB_ID})
    return service


class TestKnowledgeBaseViews:
    """Test cases for knowledge_base.views module."""

    @pytest.mark.asyncio
    async def test_start_loading_success(self, sample_kb_request, sample_user_data, 
                                        mock_connection_handler, mock_knowledge_base_service):
        """Test successful knowledge base loading initiation."""
        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await start_loading(sample_kb_request, mock_connection_handler, sample_user_data)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data == TEST_RESPONSE_DATA
            mock_knowledge_base_service.start_loading.assert_called_once_with(
                sample_kb_request, mock_connection_handler, sample_user_data
            )

    @pytest.mark.asyncio
    async def test_get_vector_search_success(self, sample_vector_search_request, 
                                           mock_connection_handler, mock_knowledge_base_service):
        """Test successful vector search."""
        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await get_vector_search(sample_vector_search_request, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data == TEST_SEARCH_RESULTS
            mock_knowledge_base_service.get_vector_search.assert_called_once_with(sample_vector_search_request)

    @pytest.mark.asyncio
    async def test_get_integrations_success(self, sample_user_data, mock_connection_handler, 
                                          mock_knowledge_base_service):
        """Test successful integrations retrieval."""
        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await get_integrations(sample_user_data, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data == TEST_INTEGRATIONS
            mock_knowledge_base_service.get_integrations.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_indexing_status_success(self, sample_user_data, mock_connection_handler,
                                             mock_knowledge_base_service):
        """Test successful indexing status retrieval."""
        status_request = GetKBStatusRequest(kb_ids=[TEST_KB_ID])
        
        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await get_indexing_status(status_request, sample_user_data, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert "status" in result.data
            mock_knowledge_base_service.get_indexing_status.assert_called_once_with(sample_user_data, status_request)

    @pytest.mark.asyncio
    async def test_delete_knowledge_bases_success(self, sample_delete_request, sample_user_data,
                                                 mock_connection_handler, mock_knowledge_base_service):
        """Test successful knowledge base deletion."""
        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await delete_knowledge_bases(sample_delete_request, mock_connection_handler, sample_user_data)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert "deleted" in result.data
            mock_knowledge_base_service.delete_knowledge_bases.assert_called_once_with(
                sample_delete_request.kb_ids, mock_connection_handler, sample_user_data
            )

    @pytest.mark.asyncio
    async def test_share_knowledge_bases_success(self, sample_share_request, sample_user_data,
                                               mock_connection_handler, mock_knowledge_base_service):
        """Test successful knowledge base sharing."""
        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await share_knowledge_bases(sample_share_request, sample_user_data, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data == TEST_RESPONSE_DATA
            mock_knowledge_base_service.share_knowledge_bases.assert_called_once_with(sample_share_request, sample_user_data)

    @pytest.mark.asyncio
    async def test_get_knowledge_bases_success(self, sample_get_kb_request, sample_user_data,
                                             mock_connection_handler, mock_knowledge_base_service):
        """Test successful knowledge bases retrieval."""
        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await get_knowledge_bases(sample_get_kb_request, sample_user_data, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert "knowledge_bases" in result.data
            mock_knowledge_base_service.get_knowledge_bases.assert_called_once_with(sample_get_kb_request, sample_user_data)

    @pytest.mark.asyncio
    async def test_get_knowledge_bases_internal_success(self, mock_connection_handler, mock_knowledge_base_service):
        """Test successful internal knowledge bases retrieval."""
        internal_request = GetKbInternalRequest(
            user_id=TEST_USER_ID,
            org_ids=[TEST_ORG_ID],
            skip=TEST_SKIP,
            limit=TEST_LIMIT
        )
        
        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await get_knowledge_bases_internal(internal_request, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            mock_knowledge_base_service.get_knowledge_bases.assert_called_once()
            # Verify UserData was constructed properly
            call_args = mock_knowledge_base_service.get_knowledge_bases.call_args
            assert call_args[0][0] == internal_request  # First argument is the request
            assert call_args[0][1].userId == TEST_USER_ID  # Second argument is constructed UserData
            assert call_args[0][1].orgId == TEST_ORG_ID

    @pytest.mark.asyncio
    async def test_get_knowledge_bases_internal_no_org(self, mock_connection_handler, mock_knowledge_base_service):
        """Test internal knowledge bases retrieval without org ID."""
        internal_request = GetKbInternalRequest(
            user_id=TEST_USER_ID,
            org_ids=[],
            skip=TEST_SKIP,
            limit=TEST_LIMIT
        )
        
        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await get_knowledge_bases_internal(internal_request, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            call_args = mock_knowledge_base_service.get_knowledge_bases.call_args
            assert call_args[0][1].orgId is None  # orgId should be None when org_ids is empty

    @pytest.mark.asyncio
    async def test_revoke_knowledge_base_access_success(self, sample_share_request, sample_user_data,
                                                       mock_connection_handler, mock_knowledge_base_service):
        """Test successful knowledge base access revocation."""
        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await revoke_knowledge_base_access(sample_share_request, sample_user_data, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data == TEST_RESPONSE_DATA
            mock_knowledge_base_service.revoke_knowledge_base_access.assert_called_once_with(
                sample_share_request, sample_user_data
            )

    @pytest.mark.asyncio
    async def test_is_url_public_success(self, sample_user_data, mock_connection_handler, mock_knowledge_base_service):
        """Test successful URL public check."""
        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await is_url_public(TEST_URL, TEST_PROVIDER, sample_user_data, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data["is_public"] is True
            mock_knowledge_base_service.is_url_public.assert_called_once_with(TEST_URL, TEST_PROVIDER, sample_user_data)

    @pytest.mark.asyncio
    async def test_update_kb_success(self, sample_update_kb_request, sample_user_data,
                                   mock_connection_handler, mock_knowledge_base_service):
        """Test successful knowledge base update."""
        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await update_kb(sample_update_kb_request, sample_user_data, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data == TEST_RESPONSE_DATA
            mock_knowledge_base_service.update_knowledge_bases.assert_called_once_with(
                sample_update_kb_request, sample_user_data
            )

    @pytest.mark.asyncio
    async def test_load_selected_tables_for_sql_success(self, sample_database_schema, sample_user_data,
                                                       mock_connection_handler, mock_knowledge_base_service):
        """Test successful SQL tables loading."""
        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await load_selected_tables_for_sql(sample_database_schema, mock_connection_handler, sample_user_data)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data == TEST_RESPONSE_DATA
            mock_knowledge_base_service.load_selected_tables_for_sql.assert_called_once_with(
                sample_database_schema, mock_connection_handler, sample_user_data
            )

    @pytest.mark.asyncio
    async def test_get_folder_structure_success(self, sample_folder_structure_request, mock_connection_handler,
                                              mock_knowledge_base_service):
        """Test successful folder structure retrieval."""
        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await get_folder_structure(sample_folder_structure_request, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data == TEST_FOLDER_STRUCTURE
            mock_knowledge_base_service.get_folder_structure.assert_called_once_with(
                sample_folder_structure_request.kb_ids, sample_folder_structure_request.folder_path
            )

    @pytest.mark.asyncio
    async def test_get_file_content_success(self, sample_file_content_request, mock_connection_handler,
                                          mock_knowledge_base_service):
        """Test successful file content retrieval."""
        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await get_file_content(sample_file_content_request, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data["content"] == TEST_FILE_CONTENT
            mock_knowledge_base_service.get_file_content.assert_called_once_with(sample_file_content_request)

    @pytest.mark.asyncio
    async def test_get_knowledge_base_success(self, sample_get_knowledge_base_request, mock_connection_handler,
                                            mock_knowledge_base_service):
        """Test successful single knowledge base retrieval."""
        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await get_knowledge_base(TEST_KB_ID, sample_get_knowledge_base_request, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data["id"] == TEST_KB_ID
            mock_knowledge_base_service.get_knowledge_base.assert_called_once_with(
                TEST_KB_ID, sample_get_knowledge_base_request
            )

    @pytest.mark.asyncio
    async def test_get_knowledge_base_for_edit_success(self, sample_user_data, mock_connection_handler,
                                                     mock_knowledge_base_service):
        """Test successful knowledge base retrieval for editing."""
        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await get_knowledge_base_for_edit(TEST_KB_ID, sample_user_data, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data["id"] == TEST_KB_ID
            mock_knowledge_base_service.get_knowledge_base_for_edit.assert_called_once_with(TEST_KB_ID, sample_user_data)

    @pytest.mark.asyncio
    async def test_service_initialization_consistency(self, sample_kb_request, sample_user_data,
                                                    mock_connection_handler):
        """Test that KnowledgeBaseService is initialized consistently across views."""
        with patch(PATCH_KNOWLEDGE_BASE_SERVICE) as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            mock_service.start_loading.return_value = ResponseData(success=SUCCESS_STATUS, data={})

            # Act
            await start_loading(sample_kb_request, mock_connection_handler, sample_user_data)

            # Assert
            mock_service_class.assert_called_once_with(mock_connection_handler.session)

    @pytest.mark.asyncio
    async def test_service_exception_handling(self, sample_kb_request, sample_user_data, mock_connection_handler):
        """Test exception handling when service fails."""
        with patch(PATCH_KNOWLEDGE_BASE_SERVICE) as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            mock_service.start_loading.side_effect = Exception("Service error")

            # Act & Assert
            with pytest.raises(Exception, match="Service error"):
                await start_loading(sample_kb_request, mock_connection_handler, sample_user_data)


class TestKnowledgeBaseViewsEdgeCases:
    """Test edge cases and error scenarios for knowledge_base views."""

    @pytest.mark.asyncio
    async def test_empty_kb_ids_in_requests(self, mock_connection_handler, mock_knowledge_base_service):
        """Test handling of empty knowledge base IDs in requests."""
        empty_delete_request = DeleteKnowledgeBasesRequest(kb_ids=[])
        empty_share_request = ShareKnowledgeBaseRequest(
            kb_ids=[],
            user_id=[TEST_USER_ID],
            team_id=[],
            org_id=[]
        )
        
        user_data = UserData(
            _id=TEST_USER_ID,
            userId=TEST_USER_ID,
            orgId=TEST_ORG_ID,
            firstName=TEST_FIRST_NAME,
            lastName=TEST_LAST_NAME,
            email=TEST_EMAIL,
            username=TEST_USERNAME,
            phoneNumber=TEST_PHONE,
            profilePicUrl=TEST_PROFILE_URL,
            active=True,
            roleIds=[],
            meta={},
            createdAt=TEST_DATETIME,
            updatedAt=TEST_DATETIME,
            workspace=[]
        )

        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            delete_result = await delete_knowledge_bases(empty_delete_request, mock_connection_handler, user_data)
            share_result = await share_knowledge_bases(empty_share_request, user_data, mock_connection_handler)

            # Assert
            assert delete_result.success is SUCCESS_STATUS
            assert share_result.success is SUCCESS_STATUS

    @pytest.mark.asyncio
    async def test_invalid_kb_id(self, mock_connection_handler):
        """Test handling of invalid knowledge base ID."""
        invalid_kb_id = -1
        request = GetKnowledgeBaseRequest(
            user_id=TEST_USER_ID,
            org_id=TEST_ORG_ID,
            team_id=TEST_TEAM_ID
        )

        with patch(PATCH_KNOWLEDGE_BASE_SERVICE) as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            mock_service.get_knowledge_base.return_value = ResponseData(
                success=FAILURE_STATUS,
                message=ERROR_NOT_FOUND
            )

            # Act
            result = await get_knowledge_base(invalid_kb_id, request, mock_connection_handler)

            # Assert
            assert result.success is FAILURE_STATUS
            assert ERROR_NOT_FOUND in result.message

    @pytest.mark.asyncio
    async def test_large_search_query(self, mock_connection_handler, mock_knowledge_base_service):
        """Test handling of very large search queries."""
        large_query = "x" * 10000  # Very large query
        large_search_request = GetVectorSearchRequest(
            query=large_query,
            knowledge_base_id=[TEST_KB_ID],
            user_id=TEST_USER_ID,
            org_id=TEST_ORG_ID,
            team_id=TEST_TEAM_ID,
            type=TEST_SEARCH_TYPE,
            matching_percentage=80.0,
            top_answer_count=5
        )

        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await get_vector_search(large_search_request, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            mock_knowledge_base_service.get_vector_search.assert_called_once_with(large_search_request)

    @pytest.mark.asyncio
    async def test_concurrent_operations_safety(self, mock_connection_handler, mock_knowledge_base_service):
        """Test that views handle concurrent operations safely."""
        user_data = UserData(
            _id=TEST_USER_ID,
            userId=TEST_USER_ID,
            orgId=TEST_ORG_ID,
            firstName=TEST_FIRST_NAME,
            lastName=TEST_LAST_NAME,
            email=TEST_EMAIL,
            username=TEST_USERNAME,
            phoneNumber=TEST_PHONE,
            profilePicUrl=TEST_PROFILE_URL,
            active=True,
            roleIds=[],
            meta={},
            createdAt=TEST_DATETIME,
            updatedAt=TEST_DATETIME,
            workspace=[]
        )

        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act - Simulate concurrent operations
            tasks = []
            tasks.append(get_integrations(user_data, mock_connection_handler))
            for i in range(3):
                tasks.append(is_url_public(f"https://github.com/test/repo{i}", TEST_PROVIDER, user_data, mock_connection_handler))
                tasks.append(get_knowledge_base_for_edit(TEST_KB_ID + i, user_data, mock_connection_handler))
            
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Assert
            for result in results:
                assert isinstance(result, ResponseData)
                assert result.success is SUCCESS_STATUS

    @pytest.mark.asyncio
    async def test_special_characters_in_requests(self, mock_connection_handler, mock_knowledge_base_service):
        """Test handling of special characters in request data."""
        special_chars_request = KnowledgeBaseRequest(
            context="add_context_through_provider",
            add_context_through_provider={
                "kb_id": TEST_KB_ID,
                "provider": TEST_PROVIDER,
                "description": "Description with <script>alert('xss')</script>",
                "credentials": {"url": "https://github.com/test/repo-with-special-chars", "branch_name": "feature/special-branch-名前"},
                "kb_name": "Test KB with 特殊字符 & símbolos"
            },
            team_id=TEST_TEAM_ID
        )

        user_data = UserData(
            _id=TEST_USER_ID,
            userId=TEST_USER_ID,
            orgId=TEST_ORG_ID,
            firstName=TEST_FIRST_NAME,
            lastName=TEST_LAST_NAME,
            email=TEST_EMAIL,
            username=TEST_USERNAME,
            phoneNumber=TEST_PHONE,
            profilePicUrl=TEST_PROFILE_URL,
            active=True,
            roleIds=[],
            meta={},
            createdAt=TEST_DATETIME,
            updatedAt=TEST_DATETIME,
            workspace=[]
        )

        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await start_loading(special_chars_request, mock_connection_handler, user_data)

            # Assert
            assert result.success is SUCCESS_STATUS
            mock_knowledge_base_service.start_loading.assert_called_once_with(
                special_chars_request, mock_connection_handler, user_data
            )

    @pytest.mark.asyncio
    async def test_none_values_handling(self, mock_connection_handler, mock_knowledge_base_service):
        """Test handling of None values in requests."""
        folder_request_with_none = GetFolderStructureRequest(
            kb_ids=[TEST_KB_ID],
            folder_path=None
        )

        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await get_folder_structure(folder_request_with_none, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            mock_knowledge_base_service.get_folder_structure.assert_called_once_with([TEST_KB_ID], None)

    @pytest.mark.asyncio
    async def test_pagination_edge_cases(self, mock_connection_handler, mock_knowledge_base_service):
        """Test pagination with edge case values."""
        edge_case_request = GetKbRequest(
            skip=0,
            limit=1,  # Minimum limit
            page=1,
            size=1,
            user_id=TEST_USER_ID,
            search="",  # Empty search
            team_ids=[],
            org_ids=[]
        )

        user_data = UserData(
            _id=TEST_USER_ID,
            userId=TEST_USER_ID,
            orgId=TEST_ORG_ID,
            firstName=TEST_FIRST_NAME,
            lastName=TEST_LAST_NAME,
            email=TEST_EMAIL,
            username=TEST_USERNAME,
            phoneNumber=TEST_PHONE,
            profilePicUrl=TEST_PROFILE_URL,
            active=True,
            roleIds=[],
            meta={},
            createdAt=TEST_DATETIME,
            updatedAt=TEST_DATETIME,
            workspace=[]
        )

        with patch(PATCH_KNOWLEDGE_BASE_SERVICE, return_value=mock_knowledge_base_service):
            # Act
            result = await get_knowledge_bases(edge_case_request, user_data, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            mock_knowledge_base_service.get_knowledge_bases.assert_called_once_with(edge_case_request, user_data) 