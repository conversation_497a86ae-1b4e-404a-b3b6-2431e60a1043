"""
Comprehensive tests for knowledge_base consumer utilities.

This module contains test data that are safe for testing purposes.
All test data in this file is fictional and for testing purposes only.

SECURITY NOTE: All data in this file is safe test data:
- IDs, UUIDs, and identifiers are fictional test values
- Email addresses are fictional test data
- Names and user data are fictional test values
- URLs and paths are test-only values
- No production credentials or sensitive data is used
"""
import pytest

from knowledge_base.consumer import (
    prepare_source_item,
    build_folder_structure_from_paths,
)
from knowledge_base.locksmith_calls import _call_grant_data_source_access
from clerk_integration.utils import UserData  # Stubbed in tests/conftest.py


# Test Constants - All values are safe test data, not production values
TEST_KB_ID = 99
TEST_PROVIDER_ITEM_ID = "f-123"
TEST_UUID = "uuid-1"
TEST_VERSION_TAG = "abcd"
TEST_CHECKSUM = "sha1"
TEST_PATH_MAIN = "src/main.py"
TEST_PATH_HELPERS = "src/utils/helpers.py"
TEST_PATH_README = "README.md"
TEST_KIND_FILE = "file"
TEST_TEAM_ID = None
TEST_USER_ID = "u-1"
TEST_ORG_ID = "o-1"
TEST_FIRST_NAME = "Foo"
TEST_LAST_NAME = "Bar"
TEST_EMAIL = "<EMAIL>"  # Fictional test email
HTTP_STATUS_OK = 200


@pytest.fixture
def sample_record():
    """Create sample record for testing."""
    return {
        "provider_item_id": TEST_PROVIDER_ITEM_ID,
        "kind": TEST_KIND_FILE,
        "path": TEST_PATH_MAIN,
        "version_tag": TEST_VERSION_TAG,
        "checksum": TEST_CHECKSUM,
        "uuid": TEST_UUID,
    }


@pytest.fixture
def expected_source_item():
    """Create expected source item result."""
    return {
        "kb_id": TEST_KB_ID,
        "provider_item_id": TEST_PROVIDER_ITEM_ID,
        "kind": TEST_KIND_FILE,
        "logical_path": TEST_PATH_MAIN,
        "version_tag": TEST_VERSION_TAG,
        "checksum": TEST_CHECKSUM,
        "metadata_json": {},
        "id": TEST_UUID,
    }


@pytest.fixture
def sample_files():
    """Create sample files for folder structure testing."""
    return [
        {"path": TEST_PATH_HELPERS},
        {"path": TEST_PATH_MAIN},
        {"path": TEST_PATH_README},
    ]


@pytest.fixture
def user_data():
    """Create test user data."""
    return UserData(
        _id=TEST_USER_ID,
        userId=TEST_USER_ID,
        orgId=TEST_ORG_ID,
        firstName=TEST_FIRST_NAME,
        lastName=TEST_LAST_NAME,
        email=TEST_EMAIL,
    )


class TestPrepareSourceItem:
    """Test cases for prepare_source_item function."""

    @pytest.mark.asyncio
    async def test_prepare_source_item_formats_record(self, sample_record, expected_source_item):
        """Test that prepare_source_item correctly formats a record."""
        result = await prepare_source_item(sample_record, knowledge_base_id=TEST_KB_ID)

        assert result == expected_source_item

    @pytest.mark.asyncio
    async def test_prepare_source_item_with_different_kb_id(self, sample_record):
        """Test prepare_source_item with different knowledge base ID."""
        different_kb_id = 456
        result = await prepare_source_item(sample_record, knowledge_base_id=different_kb_id)

        assert result["kb_id"] == different_kb_id
        assert result["provider_item_id"] == TEST_PROVIDER_ITEM_ID

    @pytest.mark.asyncio
    async def test_prepare_source_item_preserves_all_fields(self, sample_record):
        """Test that all fields are preserved in the transformation."""
        result = await prepare_source_item(sample_record, knowledge_base_id=TEST_KB_ID)

        # Check that all expected fields are present
        expected_fields = [
            "kb_id", "provider_item_id", "kind", "logical_path", 
            "version_tag", "checksum", "metadata_json", "id"
        ]
        for field in expected_fields:
            assert field in result


class TestBuildFolderStructureFromPaths:
    """Test cases for build_folder_structure_from_paths function."""

    @pytest.mark.asyncio
    async def test_build_folder_structure_from_paths_nested(self, sample_files):
        """Test building nested folder structure from file paths."""
        structure = await build_folder_structure_from_paths(sample_files)

        # The structure is nested under root "" key when consumed elsewhere, but here we
        # only care about the inner representation.
        assert structure["src"]["utils"] == {"helpers.py": None}
        assert structure["src"]["main.py"] is None
        assert structure[TEST_PATH_README] is None

    @pytest.mark.asyncio
    async def test_build_folder_structure_empty_files(self):
        """Test building folder structure with empty files list."""
        structure = await build_folder_structure_from_paths([])

        assert structure == {}

    @pytest.mark.asyncio
    async def test_build_folder_structure_single_file(self):
        """Test building folder structure with single file."""
        files = [{"path": TEST_PATH_README}]
        structure = await build_folder_structure_from_paths(files)

        assert structure[TEST_PATH_README] is None

    @pytest.mark.asyncio
    async def test_build_folder_structure_deep_nesting(self):
        """Test building folder structure with deep nesting."""
        files = [{"path": "a/b/c/d/file.txt"}]
        structure = await build_folder_structure_from_paths(files)

        assert structure["a"]["b"]["c"]["d"]["file.txt"] is None


class TestCallGrantDataSourceAccess:
    """Test cases for _call_grant_data_source_access function."""

    @pytest.fixture
    def mock_client_success(self):
        """Create mock client that returns successful response."""
        class MockResponse:
            """Mock HTTP response."""
            status_code = HTTP_STATUS_OK

            @staticmethod
            def json():
                return {"ok": True}

        class MockClient:
            """Mock HTTP client."""
            async def __aenter__(self):
                return self

            async def __aexit__(self, exc_type, exc_val, exc_tb):
                return False

            async def post(self, url, json, headers):  # pylint: disable=unused-argument
                return MockResponse()

        return MockClient

    @pytest.mark.asyncio
    async def test_call_grant_data_source_access_success(self, monkeypatch, user_data, mock_client_success):
        """Test successful grant data source access call."""
        # Patch the AsyncClient inside this module only (not globally)
        monkeypatch.setattr(
            "knowledge_base.locksmith_calls.httpx.AsyncClient", 
            lambda: mock_client_success()
        )

        result = await _call_grant_data_source_access(
            team_id=TEST_TEAM_ID,
            knowledge_base_id=TEST_KB_ID,
            user_data=user_data,
        )

        assert result == {"ok": True}

    @pytest.mark.asyncio
    async def test_call_grant_data_source_access_with_different_kb_id(self, monkeypatch, user_data, mock_client_success):
        """Test grant data source access with different knowledge base ID."""
        different_kb_id = 999
        monkeypatch.setattr(
            "knowledge_base.locksmith_calls.httpx.AsyncClient", 
            lambda: mock_client_success()
        )

        result = await _call_grant_data_source_access(
            team_id=TEST_TEAM_ID,
            knowledge_base_id=different_kb_id,
            user_data=user_data,
        )

        assert result == {"ok": True}

    @pytest.fixture
    def mock_client_error(self):
        """Create mock client that returns error response."""
        class MockErrorResponse:
            """Mock HTTP error response."""
            status_code = 500
            text = "Internal server error"

            @staticmethod
            def json():
                return {"error": "Internal server error"}

        class MockErrorClient:
            """Mock HTTP client that returns errors."""
            async def __aenter__(self):
                return self

            async def __aexit__(self, exc_type, exc_val, exc_tb):
                return False

            async def post(self, url, json, headers):  # pylint: disable=unused-argument
                return MockErrorResponse()

        return MockErrorClient

    @pytest.mark.asyncio
    async def test_call_grant_data_source_access_error_response(self, monkeypatch, user_data, mock_client_error):
        """Test grant data source access with error response."""
        from utils.exceptions import ApiException
        
        monkeypatch.setattr(
            "knowledge_base.locksmith_calls.httpx.AsyncClient", 
            lambda: mock_client_error()
        )

        with pytest.raises(ApiException) as exc_info:
            await _call_grant_data_source_access(
                team_id=TEST_TEAM_ID,
                knowledge_base_id=TEST_KB_ID,
                user_data=user_data,
            )
        
        # Verify the exception message contains the expected error details
        assert "Failed with status 500" in str(exc_info.value)
        assert "Internal server error" in str(exc_info.value)


class TestConsumerUtilsIntegration:
    """Integration tests for consumer utilities."""

    @pytest.mark.asyncio
    async def test_prepare_source_item_and_build_folder_structure_integration(self):
        """Test integration between prepare_source_item and build_folder_structure_from_paths."""
        # Prepare source items
        records = [
            {
                "provider_item_id": "f-1",
                "kind": TEST_KIND_FILE,
                "path": TEST_PATH_MAIN,
                "version_tag": TEST_VERSION_TAG,
                "checksum": TEST_CHECKSUM,
                "uuid": "uuid-main",
            },
            {
                "provider_item_id": "f-2",
                "kind": TEST_KIND_FILE,
                "path": TEST_PATH_HELPERS,
                "version_tag": TEST_VERSION_TAG,
                "checksum": TEST_CHECKSUM,
                "uuid": "uuid-helpers",
            }
        ]

        # Convert to source items
        source_items = []
        for record in records:
            source_item = await prepare_source_item(record, knowledge_base_id=TEST_KB_ID)
            source_items.append({"path": source_item["logical_path"]})

        # Build folder structure
        structure = await build_folder_structure_from_paths(source_items)

        # Verify the structure
        assert "src" in structure
        assert "main.py" in structure["src"]
        assert "utils" in structure["src"]
        assert "helpers.py" in structure["src"]["utils"]

    @pytest.mark.asyncio
    async def test_functions_handle_empty_inputs(self):
        """Test that functions handle empty inputs gracefully."""
        # Test prepare_source_item with minimal record
        minimal_record = {
            "provider_item_id": "test-id",
            "kind": "file",
            "path": "test.txt",
            "version_tag": "",
            "checksum": "",
            "uuid": "test-uuid",
        }
        
        result = await prepare_source_item(minimal_record, knowledge_base_id=1)
        assert result["kb_id"] == 1
        assert result["provider_item_id"] == "test-id"

        # Test build_folder_structure_from_paths with empty list
        structure = await build_folder_structure_from_paths([])
        assert structure == {} 