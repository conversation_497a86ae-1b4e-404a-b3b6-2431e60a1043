"""
Comprehensive tests for knowledge_base.service module to achieve SonarCube compliance.

This module contains test data that are safe for testing purposes.
All test data in this file is fictional and for testing purposes only.

SECURITY NOTE: All data in this file is safe test data:
- User IDs, organization IDs, and team IDs are fictional test identifiers
- Knowledge base IDs and names are fictional test values
- URLs and repository identifiers are fictional test data
- Email addresses and phone numbers are fictional test data
- No production credentials or sensitive data is used
"""
import asyncio
import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession

from knowledge_base.service import KnowledgeBaseService
from knowledge_base.serializers import (
    GetKbRequest, GetKnowledgeBaseRequest, 
    UpdateKB, GetVectorSearchRequest, ShareKnowledgeBaseRequest
)
from knowledge_base.models import KnowledgeBase, KBState, Integration, IntegrationType
from clerk_integration.utils import UserData
from utils.serializers import ResponseData
from utils.exceptions import CustomException, ApiException
from utils.connection_handler import ConnectionHandler

# Test Constants - All values are safe test data, not production values
# User and Organization Constants
TEST_USER_ID = "test-user-123"
TEST_ORG_ID = "test-org-456" 
TEST_TEAM_ID = "test-team-789"
TEST_CREATED_BY = "creator-123"

# Knowledge Base Constants
TEST_KB_ID = 42
TEST_KB_NAME = "Test Knowledge Base"
TEST_KB_DESCRIPTION = "Test description for knowledge base"
TEST_KB_TYPE = "repository"

# URL Constants
TEST_SOURCE_IDENTIFIER = "https://github.com/test/repo"
TEST_URL = "https://github.com/test/repo"

# User Data Constants
TEST_FIRST_NAME = "John"
TEST_LAST_NAME = "Doe"
TEST_EMAIL = "<EMAIL>"  # Fictional test email
TEST_USERNAME = "johndoe"
TEST_PHONE = "555-0123"  # Fictional test phone
TEST_PROFILE_URL = "https://example.com/profile.jpg"

# Pagination Constants
TEST_PAGE = 1
TEST_PER_PAGE = 10
TEST_SKIP = 0
TEST_LIMIT = 100
TEST_TOTAL = 1
TEST_PAGES = 1

# API Response Constants
SUCCESS_STATUS = True
FAILURE_STATUS = False
HTTP_STATUS_OK = 200
HTTP_STATUS_ERROR = 500

# State Constants
STATE_READY = "ready"
STATE_INDEXING = "indexing"
STATE_UPDATING = "updating"

# Settings and Credentials Constants
TEST_BRANCH = "main"
TEST_SETTINGS = {"branch": TEST_BRANCH, "provider": "github"}
TEST_MEMORY = {"folder_structure": {"src": {"main.py": None}}}

# Search Constants
TEST_SEARCH_QUERY = "test query"
TEST_SEARCH_TYPE = "knowledge_base"
TEST_INDEX_NAME = "knowledge_base_index"

# Integration Constants
TEST_INTEGRATION_ID = 99
TEST_INTEGRATION_NAME = "GitHub Integration"
INTEGRATION_TYPE_GITHUB = IntegrationType.github

# Provider Constants
TEST_PROVIDER = "github"
TEST_FOLDER_PATH = "src"

# Date Constants
TEST_DATETIME = datetime.now(timezone.utc)

# Error Messages
ERROR_NO_ACCESS = "Do not have access"
ERROR_NOT_FOUND = "not found"
ERROR_FAILED_TO_FETCH = "Failed to fetch"

# Patch Path Constants - Service Patch Paths
PATCH_INTEGRATION_DAO = 'knowledge_base.service.IntegrationDao'
PATCH_KNOWLEDGE_BASE_DAO = 'knowledge_base.service.KnowledgeBaseDao'
PATCH_INGESTION_RUN_DAO = 'knowledge_base.service.IngestionRunDao'
PATCH_ELASTIC_SEARCH_ADAPTER = 'knowledge_base.service.ElasticSearchAdapter'
PATCH_ELASTICSEARCH_SQL_ADAPTER = 'knowledge_base.service.ElasticsearchSQLAdapter'

# Service Function Patch Paths
PATCH_CHECK_DATA_SOURCE_ACCESS = 'knowledge_base.service._call_check_data_source_access'
PATCH_GET_ACCESSIBLE_DATASOURCES = 'knowledge_base.service._call_get_accessible_datasources_by_user'
PATCH_KB_FACTORY_GET_EXTRACTOR = 'knowledge_base.service.KnowledgeBaseFactory.get_extractor'


@pytest.fixture
def mock_session():
    """Create mock AsyncSession for all tests."""
    return AsyncMock(spec=AsyncSession)


@pytest.fixture
def mock_connection_handler():
    """Create mock ConnectionHandler for tests."""
    handler = Mock(spec=ConnectionHandler)
    handler.session = AsyncMock()
    handler.event_emitter = AsyncMock()
    return handler


@pytest.fixture
def knowledge_base_service(mock_session):
    """Create KnowledgeBaseService instance with mocked dependencies."""
    with patch(PATCH_INTEGRATION_DAO), \
         patch(PATCH_KNOWLEDGE_BASE_DAO), \
         patch(PATCH_INGESTION_RUN_DAO), \
         patch(PATCH_ELASTIC_SEARCH_ADAPTER), \
         patch(PATCH_ELASTICSEARCH_SQL_ADAPTER):
        return KnowledgeBaseService(mock_session)


@pytest.fixture
def sample_user_data():
    """Create sample UserData for testing."""
    return UserData(
        userId=TEST_USER_ID,
        orgId=TEST_ORG_ID,
        firstName=TEST_FIRST_NAME,
        lastName=TEST_LAST_NAME,
        email=TEST_EMAIL,
        username=TEST_USERNAME,
        phoneNumber=TEST_PHONE,
        profilePicUrl=TEST_PROFILE_URL,
        active=True,
        roleIds=[1, 2],
        meta={},
        createdAt=TEST_DATETIME,
        updatedAt=TEST_DATETIME,
        workspace=[]
    )


@pytest.fixture
def sample_knowledge_base():
    """Create sample KnowledgeBase for testing."""
    kb = Mock(spec=KnowledgeBase)
    kb.id = TEST_KB_ID
    kb.integration_id = TEST_INTEGRATION_ID
    kb.name = TEST_KB_NAME
    kb.source_identifier = TEST_SOURCE_IDENTIFIER
    kb.kb_type = TEST_KB_TYPE
    kb.settings_json = TEST_SETTINGS
    kb.memory = TEST_MEMORY
    kb.state = KBState.ready
    kb.created_at = TEST_DATETIME
    kb.last_indexed_at = TEST_DATETIME
    kb.team_id = TEST_TEAM_ID
    kb.created_by = TEST_USER_ID
    kb.org_id = TEST_ORG_ID
    kb.is_updatable = True
    return kb


@pytest.fixture
def sample_integration():
    """Create sample Integration for testing."""
    integration = Mock(spec=Integration)
    integration.id = TEST_INTEGRATION_ID
    integration.type = INTEGRATION_TYPE_GITHUB
    integration.name = TEST_INTEGRATION_NAME
    integration.credentials_json = {"token": "test-token"}
    return integration


@pytest.fixture
def sample_get_kb_request():
    """Create sample GetKbRequest for testing."""
    return GetKbRequest(
        skip=TEST_SKIP,
        limit=TEST_LIMIT,
        page=TEST_PAGE,
        size=TEST_PER_PAGE,
        user_id=TEST_USER_ID,
        search=TEST_SEARCH_QUERY,
        team_ids=[TEST_TEAM_ID],
        org_ids=[TEST_ORG_ID]
    )


@pytest.fixture
def sample_vector_search_request():
    """Create sample GetVectorSearchRequest for testing."""
    return GetVectorSearchRequest(
        query=TEST_SEARCH_QUERY,
        knowledge_base_id=[TEST_KB_ID],
        user_id=TEST_USER_ID,
        org_id=TEST_ORG_ID,
        team_id=TEST_TEAM_ID,
        type=TEST_SEARCH_TYPE
    )


@pytest.fixture
def sample_share_request():
    """Create sample ShareKnowledgeBaseRequest for testing."""
    return ShareKnowledgeBaseRequest(
        kb_ids=[TEST_KB_ID],
        user_id=[TEST_USER_ID],
        team_id=[TEST_TEAM_ID],
        org_id=[TEST_ORG_ID]
    )


class TestKnowledgeBaseService:
    """Test cases for KnowledgeBaseService class."""

    def test_service_initialization(self, mock_session):
        """Test KnowledgeBaseService initialization."""
        with patch(PATCH_INTEGRATION_DAO), \
             patch(PATCH_KNOWLEDGE_BASE_DAO), \
             patch(PATCH_INGESTION_RUN_DAO), \
             patch(PATCH_ELASTIC_SEARCH_ADAPTER), \
             patch(PATCH_ELASTICSEARCH_SQL_ADAPTER):
            
            service = KnowledgeBaseService(mock_session)
            
            assert service.integration_dao is not None
            assert service.knowledge_base_dao is not None
            assert service.ingestion_run_dao is not None
            assert service.vector_db is not None
            assert service.vectordb_for_sql is not None

    @pytest.mark.asyncio
    async def test_get_integrations_success(self, knowledge_base_service):
        """Test successful retrieval of integrations."""
        # Arrange
        expected_integrations = [{"id": 1, "name": "GitHub", "type": "github"}]
        knowledge_base_service.integration_dao.get_all_integrations.return_value = expected_integrations

        # Act
        result = await knowledge_base_service.get_integrations()

        # Assert
        assert result.success is SUCCESS_STATUS
        assert result.data == expected_integrations
        knowledge_base_service.integration_dao.get_all_integrations.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_vector_search_success(self, knowledge_base_service, sample_vector_search_request):
        """Test successful vector search operation."""
        # Arrange
        expected_result = {"results": ["test result"]}
        
        with patch(PATCH_CHECK_DATA_SOURCE_ACCESS, return_value=True):
            knowledge_base_service.vector_db.connect = AsyncMock()
            knowledge_base_service.vector_db.search_and_fetch_content = AsyncMock(return_value=expected_result)
            knowledge_base_service.vector_db.close = AsyncMock()
            knowledge_base_service.vectordb_for_sql.close = AsyncMock()

            # Act
            result = await knowledge_base_service.get_vector_search(sample_vector_search_request)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data == expected_result
            knowledge_base_service.vector_db.connect.assert_called_once()
            knowledge_base_service.vector_db.search_and_fetch_content.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_vector_search_no_access(self, knowledge_base_service, sample_vector_search_request):
        """Test vector search with no access to knowledge base."""
        # Arrange
        with patch(PATCH_CHECK_DATA_SOURCE_ACCESS, return_value=False):
            
            # Act
            result = await knowledge_base_service.get_vector_search(sample_vector_search_request)

            # Assert
            assert result.success is FAILURE_STATUS
            assert ERROR_NO_ACCESS in result.message

    @pytest.mark.asyncio
    async def test_get_vector_search_sql_type(self, knowledge_base_service, sample_vector_search_request):
        """Test vector search with SQL data analyst type."""
        # Arrange
        sample_vector_search_request.type = "sql_data_analyst"
        expected_result = {"sql_results": ["test result"]}
        
        with patch(PATCH_CHECK_DATA_SOURCE_ACCESS, return_value=True):
            knowledge_base_service.vectordb_for_sql.connect = AsyncMock()
            knowledge_base_service.vectordb_for_sql.knn_similarity_search = AsyncMock(return_value=expected_result)
            knowledge_base_service.vector_db.close = AsyncMock()
            knowledge_base_service.vectordb_for_sql.close = AsyncMock()

            # Act
            result = await knowledge_base_service.get_vector_search(sample_vector_search_request)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data == expected_result
            knowledge_base_service.vectordb_for_sql.connect.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_vector_search_api_exception(self, knowledge_base_service, sample_vector_search_request):
        """Test vector search raises ApiException on failure."""
        # Arrange
        error_message = "Search failed"
        
        with patch(PATCH_CHECK_DATA_SOURCE_ACCESS, return_value=True):
            knowledge_base_service.vector_db.connect = AsyncMock()
            knowledge_base_service.vector_db.search_and_fetch_content = AsyncMock(side_effect=Exception(error_message))
            knowledge_base_service.vector_db.close = AsyncMock()
            knowledge_base_service.vectordb_for_sql.close = AsyncMock()

            # Act & Assert
            with pytest.raises(ApiException) as exc_info:
                await knowledge_base_service.get_vector_search(sample_vector_search_request)
            
            assert "Search operation failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_knowledge_bases_success(self, knowledge_base_service, sample_get_kb_request, sample_user_data):
        """Test successful retrieval of knowledge bases."""
        # Arrange
        mock_kb_data = {
            "id": TEST_KB_ID,
            "name": TEST_KB_NAME,
            "kb_type": TEST_KB_TYPE,
            "state": STATE_READY
        }
        
        expected_result = {
            "knowledge_bases": [mock_kb_data],
            "pagination": {
                "page": TEST_PAGE,
                "per_page": TEST_PER_PAGE,
                "total": TEST_TOTAL,
                "pages": TEST_PAGES
            }
        }

        with patch(PATCH_GET_ACCESSIBLE_DATASOURCES) as mock_get_accessible:
            mock_get_accessible.return_value = {
                "personal": [TEST_KB_ID],
                "team": [],
                "organization": []
            }
            
            knowledge_base_service.knowledge_base_dao.get_knowledge_bases_with_pagination.return_value = expected_result

            # Act
            result = await knowledge_base_service.get_knowledge_bases(sample_get_kb_request, sample_user_data)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data is not None
            assert "personal" in result.data
            assert "team" in result.data
            assert "organization" in result.data
            assert result.pagination is not None

    @pytest.mark.asyncio
    async def test_get_knowledge_bases_error_handling(self, knowledge_base_service, sample_get_kb_request, sample_user_data):
        """Test error handling in get_knowledge_bases."""
        # Arrange
        with patch(PATCH_GET_ACCESSIBLE_DATASOURCES) as mock_get_accessible:
            mock_get_accessible.side_effect = Exception("API call failed")

            # Act
            result = await knowledge_base_service.get_knowledge_bases(sample_get_kb_request, sample_user_data)

            # Assert
            assert result.success is FAILURE_STATUS
            assert ERROR_FAILED_TO_FETCH in result.message

    @pytest.mark.asyncio
    async def test_get_knowledge_base_success(self, knowledge_base_service, sample_knowledge_base):
        """Test successful retrieval of single knowledge base."""
        # Arrange
        request = GetKnowledgeBaseRequest(
            user_id=TEST_USER_ID,
            org_id=TEST_ORG_ID,
            team_id=TEST_TEAM_ID
        )
        
        with patch(PATCH_CHECK_DATA_SOURCE_ACCESS, return_value=True):
            knowledge_base_service.knowledge_base_dao.get_by_id.return_value = sample_knowledge_base

            # Act
            result = await knowledge_base_service.get_knowledge_base(TEST_KB_ID, request)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data is not None
            assert result.data["id"] == TEST_KB_ID
            assert result.data["name"] == TEST_KB_NAME

    @pytest.mark.asyncio
    async def test_get_knowledge_base_no_access(self, knowledge_base_service):
        """Test retrieval of knowledge base without access."""
        # Arrange
        request = GetKnowledgeBaseRequest(
            user_id=TEST_USER_ID,
            org_id=TEST_ORG_ID,
            team_id=TEST_TEAM_ID
        )
        
        with patch(PATCH_CHECK_DATA_SOURCE_ACCESS, return_value=False):

            # Act
            result = await knowledge_base_service.get_knowledge_base(TEST_KB_ID, request)

            # Assert
            assert result.success is FAILURE_STATUS
            assert ERROR_NO_ACCESS in result.message

    @pytest.mark.asyncio
    async def test_get_knowledge_base_not_found(self, knowledge_base_service):
        """Test retrieval of non-existent knowledge base."""
        # Arrange
        request = GetKnowledgeBaseRequest(
            user_id=TEST_USER_ID,
            org_id=TEST_ORG_ID,
            team_id=TEST_TEAM_ID
        )
        
        with patch(PATCH_CHECK_DATA_SOURCE_ACCESS, return_value=True):
            knowledge_base_service.knowledge_base_dao.get_by_id.return_value = None

            # Act
            result = await knowledge_base_service.get_knowledge_base(TEST_KB_ID, request)

            # Assert
            assert result.success is FAILURE_STATUS
            assert ERROR_NOT_FOUND in result.message

    @pytest.mark.asyncio
    async def test_get_folder_structure_success(self, knowledge_base_service, sample_knowledge_base):
        """Test successful folder structure retrieval."""
        # Arrange
        kb_ids = [TEST_KB_ID]
        knowledge_base_service.knowledge_base_dao.get_by_id.return_value = sample_knowledge_base

        # Act
        result = await knowledge_base_service.get_folder_structure(kb_ids)

        # Assert
        assert result.success is SUCCESS_STATUS
        assert result.data is not None
        assert "folder_structures" in result.data

    @pytest.mark.asyncio
    async def test_get_folder_structure_with_path(self, knowledge_base_service, sample_knowledge_base):
        """Test folder structure retrieval with specific path."""
        # Arrange
        kb_ids = [TEST_KB_ID]
        folder_path = TEST_FOLDER_PATH
        knowledge_base_service.knowledge_base_dao.get_by_id.return_value = sample_knowledge_base

        # Act
        result = await knowledge_base_service.get_folder_structure(kb_ids, folder_path)

        # Assert
        assert result.success is SUCCESS_STATUS
        assert result.data is not None

    @pytest.mark.asyncio
    async def test_get_folder_structure_exception(self, knowledge_base_service):
        """Test folder structure retrieval exception handling."""
        # Arrange
        kb_ids = [TEST_KB_ID]
        knowledge_base_service.knowledge_base_dao.get_by_id.side_effect = Exception("Database error")

        # Act & Assert
        with pytest.raises(CustomException) as exc_info:
            await knowledge_base_service.get_folder_structure(kb_ids)
        
        assert "Get folder structure operation failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_is_url_public_success(self, knowledge_base_service, sample_user_data):
        """Test successful URL public check."""
        # Arrange
        with patch(PATCH_KB_FACTORY_GET_EXTRACTOR) as mock_extractor:
            mock_extractor_instance = AsyncMock()
            mock_extractor_instance.is_project_public.return_value = True
            mock_extractor.return_value = mock_extractor_instance

            # Act
            result = await knowledge_base_service.is_url_public(TEST_URL, TEST_PROVIDER, sample_user_data)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data["is_public"] is True

    @pytest.mark.asyncio
    async def test_is_url_public_git_extension_removal(self, knowledge_base_service, sample_user_data):
        """Test URL public check with .git extension removal."""
        # Arrange
        git_url = "https://github.com/test/repo.git"
        with patch(PATCH_KB_FACTORY_GET_EXTRACTOR) as mock_extractor:
            mock_extractor_instance = AsyncMock()
            mock_extractor_instance.is_project_public.return_value = False
            mock_extractor.return_value = mock_extractor_instance

            # Act
            result = await knowledge_base_service.is_url_public(git_url, TEST_PROVIDER, sample_user_data)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data["is_public"] is False
            # Verify .git was removed from URL
            mock_extractor.assert_called_with(TEST_PROVIDER, repo_url=TEST_URL)

    @pytest.mark.asyncio
    async def test_is_url_public_exception(self, knowledge_base_service, sample_user_data):
        """Test URL public check exception handling."""
        # Arrange
        with patch(PATCH_KB_FACTORY_GET_EXTRACTOR) as mock_extractor:
            mock_extractor.side_effect = Exception("Extractor error")

            # Act & Assert
            with pytest.raises(CustomException):
                await knowledge_base_service.is_url_public(TEST_URL, TEST_PROVIDER, sample_user_data)

    @pytest.mark.asyncio
    async def test_delete_knowledge_bases_success(self, knowledge_base_service, mock_connection_handler, sample_user_data, sample_knowledge_base):
        """Test successful knowledge base deletion."""
        # Arrange
        kb_ids = [TEST_KB_ID]
        sample_knowledge_base.created_by = TEST_USER_ID
        
        with patch('knowledge_base.service._call_revoke_data_source_access'):
            knowledge_base_service.knowledge_base_dao.get_by_id.return_value = sample_knowledge_base
            knowledge_base_service.vector_db.connect = AsyncMock()
            knowledge_base_service.vector_db.delete_documents_by_kb_id = AsyncMock(return_value={"deleted": 1, "failed": 0})
            knowledge_base_service.vector_db.close = AsyncMock()
            knowledge_base_service.knowledge_base_dao.delete_knowledge_bases = AsyncMock()

            # Act
            result = await knowledge_base_service.delete_knowledge_bases(kb_ids, mock_connection_handler, sample_user_data)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert "Successfully deleted" in result.data["message"]
            assert result.data["deleted_kb_ids"] == kb_ids

    @pytest.mark.asyncio
    async def test_delete_knowledge_bases_no_permission(self, knowledge_base_service, mock_connection_handler, sample_user_data, sample_knowledge_base):
        """Test knowledge base deletion without permission."""
        # Arrange
        kb_ids = [TEST_KB_ID]
        sample_knowledge_base.created_by = "different-user"
        knowledge_base_service.knowledge_base_dao.get_by_id.return_value = sample_knowledge_base

        # Act & Assert
        with pytest.raises(CustomException) as exc_info:
            await knowledge_base_service.delete_knowledge_bases(kb_ids, mock_connection_handler, sample_user_data)
        
        assert ERROR_NO_ACCESS in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_knowledge_bases_success(self, knowledge_base_service, sample_user_data, sample_knowledge_base):
        """Test successful knowledge base update."""
        # Arrange
        update_request = UpdateKB(
            kb_id=TEST_KB_ID,
            name="Updated KB Name",
            pat="new-token", 
            url="https://github.com/test/new-repo",
            branch_name="develop"
        )
        
        sample_knowledge_base.created_by = TEST_USER_ID
        sample_knowledge_base.settings_json = {
            "provider": "github",
            "credentials": {"url": TEST_URL, "pat": "old-token", "branch_name": "main"}
        }
        
        with patch(PATCH_KB_FACTORY_GET_EXTRACTOR) as mock_extractor:
            mock_extractor_instance = AsyncMock()
            mock_extractor_instance.is_project_public.return_value = True
            mock_extractor.return_value = mock_extractor_instance
            
            knowledge_base_service.knowledge_base_dao.get_by_id.return_value = sample_knowledge_base
            knowledge_base_service.knowledge_base_dao.update_kb_name_and_pat_url_branch = AsyncMock()

            # Act
            result = await knowledge_base_service.update_knowledge_bases(update_request, sample_user_data)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert "Successfully updated" in result.data["message"]

    @pytest.mark.asyncio
    async def test_update_knowledge_bases_no_permission(self, knowledge_base_service, sample_user_data, sample_knowledge_base):
        """Test knowledge base update without permission."""
        # Arrange
        update_request = UpdateKB(kb_id=TEST_KB_ID, name="Updated KB Name")
        sample_knowledge_base.created_by = "different-user"
        knowledge_base_service.knowledge_base_dao.get_by_id.return_value = sample_knowledge_base

        # Act & Assert
        with pytest.raises(CustomException) as exc_info:
            await knowledge_base_service.update_knowledge_bases(update_request, sample_user_data)
        
        assert ERROR_NO_ACCESS in str(exc_info.value)


class TestKnowledgeBaseServiceEdgeCases:
    """Test edge cases and error scenarios for KnowledgeBaseService."""

    @pytest.mark.asyncio
    async def test_service_with_none_dependencies(self):
        """Test service behavior with None dependencies."""
        # This test ensures the service handles None gracefully in initialization
        with patch(PATCH_INTEGRATION_DAO), \
             patch(PATCH_KNOWLEDGE_BASE_DAO), \
             patch(PATCH_INGESTION_RUN_DAO), \
             patch(PATCH_ELASTIC_SEARCH_ADAPTER), \
             patch(PATCH_ELASTICSEARCH_SQL_ADAPTER):
            
            service = KnowledgeBaseService(None)
            assert service is not None

    @pytest.mark.asyncio
    async def test_empty_knowledge_base_list(self, knowledge_base_service, sample_user_data):
        """Test handling empty knowledge base list."""
        # Arrange
        request = GetKbRequest(
            skip=TEST_SKIP,
            limit=TEST_LIMIT,
            page=TEST_PAGE,
            size=TEST_PER_PAGE,
            user_id=TEST_USER_ID
        )
        
        expected_result = {
            "knowledge_bases": [],
            "pagination": {"page": TEST_PAGE, "per_page": TEST_PER_PAGE, "total": 0, "pages": 0}
        }

        with patch(PATCH_GET_ACCESSIBLE_DATASOURCES) as mock_get_accessible:
            mock_get_accessible.return_value = {"personal": [], "team": [], "organization": []}
            knowledge_base_service.knowledge_base_dao.get_knowledge_bases_with_pagination.return_value = expected_result

            # Act
            result = await knowledge_base_service.get_knowledge_bases(request, sample_user_data)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert len(result.data["personal"]) == 0
            assert len(result.data["team"]) == 0
            assert len(result.data["organization"]) == 0

    @pytest.mark.asyncio
    async def test_concurrent_operations_safety(self, knowledge_base_service):
        """Test service safety with concurrent operations."""
        # This test ensures the service can handle concurrent calls safely
        request = GetKnowledgeBaseRequest(
            user_id=TEST_USER_ID,
            org_id=TEST_ORG_ID,
            team_id=TEST_TEAM_ID
        )
        
        with patch(PATCH_CHECK_DATA_SOURCE_ACCESS, return_value=False):
            # Act - simulate concurrent calls
            results = await asyncio.gather(
                knowledge_base_service.get_knowledge_base(TEST_KB_ID, request),
                knowledge_base_service.get_knowledge_base(TEST_KB_ID + 1, request),
                return_exceptions=True
            )

            # Assert
            for result in results:
                if isinstance(result, ResponseData):
                    assert result.success is FAILURE_STATUS 