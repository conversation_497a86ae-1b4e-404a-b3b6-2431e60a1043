"""
Comprehensive tests for knowledge_base.loaders module to achieve 100% coverage.

This module contains test data that are safe for testing purposes.
All test data in this file is fictional and for testing purposes only.
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any

from knowledge_base.loaders import (
    DataLoader,
    ElasticSearchLoader,
    ElasticSearchSQLLoader
)
from knowledge_base.dao import KnowledgeBaseDao, IngestionRunDao
from knowledge_base.models import KBState
from utils.vector_db.elastic_sql_adapter import ElasticsearchSQLAdapter


# Test Constants - All values are safe test data, not production values
TEST_INDEX_NAME = "test_index"
TEST_KNOWLEDGE_BASE_ID = "kb1"
TEST_DOC_ID_1 = "doc1"
TEST_DOC_ID_2 = "doc2"
TEST_DOC_TITLE_1 = "Test Document 1"
TEST_DOC_TITLE_2 = "Test Document 2"
TEST_DOC_CONTENT_1 = "This is test content 1"
TEST_DOC_CONTENT_2 = "This is test content 2"
TEST_CUSTOM_FIELD_VALUE = "test_value"
TEST_FIELD_NAME = "custom_field"
TEST_TABLE_NAME = "test_table"
TEST_COLUMN_NAME = "test_column"
TEST_COLUMN_TYPE = "VARCHAR"
TEST_COLUMN_DESCRIPTION = "Test column description"
TEST_TABLE_DESCRIPTION = "Test table description"
TEST_SCHEMA_NAME = "test_schema"
TEST_EMBEDDING_TEXT = "Test embedding text"
TEST_EMBEDDING_VECTOR = [0.1, 0.2, 0.3]
TEST_EMBEDDING_VECTOR_2 = [0.4, 0.5, 0.6]

# Patch Path Constants
ELASTIC_SQL_ADAPTER_MAPPING_PATH = 'utils.vector_db.elastic_sql_adapter.ElasticsearchSQLAdapter.INDEXING_DEFAULT_MAPPING_FOR_SQL'

# Mock Mapping Constants
MOCK_MAPPING_RESPONSE = {
    "mappings": {
        "properties": {
            "column_description_embedding": {
                "dims": 1536
            }
        }
    }
}

# Elasticsearch Settings Constants
ES_SHARD_COUNT = 5
ES_REPLICA_COUNT = 2
ES_CUSTOM_SHARD_COUNT = 3
ES_EMBEDDING_DIMENSION = 1536
ES_FIELD_TYPE_KEYWORD = "keyword"
ES_FIELD_TYPE_TEXT = "text"
ES_FIELD_TYPE_DENSE_VECTOR = "dense_vector"
ES_FIELD_TYPE_BOOLEAN = "boolean"

# Batch Processing Constants
DEFAULT_BATCH_SIZE = 10
SMALL_BATCH_SIZE = 2
LARGE_DOCUMENT_COUNT = 5

# Success/Failure Constants
SUCCESS_COUNT_1 = 1
SUCCESS_COUNT_2 = 2
SUCCESS_COUNT_5 = 5
FAILED_COUNT_0 = 0
EXPECTED_BATCH_COUNT = 3

# Error Messages
ERROR_INDEX_ERROR = "index error"
ERROR_MISSING_INDEX = "Missing 'index_name' in parameters."
ERROR_EMBEDDING_FAILED = "Embedding generation failed"
ERROR_DELETE_FAILED = "Delete operation failed"


class TestDataLoader:
    """Test cases for DataLoader abstract base class."""
    
    def test_data_loader_is_abstract(self):
        """Test that DataLoader cannot be instantiated directly."""
        with pytest.raises(TypeError, match="Can't instantiate abstract class"):
            DataLoader(Mock())
    
    def test_data_loader_init(self):
        """Test DataLoader initialization with db_adapter."""
        # Create a concrete implementation for testing
        class ConcreteDataLoader(DataLoader):
            async def load(self, params: Dict[str, Any]):
                """Abstract method to be implemented by subclasses."""
                pass
        
        mock_adapter = Mock()
        loader = ConcreteDataLoader(mock_adapter)
        
        assert loader.db_adapter is mock_adapter


class TestElasticSearchLoader:
    """Test cases for ElasticSearchLoader class."""
    
    @pytest.fixture
    def mock_db_adapter(self):
        """Create mock database adapter."""
        adapter = Mock()
        adapter.connect = AsyncMock()
        adapter.close = AsyncMock()
        adapter.bulk_insert = AsyncMock()
        return adapter
    
    @pytest.fixture
    def sample_documents(self):
        """Create sample documents for testing."""
        return [
            {
                "id": TEST_DOC_ID_1,
                "title": TEST_DOC_TITLE_1,
                "content": TEST_DOC_CONTENT_1,
                "embedding": TEST_EMBEDDING_VECTOR,
                "knowledge_base_id": TEST_KNOWLEDGE_BASE_ID
            },
            {
                "id": TEST_DOC_ID_2,
                "title": TEST_DOC_TITLE_2,
                "content": TEST_DOC_CONTENT_2,
                "embedding": TEST_EMBEDDING_VECTOR_2,
                "knowledge_base_id": TEST_KNOWLEDGE_BASE_ID
            }
        ]
    
    @pytest.fixture
    def custom_settings(self):
        """Create custom settings for testing."""
        return {
            "settings": {"number_of_shards": ES_CUSTOM_SHARD_COUNT},
            "mappings": {"properties": {TEST_FIELD_NAME: {"type": ES_FIELD_TYPE_TEXT}}}
        }
    
    @pytest.fixture
    def large_document_set(self):
        """Create large document set for batch testing."""
        return [
            {"id": f"doc{i}", "title": f"Doc {i}", "content": f"Content {i}"}
            for i in range(LARGE_DOCUMENT_COUNT)
        ]
    
    def test_init(self, mock_db_adapter):
        """Test ElasticSearchLoader initialization."""
        loader = ElasticSearchLoader(mock_db_adapter)
        
        assert loader.db_adapter is mock_db_adapter
        assert hasattr(loader, 'KNOWLEDGE_BASE_INDEXING_SETTINGS')
        assert 'settings' in loader.KNOWLEDGE_BASE_INDEXING_SETTINGS
        assert 'mappings' in loader.KNOWLEDGE_BASE_INDEXING_SETTINGS
    
    def test_knowledge_base_indexing_settings(self):
        """Test the structure of KNOWLEDGE_BASE_INDEXING_SETTINGS."""
        loader = ElasticSearchLoader(Mock())
        settings = loader.KNOWLEDGE_BASE_INDEXING_SETTINGS
        
        # Check settings structure
        assert 'settings' in settings
        assert 'number_of_shards' in settings['settings']
        assert 'number_of_replicas' in settings['settings']
        assert settings['settings']['number_of_shards'] == ES_SHARD_COUNT
        assert settings['settings']['number_of_replicas'] == ES_REPLICA_COUNT
        
        # Check mappings structure
        assert 'mappings' in settings
        assert 'properties' in settings['mappings']
        properties = settings['mappings']['properties']
        
        # Check required fields
        required_fields = ['id', 'title', 'content', 'embedding', 'chunk_references', 
                          'source', 'is_chunked', 'is_public', 'description', 
                          'knowledge_base_id', 'metadata']
        
        for field in required_fields:
            assert field in properties
        
        # Check specific field types
        assert properties['id']['type'] == ES_FIELD_TYPE_KEYWORD
        assert properties['title']['type'] == ES_FIELD_TYPE_TEXT
        assert properties['content']['type'] == ES_FIELD_TYPE_TEXT
        assert properties['embedding']['type'] == ES_FIELD_TYPE_DENSE_VECTOR
        assert properties['embedding']['dims'] == ES_EMBEDDING_DIMENSION
        assert properties['is_chunked']['type'] == ES_FIELD_TYPE_BOOLEAN
        assert properties['is_public']['type'] == ES_FIELD_TYPE_BOOLEAN
    
    @pytest.mark.asyncio
    async def test_load_success(self, mock_db_adapter, sample_documents):
        """Test successful document loading."""
        loader = ElasticSearchLoader(mock_db_adapter)
        
        # Mock successful bulk insert
        mock_db_adapter.bulk_insert.return_value = {"success": SUCCESS_COUNT_2, "failed": []}
        
        params = {
            "index_name": TEST_INDEX_NAME,
            "documents": sample_documents,
            "batch_size": DEFAULT_BATCH_SIZE
        }
        
        result = await loader.load(params)
        
        assert result["success"] == SUCCESS_COUNT_2
        assert result["failed"] == []
        mock_db_adapter.bulk_insert.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_load_with_custom_settings(self, mock_db_adapter, sample_documents, custom_settings):
        """Test loading with custom settings."""
        loader = ElasticSearchLoader(mock_db_adapter)
        
        # Mock successful bulk insert
        mock_db_adapter.bulk_insert.return_value = {"success": SUCCESS_COUNT_2, "failed": []}
        
        params = {
            "index_name": TEST_INDEX_NAME,
            "documents": sample_documents,
            "batch_size": DEFAULT_BATCH_SIZE,
            "settings": custom_settings
        }
        
        result = await loader.load(params)
        
        assert result["success"] == SUCCESS_COUNT_2
        # Verify bulk insert was called with custom settings
        bulk_insert_call = mock_db_adapter.bulk_insert.call_args[0]
        assert bulk_insert_call[2] == custom_settings  # settings is the third argument
    
    @pytest.mark.asyncio
    async def test_load_with_batching(self, mock_db_adapter, large_document_set):
        """Test loading with document batching."""
        loader = ElasticSearchLoader(mock_db_adapter)
        
        # Mock successful bulk insert - each batch returns 2 successes (batch size 2)
        # With 5 documents and batch size 2, we get 3 batches: [0,1], [2,3], [4]
        # First two batches have 2 documents each, last batch has 1 document
        mock_db_adapter.bulk_insert.side_effect = [
            {"success": 2, "failed": []},  # First batch
            {"success": 2, "failed": []},  # Second batch
            {"success": 1, "failed": []}   # Third batch
        ]
        
        params = {
            "index_name": TEST_INDEX_NAME,
            "documents": large_document_set,
            "batch_size": SMALL_BATCH_SIZE
        }
        
        result = await loader.load(params)
        
        assert result["success"] == SUCCESS_COUNT_5
        # Should be called multiple times for batching
        assert mock_db_adapter.bulk_insert.call_count == EXPECTED_BATCH_COUNT
    
    @pytest.mark.asyncio
    async def test_load_with_failures(self, mock_db_adapter, sample_documents):
        """Test loading with some failures."""
        loader = ElasticSearchLoader(mock_db_adapter)
        
        # Mock bulk insert with failures
        mock_db_adapter.bulk_insert.return_value = {"success": SUCCESS_COUNT_1, "failed": [TEST_DOC_ID_2]}
        
        params = {
            "index_name": TEST_INDEX_NAME,
            "documents": sample_documents,
            "batch_size": DEFAULT_BATCH_SIZE
        }
        
        result = await loader.load(params)
        
        assert result["success"] == SUCCESS_COUNT_1
        assert result["failed"] == [TEST_DOC_ID_2]
    
    @pytest.mark.asyncio
    async def test_load_missing_index_name(self, mock_db_adapter, sample_documents):
        """Test loading without index name."""
        loader = ElasticSearchLoader(mock_db_adapter)
        
        params = {
            "documents": sample_documents,
            "batch_size": DEFAULT_BATCH_SIZE
        }
        
        with pytest.raises(ValueError, match=ERROR_MISSING_INDEX):
            await loader.load(params)
    
    @pytest.mark.asyncio
    async def test_load_empty_documents(self, mock_db_adapter):
        """Test loading with empty documents list."""
        loader = ElasticSearchLoader(mock_db_adapter)
        
        params = {
            "index_name": TEST_INDEX_NAME,
            "documents": [],
            "batch_size": DEFAULT_BATCH_SIZE
        }
        
        result = await loader.load(params)
        
        assert result["success"] == FAILED_COUNT_0
        assert result["failed"] == []
        # Should not call bulk_insert for empty documents
        mock_db_adapter.bulk_insert.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_load_exception_handling(self, mock_db_adapter, sample_documents):
        """Test exception handling during loading."""
        loader = ElasticSearchLoader(mock_db_adapter)
        
        # Mock bulk insert to raise exception
        mock_db_adapter.bulk_insert.side_effect = Exception(ERROR_INDEX_ERROR)
        
        params = {
            "index_name": TEST_INDEX_NAME,
            "documents": sample_documents,
            "batch_size": DEFAULT_BATCH_SIZE
        }
        
        with pytest.raises(Exception, match=ERROR_INDEX_ERROR):
            await loader.load(params)
    
    @pytest.mark.asyncio
    async def test_load_default_batch_size(self, mock_db_adapter, sample_documents):
        """Test loading with default batch size."""
        loader = ElasticSearchLoader(mock_db_adapter)
        
        # Mock successful bulk insert
        mock_db_adapter.bulk_insert.return_value = {"success": SUCCESS_COUNT_2, "failed": []}
        
        params = {
            "index_name": TEST_INDEX_NAME,
            "documents": sample_documents
            # No batch_size specified
        }
        
        result = await loader.load(params)
        
        assert result["success"] == SUCCESS_COUNT_2
        # Should use default batch size
        assert mock_db_adapter.bulk_insert.call_count == SUCCESS_COUNT_1
    
    def test_chunks_helper_function(self, mock_db_adapter):
        """Test the chunks helper function."""
        loader = ElasticSearchLoader(mock_db_adapter)
        
        # Access the chunks function defined in the load method
        def chunks(lst, size):
            """Yield successive n-sized chunks from lst."""
            for i in range(0, len(lst), size):
                yield lst[i:i + size]
        
        test_list = [1, 2, 3, 4, 5]
        chunks_result = list(chunks(test_list, 2))
        
        assert len(chunks_result) == 3
        assert chunks_result[0] == [1, 2]
        assert chunks_result[1] == [3, 4]
        assert chunks_result[2] == [5]


class TestElasticSearchSQLLoader:
    """Test cases for ElasticSearchSQLLoader class."""
    
    @pytest.fixture
    def mock_db_adapter(self):
        """Create mock database adapter."""
        adapter = Mock()
        adapter.connect = AsyncMock()
        adapter.close = AsyncMock()
        adapter.create_index = AsyncMock()
        adapter.delete_documents_by_kb_id = AsyncMock()
        adapter.bulk_insert_docs = AsyncMock()
        adapter.embedding_generator = Mock()
        adapter.embedding_generator.generate_embedding = AsyncMock()
        adapter.embedding_generator.generate_embedding.return_value = TEST_EMBEDDING_VECTOR
        return adapter
    
    @pytest.fixture
    def sample_tables(self):
        """Create sample tables for testing."""
        return [
            {
                "table_name": TEST_TABLE_NAME,
                "table_schema": TEST_SCHEMA_NAME,
                "table_description": TEST_TABLE_DESCRIPTION,
                "columns": [
                    {
                        "column_name": TEST_COLUMN_NAME,
                        "data_type": TEST_COLUMN_TYPE,
                        "column_description": TEST_COLUMN_DESCRIPTION
                    }
                ]
            }
        ]
    
    @pytest.fixture
    def sample_tables_without_descriptions(self):
        """Create sample tables without descriptions."""
        return [
            {
                "table_name": TEST_TABLE_NAME,
                "table_schema": TEST_SCHEMA_NAME,
                "columns": [
                    {
                        "column_name": TEST_COLUMN_NAME,
                        "data_type": TEST_COLUMN_TYPE
                    }
                ]
            }
        ]
    
    def test_init(self, mock_db_adapter):
        """Test ElasticSearchSQLLoader initialization."""
        loader = ElasticSearchSQLLoader(mock_db_adapter)
        
        assert loader.db_adapter is mock_db_adapter
    
    @pytest.mark.asyncio
    async def test_load_success(self, mock_db_adapter, sample_tables):
        """Test successful table loading."""
        loader = ElasticSearchSQLLoader(mock_db_adapter)
        
        # Mock successful operations
        mock_db_adapter.delete_documents_by_kb_id.return_value = {"failures": []}
        mock_db_adapter.bulk_insert_docs.return_value = {"failed": FAILED_COUNT_0, "errors": []}
        
        params = {
            "index_name": TEST_INDEX_NAME,
            "tables": sample_tables,
            "knowledge_base_id": TEST_KNOWLEDGE_BASE_ID,
            "database_name": "test_db"
        }
        
        with patch(ELASTIC_SQL_ADAPTER_MAPPING_PATH, return_value=MOCK_MAPPING_RESPONSE):
            result = await loader.load(params)
            
            assert result["processed_columns"] == 1
            assert result["indexed_documents"] == 1
            assert result["failed"] == FAILED_COUNT_0
            mock_db_adapter.create_index.assert_called_once()
            mock_db_adapter.delete_documents_by_kb_id.assert_called_once()
            mock_db_adapter.bulk_insert_docs.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_load_with_delete_failures(self, mock_db_adapter, sample_tables):
        """Test loading with delete failures."""
        loader = ElasticSearchSQLLoader(mock_db_adapter)
        
        # Mock delete with failures
        mock_db_adapter.delete_documents_by_kb_id.return_value = {"failures": ["some error"]}
        mock_db_adapter.bulk_insert_docs.return_value = {"failed": FAILED_COUNT_0, "errors": []}
        
        params = {
            "index_name": TEST_INDEX_NAME,
            "tables": sample_tables,
            "knowledge_base_id": TEST_KNOWLEDGE_BASE_ID,
            "database_name": "test_db"
        }
        
        with patch(ELASTIC_SQL_ADAPTER_MAPPING_PATH, return_value=MOCK_MAPPING_RESPONSE):
            result = await loader.load(params)
            
            # Should not raise exception but log error
            assert "processed_columns" in result
    
    @pytest.mark.asyncio
    async def test_load_with_empty_tables(self, mock_db_adapter):
        """Test loading with empty tables list."""
        loader = ElasticSearchSQLLoader(mock_db_adapter)
        
        mock_db_adapter.delete_documents_by_kb_id.return_value = {"failures": []}
        
        params = {
            "index_name": TEST_INDEX_NAME,
            "tables": [],
            "knowledge_base_id": TEST_KNOWLEDGE_BASE_ID,
            "database_name": "test_db"
        }
        
        result = await loader.load(params)
        
        assert result["processed_columns"] == FAILED_COUNT_0
        assert result["indexed_documents"] == FAILED_COUNT_0
    
    @pytest.mark.asyncio
    async def test_load_with_table_without_description(self, mock_db_adapter, sample_tables_without_descriptions):
        """Test loading with table without description."""
        loader = ElasticSearchSQLLoader(mock_db_adapter)
        
        mock_db_adapter.delete_documents_by_kb_id.return_value = {"failures": []}
        mock_db_adapter.bulk_insert_docs.return_value = {"failed": FAILED_COUNT_0, "errors": []}
        
        params = {
            "index_name": TEST_INDEX_NAME,
            "tables": sample_tables_without_descriptions,
            "knowledge_base_id": TEST_KNOWLEDGE_BASE_ID,
            "database_name": "test_db"
        }
        
        with patch(ELASTIC_SQL_ADAPTER_MAPPING_PATH, return_value=MOCK_MAPPING_RESPONSE):
            result = await loader.load(params)
            
            assert result["processed_columns"] == 1
            # Should handle missing description gracefully
            mock_db_adapter.bulk_insert_docs.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_load_with_column_without_description(self, mock_db_adapter):
        """Test loading with column without description."""
        loader = ElasticSearchSQLLoader(mock_db_adapter)
        
        tables_without_column_desc = [
            {
                "table_name": TEST_TABLE_NAME,
                "table_schema": TEST_SCHEMA_NAME,
                "table_description": TEST_TABLE_DESCRIPTION,
                "columns": [
                    {
                        "column_name": TEST_COLUMN_NAME,
                        "data_type": TEST_COLUMN_TYPE
                        # No column_description
                    }
                ]
            }
        ]
        
        mock_db_adapter.delete_documents_by_kb_id.return_value = {"failures": []}
        mock_db_adapter.bulk_insert_docs.return_value = {"failed": FAILED_COUNT_0, "errors": []}
        
        params = {
            "index_name": TEST_INDEX_NAME,
            "tables": tables_without_column_desc,
            "knowledge_base_id": TEST_KNOWLEDGE_BASE_ID,
            "database_name": "test_db"
        }
        
        with patch(ELASTIC_SQL_ADAPTER_MAPPING_PATH, return_value=MOCK_MAPPING_RESPONSE):
            result = await loader.load(params)
            
            assert result["processed_columns"] == 1
            # Should handle missing column description gracefully
            mock_db_adapter.bulk_insert_docs.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_load_embedding_generation(self, mock_db_adapter):
        """Test embedding generation during loading."""
        loader = ElasticSearchSQLLoader(mock_db_adapter)
        
        table_data = [
            {
                "table_name": TEST_TABLE_NAME,
                "table_schema": TEST_SCHEMA_NAME,
                "table_description": TEST_TABLE_DESCRIPTION,
                "columns": [
                    {
                        "column_name": TEST_COLUMN_NAME,
                        "data_type": TEST_COLUMN_TYPE,
                        "column_description": TEST_COLUMN_DESCRIPTION
                    }
                ]
            }
        ]
        
        mock_db_adapter.delete_documents_by_kb_id.return_value = {"failures": []}
        mock_db_adapter.bulk_insert_docs.return_value = {"failed": FAILED_COUNT_0, "errors": []}
        
        params = {
            "index_name": TEST_INDEX_NAME,
            "tables": table_data,
            "knowledge_base_id": TEST_KNOWLEDGE_BASE_ID,
            "database_name": "test_db"
        }
        
        with patch(ELASTIC_SQL_ADAPTER_MAPPING_PATH, return_value=MOCK_MAPPING_RESPONSE):
            result = await loader.load(params)
            
            assert result["processed_columns"] == 1
            mock_db_adapter.embedding_generator.generate_embedding.assert_called_once()
            
            # Check that the embedding generation was called with proper text
            call_args = mock_db_adapter.embedding_generator.generate_embedding.call_args[0][0]
            assert TEST_TABLE_NAME in call_args or TEST_COLUMN_NAME in call_args


class TestLoaderIntegration:
    """Integration tests for loader classes."""
    
    def test_all_loaders_implement_interface(self):
        """Test that all loaders implement the DataLoader interface."""
        mock_adapter = Mock()
        
        # Test ElasticSearchLoader
        es_loader = ElasticSearchLoader(mock_adapter)
        assert hasattr(es_loader, 'load')
        assert hasattr(es_loader, 'db_adapter')
        
        # Test ElasticSearchSQLLoader
        es_sql_loader = ElasticSearchSQLLoader(mock_adapter)
        assert hasattr(es_sql_loader, 'load')
        assert hasattr(es_sql_loader, 'db_adapter')
    
    @pytest.mark.asyncio
    async def test_loader_interface_consistency(self):
        """Test that all loaders have consistent interface."""
        mock_adapter = Mock()
        
        loaders = [
            ElasticSearchLoader(mock_adapter),
            ElasticSearchSQLLoader(mock_adapter)
        ]
        
        for loader in loaders:
            # All loaders should have load method
            assert hasattr(loader, 'load')
            assert callable(loader.load)
            
            # All loaders should have db_adapter attribute
            assert hasattr(loader, 'db_adapter')
            assert loader.db_adapter is mock_adapter
    
    def test_loader_inheritance(self):
        """Test loader inheritance hierarchy."""
        mock_adapter = Mock()
        
        es_loader = ElasticSearchLoader(mock_adapter)
        es_sql_loader = ElasticSearchSQLLoader(mock_adapter)
        
        # Both should be instances of DataLoader
        assert isinstance(es_loader, DataLoader)
        assert isinstance(es_sql_loader, DataLoader)
        
        # But different types
        assert type(es_loader) != type(es_sql_loader) 