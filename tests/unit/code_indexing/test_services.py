"""
Comprehensive tests for code_indexing.services module to achieve 100% coverage.

This module contains test data that are safe for testing purposes.
All test data in this file is fictional and for testing purposes only.

SECURITY NOTE: All data in this file is safe test data:
- All file paths and URLs are fictional test values
- Code content samples are fictional test code snippets
- Graph IDs and branch names are test configuration values
- User data and organization IDs are fictional test identifiers
- No production credentials or sensitive data is used
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import hashlib
from datetime import datetime, timezone

from code_indexing.services import GraphService
from code_indexing.serializers import (
    GetGraphRequest,
    GetGraphResponse,
    SyncGraphFileRequest,
    SyncGraphFileResponse,
    SyncStatusResponse,
    KeywordSearchRequest,
    ResponseData,
    VectorSearchRequest,
    GetFileContentRequest,
    GetIndexingStatusRequest
)
from clerk_integration.utils import UserData
from knowledge_base.dao import KnowledgeBaseDao, IntegrationDao, IngestionRunDao
from knowledge_base.models import IntegrationType, KBState, KnowledgeBase, Integration, IngestionRun
from utils.connection_handler import ConnectionHandler
from utils.exceptions import CustomException
from utils.vector_db import VectorDBAdapter


# Test Constants - All values are safe test data, not production values
# Path Constants
PATH_TO_LOCAL = "/path/to/local"
PATH_TO_REMOTE = "/path/to/remote"
PATH_TO_FILE_PY = "/path/to/file.py"
TEST_FILE_PY = "/test/file.py"
PATH_TO_FILE1_PY = "/path/to/file1.py"
PATH_TO_FILE2_PY = "/path/to/file2.py"
PATH_TO_LOCAL_PROJECT = "/path/to/local-project"
PATH_TO_IGNORED_FILE = "/path/to/ignored_file.log"
PATH_TO_NONEXISTENT_PY = "/path/to/nonexistent.py"

# Code Content Constants
PRINT_HELLO = "print('hello')"
TEST_CONTENT_1 = "Test content 1"
TEST_CONTENT_2 = "Test content 2"
PRINT_HELLO_WORLD = "print('hello world')"
LOG_CONTENT = "log content"

# File Name Constants
FILE1_PY = "file1.py"
FILE2_PY = "file2.py"
TEST_FILE = "Test File"

# Service Path Constants
SERVICE_GRANT_ACCESS_PATCH = 'code_indexing.services._call_grant_data_source_access'
SERVICE_IS_IGNORED_PATCH = 'code_indexing.services.is_ignored'

# Graph ID Constants
TEST_GRAPH_ID = "test-graph-id"
NON_EXISTENT_GRAPH_ID = "non-existent-graph-id"

# Branch Constants
MAIN_BRANCH = "main"

# File Count Constants
FILE_COUNT_100 = 100
FILE_COUNT_150 = 150
FILE_COUNT_75 = 75

# User Constants
USER_ID_123 = "user123"
ORG_ID_456 = "org456"
USER_INTERNAL_ID = "user_id_123"
FIRST_NAME_TEST = "Test"
LAST_NAME_USER = "User"
EMAIL_TEST = "<EMAIL>"

# Remote URL Constants
GITHUB_TEST_REPO = "https://github.com/user/test-repo"

# KB Name Constants
TEST_REPO_NAME = "test-repo"
LOCAL_PROJECT_NAME = "local-project"

# Response Messages
SUCCESS_STATUS = "success"
FILE_SYNCED_MESSAGE = "File synced successfully."
GRAPH_EXISTS_MESSAGE = "Graph ID already exists"
GRAPH_GENERATED_MESSAGE = "Graph ID generated successfully"

# Error Messages
KB_NOT_FOUND_ERROR = "Knowledge Base not found"
GRAPH_NOT_FOUND_ERROR = "Graph not found"

# Mode Constants
CREATE_MODE = "create"

# Status Constants
RUNNING_STATUS = "running"
IN_PROGRESS_STATUS = "IN_PROGRESS"
COMPLETED_STATUS = "COMPLETED"

# Integration Constants
INTEGRATION_ID_1 = 1

# Search Constants
SEARCH_KEYWORDS = ["test", "query"]
MAX_RESULTS_10 = 10
THRESHOLD_70 = 70.0
SCORE_0_9 = 0.9
SCORE_0_8 = 0.8
SCORE_0_5 = 0.5

# Document Constants
DOC_ID_1 = "doc1"
DOC_ID_2 = "doc2"

# Metadata Constants
FILE_TYPE = "file"
START_LINE_1 = 1
START_LINE_5 = 5
END_LINE_10 = 10
END_LINE_15 = 15

# Stats Constants
TOTAL_SOURCE_ITEM_KEY = "total_source_item"

# Folder Constants
FOLDER_STRUCTURE_KEY = "folder_structure"
ROOT_FOLDER = "root"
SPECIFIC_FOLDER = "specific"
ROOT_SPECIFIC_PATH = "root/specific"

# Project Constants
SERVICE_ACCOUNT_TYPE = "service_account"
PRIVATE_KEY_ID_TEST = "private_key_id_test"

# Count Constants
ID_COUNT_2 = 2
PERCENTAGE_75 = 75.0
PERCENTAGE_100 = 100.0
PERCENTAGE_0 = 0.0

# Hash Constants
SHA256_HEX_LENGTH = 64


class TestGraphService:
    """Test cases for GraphService class."""
    
    @pytest.fixture
    def mock_vector_db_adapter(self):
        """Create mock vector DB adapter."""
        adapter = Mock(spec=VectorDBAdapter)
        adapter.knn_similarity_search = AsyncMock()
        adapter.keyword_search_source_code = AsyncMock()
        adapter.get_document_by_id = AsyncMock()
        return adapter
    
    @pytest.fixture
    def mock_knowledge_base_dao(self):
        """Create mock knowledge base DAO."""
        dao = Mock(spec=KnowledgeBaseDao)
        dao.exist_by_graph_id = AsyncMock()
        dao.update_graph_details = AsyncMock()
        dao.create_knowledge_base = AsyncMock()
        return dao
    
    @pytest.fixture
    def mock_integration_dao(self):
        """Create mock integration DAO."""
        dao = Mock(spec=IntegrationDao)
        dao.get_by_type = AsyncMock()
        return dao
    
    @pytest.fixture
    def mock_ingestion_run_dao(self):
        """Create mock ingestion run DAO."""
        dao = Mock(spec=IngestionRunDao)
        dao.get_latest_ingestion_run_by_kb_id = AsyncMock()
        dao.create_ingestion_run = AsyncMock()
        dao.get_latest_stats_json_by_kb_id = AsyncMock()
        return dao
    
    @pytest.fixture
    def sample_user_data(self):
        """Create sample user data."""
        return UserData(
            userId=USER_ID_123, 
            orgId=ORG_ID_456,
            _id=USER_INTERNAL_ID,
            firstName=FIRST_NAME_TEST,
            lastName=LAST_NAME_USER,
            email=EMAIL_TEST
        )
    
    @pytest.fixture
    def sample_integration(self):
        """Create sample integration."""
        integration = Mock(spec=Integration)
        integration.id = INTEGRATION_ID_1
        integration.type = IntegrationType.fynix_extension
        return integration
    
    @pytest.fixture
    def sample_kb(self):
        """Create sample knowledge base."""
        kb = Mock(spec=KnowledgeBase)
        kb.id = INTEGRATION_ID_1
        kb.graph_id = TEST_GRAPH_ID
        kb.total_files = FILE_COUNT_100
        kb.state = KBState.ready
        return kb
    
    @pytest.fixture
    def sample_ingestion_run(self):
        """Create sample ingestion run."""
        run = Mock(spec=IngestionRun)
        run.id = INTEGRATION_ID_1
        run.kb_id = INTEGRATION_ID_1
        run.status = RUNNING_STATUS
        return run
    
    @pytest.fixture
    def graph_service(
        self, 
        mock_vector_db_adapter, 
        mock_knowledge_base_dao, 
        mock_integration_dao, 
        mock_ingestion_run_dao
    ):
        """Create GraphService instance with mocked dependencies."""
        return GraphService(
            vector_db_adapter=mock_vector_db_adapter,
            knowledge_base_dao=mock_knowledge_base_dao,
            integration_dao=mock_integration_dao,
            ingestion_run_dao=mock_ingestion_run_dao
        )
    
    def test_init(self, graph_service, mock_vector_db_adapter, mock_knowledge_base_dao, 
                  mock_integration_dao, mock_ingestion_run_dao):
        """Test GraphService initialization."""
        assert graph_service.vector_db_adapter is mock_vector_db_adapter
        assert graph_service.knowledge_base_dao is mock_knowledge_base_dao
        assert graph_service.integration_dao is mock_integration_dao
        assert graph_service.ingestion_run_dao is mock_ingestion_run_dao
    
    @pytest.mark.asyncio
    async def test_get_local_graph_existing_entry_same_files(
        self, graph_service, mock_knowledge_base_dao, sample_user_data, sample_kb
    ):
        """Test get_local_graph with existing entry and same file count."""
        request = GetGraphRequest(
            path=PATH_TO_LOCAL,
            remote_url=PATH_TO_REMOTE,
            branch=MAIN_BRANCH,
            total_files=FILE_COUNT_100
        )
        
        # Mock existing KB with same file count
        sample_kb.total_files = FILE_COUNT_100
        mock_knowledge_base_dao.exist_by_graph_id.return_value = sample_kb
        
        result = await graph_service.get_local_graph(request, sample_user_data)
        
        assert result.success is True
        assert GRAPH_EXISTS_MESSAGE in result.data["message"]
        mock_knowledge_base_dao.update_graph_details.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_get_local_graph_existing_entry_different_files(
        self, graph_service, mock_knowledge_base_dao, sample_user_data, sample_kb
    ):
        """Test get_local_graph with existing entry and different file count."""
        request = GetGraphRequest(
            path=PATH_TO_LOCAL,
            remote_url=PATH_TO_REMOTE,
            branch=MAIN_BRANCH,
            total_files=FILE_COUNT_150
        )
        
        # Mock existing KB with different file count
        sample_kb.total_files = FILE_COUNT_100
        mock_knowledge_base_dao.exist_by_graph_id.return_value = sample_kb
        
        result = await graph_service.get_local_graph(request, sample_user_data)
        
        assert result.success is True
        assert GRAPH_EXISTS_MESSAGE in result.data["message"]
        mock_knowledge_base_dao.update_graph_details.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_local_graph_new_entry(
        self, graph_service, mock_knowledge_base_dao, mock_integration_dao, 
        sample_user_data, sample_integration, sample_kb
    ):
        """Test get_local_graph with new entry creation."""
        request = GetGraphRequest(
            path=PATH_TO_LOCAL,
            remote_url=PATH_TO_REMOTE,
            branch=MAIN_BRANCH,
            total_files=FILE_COUNT_100
        )
        
        # Mock no existing KB
        mock_knowledge_base_dao.exist_by_graph_id.return_value = None
        mock_integration_dao.get_by_type.return_value = sample_integration
        mock_knowledge_base_dao.create_knowledge_base.return_value = sample_kb
        
        with patch(SERVICE_GRANT_ACCESS_PATCH):
            result = await graph_service.get_local_graph(request, sample_user_data)
            
            assert result.success is True
            assert GRAPH_GENERATED_MESSAGE in result.data["message"]
            mock_knowledge_base_dao.create_knowledge_base.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_local_graph_kb_name_from_remote_path(
        self, graph_service, mock_knowledge_base_dao, mock_integration_dao, 
        sample_user_data, sample_integration, sample_kb
    ):
        """Test get_local_graph extracts KB name from remote path."""
        request = GetGraphRequest(
            path=PATH_TO_LOCAL,
            remote_url=GITHUB_TEST_REPO,
            branch=MAIN_BRANCH,
            total_files=FILE_COUNT_100
        )
        
        # Mock no existing KB
        mock_knowledge_base_dao.exist_by_graph_id.return_value = None
        mock_integration_dao.get_by_type.return_value = sample_integration
        mock_knowledge_base_dao.create_knowledge_base.return_value = sample_kb
        
        with patch(SERVICE_GRANT_ACCESS_PATCH):
            result = await graph_service.get_local_graph(request, sample_user_data)
            
            assert result.success is True
            mock_knowledge_base_dao.create_knowledge_base.assert_called_once()
            # Verify KB name is extracted from remote path
            call_args = mock_knowledge_base_dao.create_knowledge_base.call_args[1]
            assert call_args["name"] == TEST_REPO_NAME
    
    @pytest.mark.asyncio
    async def test_get_local_graph_kb_name_from_local_path(
        self, graph_service, mock_knowledge_base_dao, mock_integration_dao, 
        sample_user_data, sample_integration, sample_kb
    ):
        """Test get_local_graph extracts KB name from local path when no remote path."""
        request = GetGraphRequest(
            path=PATH_TO_LOCAL_PROJECT,
            branch=MAIN_BRANCH,
            total_files=FILE_COUNT_100
        )
        
        # Mock no existing KB
        mock_knowledge_base_dao.exist_by_graph_id.return_value = None
        mock_integration_dao.get_by_type.return_value = sample_integration
        mock_knowledge_base_dao.create_knowledge_base.return_value = sample_kb
        
        with patch(SERVICE_GRANT_ACCESS_PATCH):
            result = await graph_service.get_local_graph(request, sample_user_data)
            
            assert result.success is True
            mock_knowledge_base_dao.create_knowledge_base.assert_called_once()
            # Verify KB name is extracted from local path
            call_args = mock_knowledge_base_dao.create_knowledge_base.call_args[1]
            assert call_args["name"] == LOCAL_PROJECT_NAME
    
    @pytest.mark.asyncio
    async def test_sync_graph_file_success(
        self, graph_service, mock_knowledge_base_dao, mock_ingestion_run_dao,
        sample_user_data, sample_kb, sample_ingestion_run
    ):
        """Test successful sync_graph_file operation."""
        request = SyncGraphFileRequest(
            graph_id=TEST_GRAPH_ID,
            file_path=PATH_TO_FILE_PY,
            content=PRINT_HELLO,
            mode=CREATE_MODE
        )
        
        mock_connection_handler = Mock(spec=ConnectionHandler)
        mock_connection_handler.event_emitter.emit = AsyncMock()
        mock_connection_handler.session.commit = AsyncMock()
        
        mock_knowledge_base_dao.exist_by_graph_id.return_value = sample_kb
        mock_ingestion_run_dao.get_latest_ingestion_run_by_kb_id.return_value = sample_ingestion_run
        
        with patch(SERVICE_IS_IGNORED_PATCH, return_value=False):
            result = await graph_service.sync_graph_file(
                request, mock_connection_handler, sample_user_data
            )
            
            assert result.status == SUCCESS_STATUS
            assert result.message == FILE_SYNCED_MESSAGE
            mock_connection_handler.event_emitter.emit.assert_called_once()
            mock_connection_handler.session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_sync_graph_file_kb_not_found(
        self, graph_service, mock_knowledge_base_dao, sample_user_data
    ):
        """Test sync_graph_file with non-existent knowledge base."""
        request = SyncGraphFileRequest(
            graph_id=NON_EXISTENT_GRAPH_ID,
            file_path=PATH_TO_FILE_PY,
            content=PRINT_HELLO,
            mode=CREATE_MODE
        )
        
        mock_connection_handler = Mock(spec=ConnectionHandler)
        mock_knowledge_base_dao.exist_by_graph_id.return_value = None
        
        with pytest.raises(CustomException, match=KB_NOT_FOUND_ERROR):
            await graph_service.sync_graph_file(
                request, mock_connection_handler, sample_user_data
            )
    
    @pytest.mark.asyncio
    async def test_sync_graph_file_ignored_file(
        self, graph_service, mock_knowledge_base_dao, sample_user_data, sample_kb
    ):
        """Test sync_graph_file with ignored file."""
        request = SyncGraphFileRequest(
            graph_id=TEST_GRAPH_ID,
            file_path=PATH_TO_IGNORED_FILE,
            content=LOG_CONTENT,
            mode=CREATE_MODE
        )
        
        mock_connection_handler = Mock(spec=ConnectionHandler)
        mock_connection_handler.session.commit = AsyncMock()
        
        mock_knowledge_base_dao.exist_by_graph_id.return_value = sample_kb
        
        with patch(SERVICE_IS_IGNORED_PATCH, return_value=True):
            result = await graph_service.sync_graph_file(
                request, mock_connection_handler, sample_user_data
            )
            
            assert result.status == SUCCESS_STATUS
            # Should commit but not emit event for ignored files
            mock_connection_handler.session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_sync_graph_file_create_new_ingestion_run(
        self, graph_service, mock_knowledge_base_dao, mock_ingestion_run_dao,
        sample_user_data, sample_kb
    ):
        """Test sync_graph_file creates new ingestion run when none exists."""
        request = SyncGraphFileRequest(
            graph_id=TEST_GRAPH_ID,
            file_path=PATH_TO_FILE_PY,
            content=PRINT_HELLO,
            mode=CREATE_MODE
        )
        
        mock_connection_handler = Mock(spec=ConnectionHandler)
        mock_connection_handler.event_emitter.emit = AsyncMock()
        mock_connection_handler.session.commit = AsyncMock()
        
        new_ingestion_run = Mock(spec=IngestionRun)
        new_ingestion_run.id = ID_COUNT_2
        
        mock_knowledge_base_dao.exist_by_graph_id.return_value = sample_kb
        mock_ingestion_run_dao.get_latest_ingestion_run_by_kb_id.return_value = None
        mock_ingestion_run_dao.create_ingestion_run.return_value = new_ingestion_run
        
        with patch(SERVICE_IS_IGNORED_PATCH, return_value=False):
            result = await graph_service.sync_graph_file(
                request, mock_connection_handler, sample_user_data
            )
            
            assert result.status == SUCCESS_STATUS
            mock_ingestion_run_dao.create_ingestion_run.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_sync_status_success(
        self, graph_service, mock_knowledge_base_dao, mock_ingestion_run_dao, sample_kb
    ):
        """Test get_sync_status with successful status calculation."""
        request = GetIndexingStatusRequest(graph_id=TEST_GRAPH_ID)
        
        sample_kb.total_files = FILE_COUNT_100
        stats_json = {TOTAL_SOURCE_ITEM_KEY: FILE_COUNT_75}
        
        mock_knowledge_base_dao.exist_by_graph_id.return_value = sample_kb
        mock_ingestion_run_dao.get_latest_stats_json_by_kb_id.return_value = stats_json
        
        result = await graph_service.get_sync_status(request)
        
        assert result.success is True
        assert result.data["percentage"] == PERCENTAGE_75
        assert result.data["status"] == IN_PROGRESS_STATUS
    
    @pytest.mark.asyncio
    async def test_get_sync_status_completed(
        self, graph_service, mock_knowledge_base_dao, mock_ingestion_run_dao, sample_kb
    ):
        """Test get_sync_status with completed status."""
        request = GetIndexingStatusRequest(graph_id=TEST_GRAPH_ID)
        
        sample_kb.total_files = FILE_COUNT_100
        stats_json = {TOTAL_SOURCE_ITEM_KEY: FILE_COUNT_100}
        
        mock_knowledge_base_dao.exist_by_graph_id.return_value = sample_kb
        mock_ingestion_run_dao.get_latest_stats_json_by_kb_id.return_value = stats_json
        
        result = await graph_service.get_sync_status(request)
        
        assert result.success is True
        assert result.data["percentage"] == PERCENTAGE_100
        assert result.data["status"] == COMPLETED_STATUS
    
    @pytest.mark.asyncio
    async def test_get_sync_status_no_stats(
        self, graph_service, mock_knowledge_base_dao, mock_ingestion_run_dao, sample_kb
    ):
        """Test get_sync_status with no stats available."""
        request = GetIndexingStatusRequest(graph_id=TEST_GRAPH_ID)
        
        sample_kb.total_files = FILE_COUNT_100
        
        mock_knowledge_base_dao.exist_by_graph_id.return_value = sample_kb
        mock_ingestion_run_dao.get_latest_stats_json_by_kb_id.return_value = None
        
        result = await graph_service.get_sync_status(request)
        
        assert result.success is True
        assert result.data["percentage"] == PERCENTAGE_0
        assert result.data["status"] == IN_PROGRESS_STATUS
    
    @pytest.mark.asyncio
    async def test_get_sync_status_zero_total_files(
        self, graph_service, mock_knowledge_base_dao, mock_ingestion_run_dao, sample_kb
    ):
        """Test get_sync_status with zero total files."""
        request = GetIndexingStatusRequest(graph_id=TEST_GRAPH_ID)
        
        sample_kb.total_files = 0
        
        mock_knowledge_base_dao.exist_by_graph_id.return_value = sample_kb
        mock_ingestion_run_dao.get_latest_stats_json_by_kb_id.return_value = {}
        
        result = await graph_service.get_sync_status(request)
        
        assert result.success is True
        assert result.data["percentage"] == PERCENTAGE_0
    
    @pytest.mark.asyncio
    async def test_get_sync_status_graph_not_found(
        self, graph_service, mock_knowledge_base_dao
    ):
        """Test get_sync_status with non-existent graph."""
        request = GetIndexingStatusRequest(graph_id=NON_EXISTENT_GRAPH_ID)
        
        mock_knowledge_base_dao.exist_by_graph_id.return_value = None
        
        with pytest.raises(CustomException, match=GRAPH_NOT_FOUND_ERROR):
            await graph_service.get_sync_status(request)
    
    @pytest.mark.asyncio
    async def test_keyword_search_success(
        self, graph_service, mock_vector_db_adapter, mock_knowledge_base_dao, sample_kb
    ):
        """Test successful keyword search."""
        request = KeywordSearchRequest(
            graph_id=TEST_GRAPH_ID,
            keywords=SEARCH_KEYWORDS,
            max_results=MAX_RESULTS_10
        )
        
        search_results = {
            "hits": {
                "hits": [
                    {
                        "_id": DOC_ID_1,
                        "_source": {
                            "title": TEST_FILE,
                            "content": "Test content with query",
                            "metadata": {
                                "path": TEST_FILE_PY,
                                "type": FILE_TYPE,
                                "start_line": START_LINE_1,
                                "end_line": END_LINE_10
                            }
                        },
                        "_score": SCORE_0_9
                    }
                ]
            }
        }
        
        mock_knowledge_base_dao.exist_by_graph_id.return_value = sample_kb
        mock_vector_db_adapter.keyword_search_source_code.return_value = search_results
        
        result = await graph_service.keyword_search(request)
        
        assert result.success is True
        assert isinstance(result.data, list)
        assert len(result.data) == 1
        assert result.data[0]["path"] == TEST_FILE_PY
        mock_vector_db_adapter.keyword_search_source_code.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_keyword_search_kb_not_found(
        self, graph_service, mock_knowledge_base_dao
    ):
        """Test keyword search with non-existent knowledge base."""
        request = KeywordSearchRequest(
            graph_id=NON_EXISTENT_GRAPH_ID,
            keywords=SEARCH_KEYWORDS,
            max_results=MAX_RESULTS_10
        )
        
        mock_knowledge_base_dao.exist_by_graph_id.return_value = None
        
        result = await graph_service.keyword_search(request)
        
        assert result.success is False
    
    @pytest.mark.asyncio
    async def test_vector_search_success(
        self, graph_service, mock_vector_db_adapter, mock_knowledge_base_dao, sample_kb
    ):
        """Test successful vector search."""
        request = VectorSearchRequest(
            graph_id=TEST_GRAPH_ID,
            query="test query",
            max_results=MAX_RESULTS_10,
            threshold=THRESHOLD_70
        )
        
        search_results = {
            "hits": {
                "hits": [
                    {
                        "_id": DOC_ID_1,
                        "_source": {
                            "title": TEST_FILE,
                            "content": "Test content",
                            "metadata": {
                                "path": TEST_FILE_PY,
                                "type": FILE_TYPE,
                                "start_line": START_LINE_1,
                                "end_line": END_LINE_10
                            }
                        },
                        "_score": SCORE_0_9
                    }
                ]
            }
        }
        
        mock_knowledge_base_dao.exist_by_graph_id.return_value = sample_kb
        mock_vector_db_adapter.knn_similarity_search.return_value = search_results
        
        result = await graph_service.vector_search(request)
        
        assert result.success is True
        assert isinstance(result.data, list)
        assert len(result.data) == 1
        assert result.data[0]["path"] == TEST_FILE_PY
        mock_vector_db_adapter.knn_similarity_search.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_vector_search_kb_not_found(
        self, graph_service, mock_knowledge_base_dao
    ):
        """Test vector search with non-existent knowledge base."""
        request = VectorSearchRequest(
            graph_id=NON_EXISTENT_GRAPH_ID,
            query="test query",
            max_results=MAX_RESULTS_10
        )
        
        mock_knowledge_base_dao.exist_by_graph_id.return_value = None
        
        result = await graph_service.vector_search(request)
        
        assert result.success is False
    
    @pytest.mark.asyncio
    async def test_get_file_content_success(
        self, graph_service, mock_vector_db_adapter, mock_knowledge_base_dao, sample_kb
    ):
        """Test successful get_file_content."""
        request = GetFileContentRequest(
            graph_id=TEST_GRAPH_ID,
            file_path=PATH_TO_FILE_PY
        )
        
        mock_knowledge_base_dao.exist_by_graph_id.return_value = sample_kb
        mock_vector_db_adapter.get_file_content.return_value = PRINT_HELLO_WORLD
        
        result = await graph_service.get_file_content(request)
        
        assert result.success is True
        assert result.data["content"] == PRINT_HELLO_WORLD
    
    @pytest.mark.asyncio
    async def test_get_file_content_not_found(
        self, graph_service, mock_vector_db_adapter, mock_knowledge_base_dao, sample_kb
    ):
        """Test get_file_content with file not found."""
        request = GetFileContentRequest(
            graph_id=TEST_GRAPH_ID,
            file_path=PATH_TO_NONEXISTENT_PY
        )
        
        mock_knowledge_base_dao.exist_by_graph_id.return_value = sample_kb
        mock_vector_db_adapter.get_file_content.return_value = None
        
        result = await graph_service.get_file_content(request)
        
        assert result.success is False
    
    @pytest.mark.asyncio
    async def test_get_file_content_kb_not_found(
        self, graph_service, mock_knowledge_base_dao
    ):
        """Test get_file_content with non-existent knowledge base."""
        request = GetFileContentRequest(
            graph_id=NON_EXISTENT_GRAPH_ID,
            file_path=PATH_TO_FILE_PY
        )
        
        mock_knowledge_base_dao.exist_by_graph_id.return_value = None
        
        result = await graph_service.get_file_content(request)
        
        assert result.success is False
    
    @pytest.mark.asyncio
    async def test_get_folder_structure_success(
        self, graph_service, mock_vector_db_adapter, mock_knowledge_base_dao, sample_kb
    ):
        """Test successful get_folder_structure."""
        # Mock the knowledge base memory structure instead of search hits
        folder_structure = {
            "": {
                "folder1": {
                    FILE1_PY: None
                },
                "folder2": {
                    FILE2_PY: None
                }
            }
        }
        sample_kb.memory = {FOLDER_STRUCTURE_KEY: folder_structure}
        
        mock_knowledge_base_dao.exist_by_graph_id.return_value = sample_kb
        
        result = await graph_service.get_folder_structure(TEST_GRAPH_ID)
        
        assert result.success is True
        assert FOLDER_STRUCTURE_KEY in result.data
        # The method reads from memory, not vector search
        mock_vector_db_adapter.keyword_search_source_code.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_get_folder_structure_with_folder_path(
        self, graph_service, mock_vector_db_adapter, mock_knowledge_base_dao, sample_kb
    ):
        """Test get_folder_structure with specific folder path."""
        # Mock the knowledge base memory structure with nested folder
        folder_structure = {
            "": {
                ROOT_FOLDER: {
                    SPECIFIC_FOLDER: {
                        FILE1_PY: None
                    }
                }
            }
        }
        sample_kb.memory = {FOLDER_STRUCTURE_KEY: folder_structure}
        
        mock_knowledge_base_dao.exist_by_graph_id.return_value = sample_kb
        
        result = await graph_service.get_folder_structure(TEST_GRAPH_ID, ROOT_SPECIFIC_PATH)
        
        assert result.success is True
        assert FOLDER_STRUCTURE_KEY in result.data
    
    @pytest.mark.asyncio
    async def test_get_folder_structure_kb_not_found(
        self, graph_service, mock_knowledge_base_dao
    ):
        """Test get_folder_structure with non-existent knowledge base."""
        mock_knowledge_base_dao.exist_by_graph_id.return_value = None
        
        result = await graph_service.get_folder_structure(NON_EXISTENT_GRAPH_ID)
        
        assert result.success is False
    
    @pytest.mark.asyncio
    async def test_build_search_results(self, graph_service):
        """Test _build_search_results method."""
        hits = [
            {
                "_id": DOC_ID_1,
                "_source": {
                    "title": FILE1_PY,
                    "content": TEST_CONTENT_1,
                    "metadata": {
                        "path": PATH_TO_FILE1_PY, 
                        "type": FILE_TYPE,
                        "start_line": START_LINE_1, 
                        "end_line": END_LINE_10
                    }
                },
                "_score": SCORE_0_9
            },
            {
                "_id": DOC_ID_2,
                "_source": {
                    "title": FILE2_PY,
                    "content": TEST_CONTENT_2,
                    "metadata": {
                        "path": PATH_TO_FILE2_PY, 
                        "type": FILE_TYPE,
                        "start_line": START_LINE_5, 
                        "end_line": END_LINE_15
                    }
                },
                "_score": SCORE_0_8
            }
        ]
        
        result = await graph_service._build_search_results(hits, TEST_GRAPH_ID)
        
        assert len(result) == ID_COUNT_2
        assert result[0]["path"] == PATH_TO_FILE1_PY
        assert result[0]["source_code"] == TEST_CONTENT_1
        assert result[0]["graph_id"] == TEST_GRAPH_ID
        assert result[1]["path"] == PATH_TO_FILE2_PY
    
    @pytest.mark.asyncio
    async def test_build_search_results_with_threshold(self, graph_service):
        """Test _build_search_results with threshold filtering."""
        hits = [
            {
                "_id": DOC_ID_1,
                "_source": {
                    "title": FILE1_PY,
                    "content": TEST_CONTENT_1,
                    "metadata": {"path": PATH_TO_FILE1_PY, "type": FILE_TYPE}
                },
                "_score": SCORE_0_9
            },
            {
                "_id": DOC_ID_2,
                "_source": {
                    "title": FILE2_PY,
                    "content": TEST_CONTENT_2,
                    "metadata": {"path": PATH_TO_FILE2_PY, "type": FILE_TYPE}
                },
                "_score": SCORE_0_5  # Below threshold
            }
        ]
        
        result = await graph_service._build_search_results(hits, TEST_GRAPH_ID, threshold=THRESHOLD_70)
        
        # Should only return results above threshold (70% = 0.7)
        assert len(result) == 1
        assert result[0]["path"] == PATH_TO_FILE1_PY
    
    @pytest.mark.asyncio
    async def test_build_search_results_empty(self, graph_service):
        """Test _build_search_results with empty hits."""
        result = await graph_service._build_search_results([], TEST_GRAPH_ID)
        
        assert result == []


class TestGraphServiceIntegration:
    """Integration tests for GraphService."""
    
    @pytest.mark.asyncio
    async def test_graph_service_end_to_end_workflow(self):
        """Test a complete workflow from graph creation to search."""
        # Mock all dependencies
        mock_vector_db = Mock(spec=VectorDBAdapter)
        mock_kb_dao = Mock(spec=KnowledgeBaseDao)
        mock_integration_dao = Mock(spec=IntegrationDao)
        mock_ingestion_dao = Mock(spec=IngestionRunDao)
        
        service = GraphService(
            vector_db_adapter=mock_vector_db,
            knowledge_base_dao=mock_kb_dao,
            integration_dao=mock_integration_dao,
            ingestion_run_dao=mock_ingestion_dao
        )
        
        # Test service initialization
        assert service.vector_db_adapter is mock_vector_db
        assert service.knowledge_base_dao is mock_kb_dao
        assert service.integration_dao is mock_integration_dao
        assert service.ingestion_run_dao is mock_ingestion_dao
    
    def test_graph_id_generation_consistency(self):
        """Test that graph ID generation is consistent for same inputs."""
        user_data = UserData(
            userId=USER_ID_123, 
            orgId=ORG_ID_456,
            _id=USER_INTERNAL_ID,
            email=EMAIL_TEST,
            firstName=FIRST_NAME_TEST,
            lastName=LAST_NAME_USER
        )
        local_path = PATH_TO_LOCAL
        remote_path = PATH_TO_REMOTE
        
        # Generate graph ID manually to test consistency
        unique_str = f"{str(user_data.userId)}_{local_path}_{remote_path}"
        expected_graph_id = hashlib.sha256(unique_str.encode()).hexdigest()
        
        # This should match the logic in get_local_graph
        assert len(expected_graph_id) == SHA256_HEX_LENGTH  # SHA256 hex digest length
        assert isinstance(expected_graph_id, str)
    
    @pytest.mark.asyncio
    async def test_error_handling_consistency(self):
        """Test that all methods handle missing knowledge bases consistently."""
        mock_vector_db = Mock(spec=VectorDBAdapter)
        mock_kb_dao = Mock(spec=KnowledgeBaseDao)
        mock_integration_dao = Mock(spec=IntegrationDao)
        mock_ingestion_dao = Mock(spec=IngestionRunDao)
        
        service = GraphService(
            vector_db_adapter=mock_vector_db,
            knowledge_base_dao=mock_kb_dao,
            integration_dao=mock_integration_dao,
            ingestion_run_dao=mock_ingestion_dao
        )
        
        # Mock KB not found for all methods
        mock_kb_dao.exist_by_graph_id.return_value = None
        
        # Test methods that should return failed response data
        search_request = KeywordSearchRequest(
            graph_id="test", 
            keywords=["test"], 
            max_results=MAX_RESULTS_10
        )
        keyword_result = await service.keyword_search(search_request)
        assert keyword_result.success is False
        
        vector_request = VectorSearchRequest(
            graph_id="test", 
            query="test", 
            max_results=MAX_RESULTS_10
        )
        vector_result = await service.vector_search(vector_request)
        assert vector_result.success is False
        
        file_request = GetFileContentRequest(
            graph_id="test", 
            file_path="/test"
        )
        file_result = await service.get_file_content(file_request)
        assert file_result.success is False
        
        folder_result = await service.get_folder_structure("test")
        assert folder_result.success is False 