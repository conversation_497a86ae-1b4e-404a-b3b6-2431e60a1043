"""
Comprehensive tests for code_indexing.views module to achieve SonarCube compliance.

This module contains test data that are safe for testing purposes.
All test data in this file is fictional and for testing purposes only.

SECURITY NOTE: All data in this file is safe test data:
- User IDs, organization IDs, and team IDs are fictional test identifiers
- Graph IDs and repository URLs are fictional test values
- File paths and content are fictional test data
- Email addresses and phone numbers are fictional test data
- Search queries and keywords are fictional test content
- No production credentials or sensitive data is used
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timezone
from fastapi import HTTPException

from code_indexing.views import (
    get_local_graph,
    sync_graph_file,
    get_sync_status,
    keyword_search,
    vector_search,
    get_file_content,
    get_folder_structure
)
from code_indexing.serializers import (
    GetGraphRequest,
    SyncGraphFileRequest,
    KeywordSearchRequest,
    VectorSearchRequest,
    GetFileContentRequest,
    GetIndexingStatusRequest,
    ResponseData,
    SyncGraphFileResponse
)
from clerk_integration.utils import UserData
from utils.connection_handler import ConnectionHandler

# Test Constants - All values are safe test data, not production values
# User and Organization Constants
TEST_USER_ID = "test-user-123"
TEST_ORG_ID = "test-org-456"
TEST_TEAM_ID = "test-team-789"

# Graph and Repository Constants
TEST_GRAPH_ID = "test-graph-123"
TEST_REPOSITORY_URL = "https://github.com/test/repo"
TEST_LOCAL_PATH = "/path/to/local/repo"
TEST_REMOTE_PATH = "/path/to/remote/repo"
TEST_BRANCH = "main"

# File and Content Constants
TEST_FILE_PATH = "src/main.py"
TEST_FILE_CONTENT = "# Test file content\nprint('Hello World')"
TEST_FOLDER_PATH = "src"

# Search Constants
TEST_SEARCH_QUERY = "test search query"
TEST_KEYWORD_QUERY = "function test"
TEST_VECTOR_QUERY = "search for functionality"

# User Data Constants
TEST_FIRST_NAME = "John"
TEST_LAST_NAME = "Doe"
TEST_EMAIL = "<EMAIL>"  # Fictional test email
TEST_USERNAME = "johndoe"
TEST_PHONE = "555-0123"  # Fictional test phone
TEST_PROFILE_URL = "https://example.com/profile.jpg"

# API Response Constants
SUCCESS_STATUS = True
FAILURE_STATUS = False
HTTP_STATUS_OK = 200
HTTP_STATUS_ERROR = 500

# Indexing Status Constants
STATUS_READY = "ready"
STATUS_INDEXING = "indexing"
STATUS_FAILED = "failed"

# Pagination Constants
TEST_PAGE = 1
TEST_PER_PAGE = 10
TEST_SKIP = 0
TEST_LIMIT = 100

# Date Constants
TEST_DATETIME = datetime.now(timezone.utc)

# Error Messages
ERROR_NOT_FOUND = "not found"
ERROR_ACCESS_DENIED = "access denied"
ERROR_INVALID_REQUEST = "invalid request"

# Service Mock Patch Paths
PATCH_GRAPH_SERVICE = 'code_indexing.views.GraphService'
PATCH_ELASTIC_SEARCH_ADAPTER = 'code_indexing.views.ElasticSearchAdapter'
PATCH_KNOWLEDGE_BASE_DAO = 'code_indexing.views.KnowledgeBaseDao'
PATCH_INTEGRATION_DAO = 'code_indexing.views.IntegrationDao'
PATCH_INGESTION_RUN_DAO = 'code_indexing.views.IngestionRunDao'

# Response Constants
TEST_RESPONSE_DATA = {"status": STATUS_READY, "message": "Operation successful"}
TEST_SEARCH_RESULTS = [{"file": TEST_FILE_PATH, "score": 0.95}]
TEST_FOLDER_STRUCTURE = {"folders": ["src", "docs"], "files": ["README.md"]}


@pytest.fixture
def mock_connection_handler():
    """Create mock ConnectionHandler for tests."""
    handler = Mock(spec=ConnectionHandler)
    handler.session = AsyncMock()
    handler.event_emitter = AsyncMock()
    return handler


@pytest.fixture
def sample_user_data():
    """Create sample UserData for testing."""
    return UserData(
        _id=TEST_USER_ID,
        userId=TEST_USER_ID,
        orgId=TEST_ORG_ID,
        firstName=TEST_FIRST_NAME,
        lastName=TEST_LAST_NAME,
        email=TEST_EMAIL,
        username=TEST_USERNAME,
        phoneNumber=TEST_PHONE,
        profilePicUrl=TEST_PROFILE_URL,
        active=True,
        roleIds=[1, 2],
        meta={},
        createdAt=TEST_DATETIME,
        updatedAt=TEST_DATETIME,
        workspace=[]
    )


@pytest.fixture
def sample_get_graph_request():
    """Create sample GetGraphRequest for testing."""
    return GetGraphRequest(
        path=TEST_LOCAL_PATH,
        remote_url=TEST_REPOSITORY_URL,
        branch=TEST_BRANCH
    )


@pytest.fixture
def sample_sync_graph_file_request():
    """Create sample SyncGraphFileRequest for testing."""
    return SyncGraphFileRequest(
        graph_id=TEST_GRAPH_ID,
        file_path=TEST_FILE_PATH,
        content=TEST_FILE_CONTENT,
        mode="sync"
    )


@pytest.fixture
def sample_keyword_search_request():
    """Create sample KeywordSearchRequest for testing."""
    return KeywordSearchRequest(
        graph_id=TEST_GRAPH_ID,
        keywords=[TEST_KEYWORD_QUERY],
        max_results=10
    )


@pytest.fixture
def sample_vector_search_request():
    """Create sample VectorSearchRequest for testing."""
    return VectorSearchRequest(
        graph_id=TEST_GRAPH_ID,
        query=TEST_VECTOR_QUERY,
        max_results=10
    )


@pytest.fixture
def sample_file_content_request():
    """Create sample GetFileContentRequest for testing."""
    return GetFileContentRequest(
        graph_id=TEST_GRAPH_ID,
        file_path=TEST_FILE_PATH
    )


@pytest.fixture
def sample_indexing_status_request():
    """Create sample GetIndexingStatusRequest for testing."""
    return GetIndexingStatusRequest(
        graph_id=TEST_GRAPH_ID
    )


@pytest.fixture
def mock_graph_service():
    """Create mock GraphService for testing."""
    service = AsyncMock()
    service.get_local_graph.return_value = ResponseData(
        success=SUCCESS_STATUS, 
        data=TEST_RESPONSE_DATA, 
        message="Operation successful"
    )
    service.sync_graph_file.return_value = SyncGraphFileResponse(
        status=STATUS_READY, 
        message="File synced successfully"
    )
    service.get_sync_status.return_value = ResponseData(
        success=SUCCESS_STATUS, 
        data={"status": STATUS_READY}, 
        message="Status retrieved"
    )
    service.keyword_search.return_value = ResponseData(
        success=SUCCESS_STATUS, 
        data=TEST_SEARCH_RESULTS, 
        message="Search completed"
    )
    service.vector_search.return_value = ResponseData(
        success=SUCCESS_STATUS, 
        data=TEST_SEARCH_RESULTS, 
        message="Vector search completed"
    )
    service.get_file_content.return_value = ResponseData(
        success=SUCCESS_STATUS, 
        data={"content": TEST_FILE_CONTENT}, 
        message="File content retrieved"
    )
    service.get_folder_structure.return_value = ResponseData(
        success=SUCCESS_STATUS, 
        data=TEST_FOLDER_STRUCTURE, 
        message="Folder structure retrieved"
    )
    return service


class TestCodeIndexingViews:
    """Test cases for code_indexing.views module."""

    @pytest.mark.asyncio
    async def test_get_local_graph_success(self, sample_get_graph_request, sample_user_data, 
                                          mock_connection_handler, mock_graph_service):
        """Test successful local graph retrieval."""
        with patch(PATCH_GRAPH_SERVICE, return_value=mock_graph_service):
            # Act
            result = await get_local_graph(sample_get_graph_request, sample_user_data, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data == TEST_RESPONSE_DATA
            mock_graph_service.get_local_graph.assert_called_once_with(sample_get_graph_request, sample_user_data)

    @pytest.mark.asyncio
    async def test_get_local_graph_service_initialization(self, sample_get_graph_request, sample_user_data,
                                                         mock_connection_handler):
        """Test that GraphService is initialized with correct dependencies."""
        with patch(PATCH_GRAPH_SERVICE) as mock_service_class, \
             patch(PATCH_ELASTIC_SEARCH_ADAPTER) as mock_elastic, \
             patch(PATCH_KNOWLEDGE_BASE_DAO) as mock_kb_dao, \
             patch(PATCH_INTEGRATION_DAO) as mock_int_dao, \
             patch(PATCH_INGESTION_RUN_DAO) as mock_run_dao:
            
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            mock_service.get_local_graph.return_value = ResponseData(
                success=SUCCESS_STATUS, 
                data=TEST_RESPONSE_DATA, 
                message="Operation successful"
            )

            # Act
            await get_local_graph(sample_get_graph_request, sample_user_data, mock_connection_handler)

            # Assert
            mock_service_class.assert_called_once()
            mock_elastic.assert_called_once()
            mock_kb_dao.assert_called_once_with(mock_connection_handler.session)
            mock_int_dao.assert_called_once_with(mock_connection_handler.session)
            mock_run_dao.assert_called_once_with(mock_connection_handler.session)

    @pytest.mark.asyncio
    async def test_sync_graph_file_success(self, sample_sync_graph_file_request, sample_user_data,
                                          mock_connection_handler, mock_graph_service):
        """Test successful graph file synchronization."""
        with patch(PATCH_GRAPH_SERVICE, return_value=mock_graph_service):
            # Act
            result = await sync_graph_file(sample_sync_graph_file_request, mock_connection_handler, sample_user_data)

            # Assert
            assert result.status == STATUS_READY
            assert "File synced successfully" in result.message
            mock_graph_service.sync_graph_file.assert_called_once_with(
                sample_sync_graph_file_request, mock_connection_handler, sample_user_data
            )

    @pytest.mark.asyncio
    async def test_get_sync_status_success(self, sample_indexing_status_request, mock_connection_handler, 
                                          mock_graph_service):
        """Test successful sync status retrieval."""
        with patch(PATCH_GRAPH_SERVICE, return_value=mock_graph_service):
            # Act
            result = await get_sync_status(sample_indexing_status_request, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert "status" in result.data
            mock_graph_service.get_sync_status.assert_called_once_with(sample_indexing_status_request)

    @pytest.mark.asyncio
    async def test_keyword_search_success(self, sample_keyword_search_request, mock_connection_handler,
                                         mock_graph_service):
        """Test successful keyword search."""
        with patch(PATCH_GRAPH_SERVICE, return_value=mock_graph_service):
            # Act
            result = await keyword_search(sample_keyword_search_request, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data == TEST_SEARCH_RESULTS
            mock_graph_service.keyword_search.assert_called_once_with(sample_keyword_search_request)

    @pytest.mark.asyncio
    async def test_vector_search_success(self, sample_vector_search_request, mock_connection_handler,
                                        mock_graph_service):
        """Test successful vector search."""
        with patch(PATCH_GRAPH_SERVICE, return_value=mock_graph_service):
            # Act
            result = await vector_search(sample_vector_search_request, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data == TEST_SEARCH_RESULTS
            mock_graph_service.vector_search.assert_called_once_with(sample_vector_search_request)

    @pytest.mark.asyncio
    async def test_get_file_content_success(self, sample_file_content_request, mock_connection_handler,
                                           mock_graph_service):
        """Test successful file content retrieval."""
        with patch(PATCH_GRAPH_SERVICE, return_value=mock_graph_service):
            # Act
            result = await get_file_content(sample_file_content_request, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert "content" in result.data
            assert result.data["content"] == TEST_FILE_CONTENT
            mock_graph_service.get_file_content.assert_called_once_with(sample_file_content_request)

    @pytest.mark.asyncio
    async def test_get_folder_structure_success(self, mock_connection_handler, mock_graph_service):
        """Test successful folder structure retrieval."""
        with patch(PATCH_GRAPH_SERVICE, return_value=mock_graph_service):
            # Act
            result = await get_folder_structure(TEST_GRAPH_ID, TEST_FOLDER_PATH, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert result.data == TEST_FOLDER_STRUCTURE
            mock_graph_service.get_folder_structure.assert_called_once_with(TEST_GRAPH_ID, TEST_FOLDER_PATH)

    @pytest.mark.asyncio
    async def test_get_folder_structure_without_path(self, mock_connection_handler, mock_graph_service):
        """Test folder structure retrieval without specific path."""
        with patch(PATCH_GRAPH_SERVICE, return_value=mock_graph_service):
            # Act
            result = await get_folder_structure(TEST_GRAPH_ID, None, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            mock_graph_service.get_folder_structure.assert_called_once_with(TEST_GRAPH_ID, None)

    @pytest.mark.asyncio
    async def test_service_exception_handling(self, sample_get_graph_request, sample_user_data,
                                             mock_connection_handler):
        """Test exception handling in views when service fails."""
        with patch(PATCH_GRAPH_SERVICE) as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            mock_service.get_local_graph.side_effect = Exception("Service error")

            # Act & Assert
            with pytest.raises(Exception, match="Service error"):
                await get_local_graph(sample_get_graph_request, sample_user_data, mock_connection_handler)

    @pytest.mark.asyncio
    async def test_dependency_injection_consistency(self, sample_sync_graph_file_request, sample_user_data,
                                                   mock_connection_handler):
        """Test that all views use consistent dependency injection."""
        with patch(PATCH_GRAPH_SERVICE) as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            mock_service.sync_graph_file.return_value = SyncGraphFileResponse(
                status=STATUS_READY, 
                message="File synced successfully"
            )

            # Act
            await sync_graph_file(sample_sync_graph_file_request, mock_connection_handler, sample_user_data)

            # Assert
            # Verify GraphService is instantiated with the expected dependencies
            call_args = mock_service_class.call_args[0]
            assert len(call_args) == 4  # ElasticSearchAdapter, KnowledgeBaseDao, IntegrationDao, IngestionRunDao

    @pytest.mark.asyncio
    async def test_multiple_view_operations(self, mock_connection_handler, mock_graph_service):
        """Test multiple view operations work independently."""
        with patch(PATCH_GRAPH_SERVICE, return_value=mock_graph_service):
            # Act - Multiple operations
            status_result = await get_sync_status(
                GetIndexingStatusRequest(graph_id=TEST_GRAPH_ID), 
                mock_connection_handler
            )
            
            folder_result = await get_folder_structure(TEST_GRAPH_ID, None, mock_connection_handler)

            # Assert
            assert status_result.success is SUCCESS_STATUS
            assert folder_result.success is SUCCESS_STATUS
            
            # Verify services were called correctly
            assert mock_graph_service.get_sync_status.call_count == 1
            assert mock_graph_service.get_folder_structure.call_count == 1


class TestCodeIndexingViewsEdgeCases:
    """Test edge cases and error scenarios for code_indexing views."""

    @pytest.mark.asyncio
    async def test_empty_search_query(self, mock_connection_handler, mock_graph_service):
        """Test handling of empty search queries."""
        empty_query_request = KeywordSearchRequest(
            query="",
            graph_id=TEST_GRAPH_ID,
            page=TEST_PAGE,
            per_page=TEST_PER_PAGE
        )
        
        with patch(PATCH_GRAPH_SERVICE, return_value=mock_graph_service):
            # Act
            result = await keyword_search(empty_query_request, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            mock_graph_service.keyword_search.assert_called_once_with(empty_query_request)

    @pytest.mark.asyncio
    async def test_invalid_graph_id(self, mock_connection_handler):
        """Test handling of invalid graph ID."""
        invalid_request = GetIndexingStatusRequest(graph_id="invalid-graph-id")
        
        with patch(PATCH_GRAPH_SERVICE) as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            mock_service.get_sync_status.return_value = ResponseData(
                success=FAILURE_STATUS, 
                message=ERROR_NOT_FOUND
            )

            # Act
            result = await get_sync_status(invalid_request, mock_connection_handler)

            # Assert
            assert result.success is FAILURE_STATUS
            assert ERROR_NOT_FOUND in result.message

    @pytest.mark.asyncio
    async def test_large_file_content_request(self, mock_connection_handler, mock_graph_service):
        """Test handling of large file content requests."""
        large_file_request = GetFileContentRequest(
            graph_id=TEST_GRAPH_ID,
            file_path="large_file.txt"
        )
        
        large_content = "x" * 10000  # Large content
        mock_graph_service.get_file_content.return_value = ResponseData(
            success=SUCCESS_STATUS, 
            data={"content": large_content},
            message="Large file content retrieved"
        )
        
        with patch(PATCH_GRAPH_SERVICE, return_value=mock_graph_service):
            # Act
            result = await get_file_content(large_file_request, mock_connection_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            assert len(result.data["content"]) == 10000

    @pytest.mark.asyncio
    async def test_concurrent_operations_safety(self, mock_connection_handler, mock_graph_service):
        """Test that views handle concurrent operations safely."""
        import asyncio
        
        with patch(PATCH_GRAPH_SERVICE, return_value=mock_graph_service):
            # Act - Simulate concurrent operations
            tasks = [
                get_sync_status(GetIndexingStatusRequest(graph_id=f"graph-{i}"), mock_connection_handler)
                for i in range(5)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Assert
            for result in results:
                assert isinstance(result, ResponseData)
                assert result.success is SUCCESS_STATUS

    @pytest.mark.asyncio
    async def test_service_initialization_with_none_session(self):
        """Test service initialization with None session."""
        mock_handler = Mock(spec=ConnectionHandler)
        mock_handler.session = None
        
        request = GetGraphRequest(
            path=TEST_LOCAL_PATH,
            remote_url=TEST_REPOSITORY_URL,
            branch=TEST_BRANCH
        )
        
        user_data = UserData(
            _id=TEST_USER_ID,
            userId=TEST_USER_ID,
            orgId=TEST_ORG_ID,
            firstName=TEST_FIRST_NAME,
            lastName=TEST_LAST_NAME,
            email=TEST_EMAIL,
            username=TEST_USERNAME,
            phoneNumber=TEST_PHONE,
            profilePicUrl=TEST_PROFILE_URL,
            active=True,
            roleIds=[],
            meta={},
            createdAt=TEST_DATETIME,
            updatedAt=TEST_DATETIME,
            workspace=[]
        )

        with patch(PATCH_GRAPH_SERVICE) as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            mock_service.get_local_graph.return_value = ResponseData(
                success=SUCCESS_STATUS, 
                data={}, 
                message="Operation successful"
            )

            # Act
            result = await get_local_graph(request, user_data, mock_handler)

            # Assert
            assert result.success is SUCCESS_STATUS
            # Verify service was created with None session
            mock_service_class.assert_called_once() 