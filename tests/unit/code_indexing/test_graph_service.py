"""
Comprehensive tests for code_indexing.services.GraphService to achieve SonarCube compliance.

This module contains test data that are safe for testing purposes.
All test data in this file is fictional and for testing purposes only.

SECURITY NOTE: All data in this file is safe test data:
- User IDs and organization IDs are fictional test identifiers
- Email addresses are fictional test email addresses
- File paths and repository URLs are fictional test paths
- Graph IDs and integration IDs are fictional test values
- No production credentials or sensitive data is used
"""

from types import SimpleNamespace
from unittest.mock import AsyncMock, patch

import pytest

from code_indexing.serializers import (
    GetGraphRequest,
    KeywordSearchRequest,
    VectorSearchRequest,
    GetFileContentRequest,
    GetIndexingStatusRequest,
)
from code_indexing.services import GraphService
from clerk_integration.utils import UserData  # Stubbed in tests/conftest.py


# Test Constants - All values are safe test data, not production values
# User Data Constants
TEST_USER_ID = "user-123"
TEST_ORG_ID = "org-456"
TEST_FIRST_NAME = "Tester"
TEST_LAST_NAME = "McTest"
TEST_EMAIL = "<EMAIL>"  # Fictional test email

# File and Path Constants
TEST_LOCAL_PATH = "/local/path"
TEST_BRANCH_NAME = "main"
TEST_FILE_PATH = "src/utils/helpers.py"
TEST_MAIN_FILE = "src/main.py"
TEST_FOLDER_PATH = "src/utils"
TEST_FILE_CONTENT = "def foo(): pass"
TEST_PRINT_CONTENT = "print('hello')\n"

# Graph and Repository Constants
TEST_GRAPH_ID = "graph-id"
TEST_DUMMY_GRAPH_ID = "dummy-graph"
TEST_SHORT_GRAPH_ID = "gid"

# Integration and Database Constants
TEST_INTEGRATION_ID = 123
TEST_KB_ID_NEW = 999
TEST_KB_ID_EXISTING = 777
TEST_KB_ID_SMALL = 42
TEST_KB_ID_SINGLE = 11
TEST_KB_ID_DEFAULT = 1

# File Count Constants
TOTAL_FILES_NEW = 7
TOTAL_FILES_EXISTING = 3
TOTAL_FILES_UPDATED = 5
TOTAL_FILES_COMPLETED = 4

# Search and Score Constants
HIGH_SCORE = 1.0
LOW_SCORE = 0.1
THRESHOLD_PERCENTAGE = 50
COMPLETION_PERCENTAGE = 100

# Line Number Constants
START_LINE = 1
END_LINE = 1

# Status Constants
STATUS_COMPLETED = "COMPLETED"

# Search Keywords
SEARCH_KEYWORD = "foo"
SEARCH_QUERY = "irrelevant"

# Content Type Constants
FILE_TYPE = "file"

# Dictionary Keys
KEY_HITS = "hits"
KEY_SCORE = "_score"
KEY_SOURCE = "_source"
KEY_TITLE = "title"
KEY_CONTENT = "content"
KEY_METADATA = "metadata"
KEY_PATH = "path"
KEY_TYPE = "type"
KEY_START_LINE = "start_line"
KEY_END_LINE = "end_line"
KEY_GRAPH_ID = "graph_id"
KEY_TOTAL_FILES = "total_files"
KEY_STATUS = "status"
KEY_PERCENTAGE = "percentage"
KEY_TOTAL_SOURCE_ITEM = "total_source_item"
KEY_FOLDER_STRUCTURE = "folder_structure"

# Content Assertions
CONTENT_STARTS_WITH = "print"

# Mock Service Paths
LOCKSMITH_SERVICE_PATH = "code_indexing.services._call_grant_data_source_access"


@pytest.fixture
def test_user_data():
    """Create test user data."""
    return UserData(
        _id=TEST_USER_ID,
        userId=TEST_USER_ID,
        orgId=TEST_ORG_ID,
        firstName=TEST_FIRST_NAME,
        lastName=TEST_LAST_NAME,
        email=TEST_EMAIL,
    )


@pytest.fixture
def get_graph_request_new():
    """Create GetGraphRequest for new graph."""
    return GetGraphRequest(
        path=TEST_LOCAL_PATH,
        remote_url=None,
        branch=TEST_BRANCH_NAME,
        total_files=TOTAL_FILES_NEW
    )


@pytest.fixture
def get_graph_request_update():
    """Create GetGraphRequest for updating existing graph."""
    return GetGraphRequest(
        path=TEST_LOCAL_PATH,
        remote_url=None,
        branch=TEST_BRANCH_NAME,
        total_files=TOTAL_FILES_UPDATED
    )


@pytest.fixture
def keyword_search_request():
    """Create KeywordSearchRequest."""
    return KeywordSearchRequest(
        graph_id=TEST_GRAPH_ID,
        keywords=[SEARCH_KEYWORD],
        entire_workspace=True,
    )


@pytest.fixture
def vector_search_request():
    """Create VectorSearchRequest."""
    return VectorSearchRequest(
        graph_id=TEST_GRAPH_ID,
        query=SEARCH_QUERY,
        threshold=THRESHOLD_PERCENTAGE,
    )


@pytest.fixture
def get_file_content_request():
    """Create GetFileContentRequest."""
    return GetFileContentRequest(
        graph_id=TEST_SHORT_GRAPH_ID,
        file_path=TEST_MAIN_FILE
    )


@pytest.fixture
def get_indexing_status_request():
    """Create GetIndexingStatusRequest."""
    return GetIndexingStatusRequest(graph_id=TEST_DUMMY_GRAPH_ID)


@pytest.fixture()
def graph_service():
    """Return a GraphService instance with *all* collaborators mocked."""
    # Lazy async mocks for every dependency so we do not hit external systems.
    vector_db_adapter = AsyncMock()
    # Provide explicit AsyncMock attributes so we can assert on them reliably.
    vector_db_adapter.connect = AsyncMock()
    vector_db_adapter.close = AsyncMock()

    knowledge_base_dao = AsyncMock()
    knowledge_base_dao.update_graph_details = AsyncMock()
    integration_dao = AsyncMock()
    ingestion_run_dao = AsyncMock()

    service = GraphService(
        vector_db_adapter=vector_db_adapter,
        knowledge_base_dao=knowledge_base_dao,
        integration_dao=integration_dao,
        ingestion_run_dao=ingestion_run_dao,
    )
    return service, vector_db_adapter, knowledge_base_dao, integration_dao, ingestion_run_dao


@pytest.fixture()
def mock_search_hits():
    """Create mock search hits data."""
    return {
        KEY_HITS: {
            KEY_HITS: [
                {
                    KEY_SCORE: HIGH_SCORE,
                    KEY_SOURCE: {
                        KEY_TITLE: TEST_FILE_PATH,
                        KEY_CONTENT: TEST_FILE_CONTENT,
                        KEY_METADATA: {
                            KEY_PATH: TEST_FILE_PATH,
                            KEY_TYPE: FILE_TYPE,
                            KEY_START_LINE: START_LINE,
                            KEY_END_LINE: END_LINE,
                        },
                    },
                }
            ]
        }
    }


@pytest.fixture()
def mock_low_score_hits():
    """Create mock search hits with low scores."""
    return {
        KEY_HITS: {
            KEY_HITS: [
                {
                    KEY_SCORE: LOW_SCORE,  # Below threshold
                    KEY_SOURCE: {
                        KEY_TITLE: TEST_FILE_PATH,
                        KEY_CONTENT: TEST_FILE_CONTENT,
                        KEY_METADATA: {
                            KEY_PATH: TEST_FILE_PATH,
                            KEY_TYPE: FILE_TYPE,
                            KEY_START_LINE: START_LINE,
                            KEY_END_LINE: END_LINE,
                        },
                    },
                }
            ]
        }
    }


@pytest.fixture()
def mock_integration():
    """Create mock integration object."""
    return SimpleNamespace(id=TEST_INTEGRATION_ID)


@pytest.fixture()
def mock_new_knowledge_base():
    """Create mock new knowledge base."""
    return SimpleNamespace(id=TEST_KB_ID_NEW, total_files=TOTAL_FILES_NEW)


@pytest.fixture()
def mock_existing_knowledge_base():
    """Create mock existing knowledge base."""
    return SimpleNamespace(total_files=TOTAL_FILES_EXISTING)


@pytest.fixture()
def mock_folder_structure_kb():
    """Create mock knowledge base with folder structure."""
    return SimpleNamespace(
        id=TEST_KB_ID_DEFAULT,
        memory={
            KEY_FOLDER_STRUCTURE: {
                "": {
                    "src": {
                        "utils": {"helpers.py": None},
                        "main.py": None,
                    }
                }
            }
        },
    )


def assert_successful_response(response, expected_data_checks=None):
    """
    Assert that response is successful and optionally check data.
    
    :param response: Response object to check
    :param expected_data_checks: Optional dict of data checks to perform
    """
    assert response.success is True
    if expected_data_checks:
        for key, expected_value in expected_data_checks.items():
            if expected_value is not None:
                assert response.data[key] == expected_value
            else:
                assert response.data[key]  # Just check it exists


def assert_dao_interactions(kb_dao, create_called=False, update_called=False):
    """
    Assert DAO interaction patterns.
    
    :param kb_dao: Knowledge base DAO mock
    :param create_called: Whether create should have been called
    :param update_called: Whether update should have been called
    """
    if create_called:
        kb_dao.create_knowledge_base.assert_awaited_once()
    else:
        kb_dao.create_knowledge_base.assert_not_awaited()
    
    if update_called:
        kb_dao.update_graph_details.assert_awaited()


def assert_vector_db_connections(vector_db):
    """
    Assert vector database connection lifecycle.
    
    :param vector_db: Vector database adapter mock
    """
    vector_db.connect.assert_awaited()
    vector_db.close.assert_awaited()


# ---------------------------------------------------------------------------
# get_local_graph -----------------------------------------------------------
# ---------------------------------------------------------------------------
@pytest.mark.asyncio
async def test_get_local_graph_creates_new_kb(graph_service, test_user_data, get_graph_request_new,
                                              mock_integration, mock_new_knowledge_base):
    """Test get_local_graph creates new knowledge base when none exists."""
    service, _, kb_dao, integration_dao, _ = graph_service

    # DAO responses ---------------------------------------------------------
    kb_dao.exist_by_graph_id.return_value = None  # No KB yet.
    integration_dao.get_by_type.return_value = mock_integration
    # Simulate newly-created KB row returned by DAO
    kb_dao.create_knowledge_base.return_value = mock_new_knowledge_base

    # Patch the downstream locksmith call so the test never hits network.
    with patch(LOCKSMITH_SERVICE_PATH, new=AsyncMock()) as mock_locksmith:
        response = await service.get_local_graph(get_graph_request_new, test_user_data)

    # Assertions -----------------------------------------------------------
    expected_checks = {
        KEY_GRAPH_ID: None,  # SHA-256 hex string generated, just check exists
        KEY_TOTAL_FILES: TOTAL_FILES_NEW
    }
    assert_successful_response(response, expected_checks)
    assert_dao_interactions(kb_dao, create_called=True, update_called=False)
    mock_locksmith.assert_awaited_once()


@pytest.mark.asyncio
async def test_get_local_graph_updates_existing_kb(graph_service, test_user_data, get_graph_request_update,
                                                   mock_integration, mock_existing_knowledge_base):
    """Test get_local_graph updates existing knowledge base."""
    service, _, kb_dao, integration_dao, _ = graph_service

    kb_dao.exist_by_graph_id.return_value = mock_existing_knowledge_base
    integration_dao.get_by_type.return_value = mock_integration

    with patch(LOCKSMITH_SERVICE_PATH, new=AsyncMock()):
        response = await service.get_local_graph(get_graph_request_update, test_user_data)

    # The service should have toggled success & returned quickly without creating a KB.
    assert_successful_response(response)
    assert_dao_interactions(kb_dao, create_called=False, update_called=True)


# ---------------------------------------------------------------------------
# get_sync_status -----------------------------------------------------------
# ---------------------------------------------------------------------------
@pytest.mark.asyncio
async def test_get_sync_status_completed(graph_service, get_indexing_status_request):
    """Test get_sync_status returns completed status."""
    service, _, kb_dao, _, ingestion_run_dao = graph_service

    kb_dao.exist_by_graph_id.return_value = SimpleNamespace(id=TEST_KB_ID_DEFAULT, total_files=TOTAL_FILES_COMPLETED)
    ingestion_run_dao.get_latest_stats_json_by_kb_id.return_value = {
        KEY_TOTAL_SOURCE_ITEM: TOTAL_FILES_COMPLETED
    }

    response = await service.get_sync_status(get_indexing_status_request)

    expected_checks = {
        KEY_STATUS: STATUS_COMPLETED,
        KEY_PERCENTAGE: COMPLETION_PERCENTAGE
    }
    assert_successful_response(response, expected_checks)


# ---------------------------------------------------------------------------
# keyword_search & vector_search -------------------------------------------
# ---------------------------------------------------------------------------
@pytest.mark.asyncio
async def test_keyword_search_happy_path(graph_service, mock_search_hits, keyword_search_request):
    """Test keyword search happy path."""
    (
        service,
        vector_db,
        kb_dao,
        _,
        _,
    ) = graph_service

    kb_dao.exist_by_graph_id.return_value = SimpleNamespace(id=TEST_KB_ID_EXISTING)
    vector_db.keyword_search_source_code.return_value = mock_search_hits

    response = await service.keyword_search(keyword_search_request)

    assert_successful_response(response)
    assert response.data  # List with one element.
    assert_vector_db_connections(vector_db)


@pytest.mark.asyncio
async def test_vector_search_threshold(graph_service, mock_low_score_hits, vector_search_request):
    """Test vector search threshold filtering."""
    service, vector_db, kb_dao, _, _ = graph_service

    kb_dao.exist_by_graph_id.return_value = SimpleNamespace(id=TEST_KB_ID_SMALL)
    vector_db.knn_similarity_search.return_value = mock_low_score_hits

    response = await service.vector_search(vector_search_request)

    assert_successful_response(response)
    # No hits should survive threshold filtering.
    assert response.data == []


# ---------------------------------------------------------------------------
# get_file_content ----------------------------------------------------------
# ---------------------------------------------------------------------------
@pytest.mark.asyncio
async def test_get_file_content_found(graph_service, get_file_content_request):
    """Test get_file_content returns file content."""
    service, vector_db, kb_dao, _, _ = graph_service

    kb_dao.exist_by_graph_id.return_value = SimpleNamespace(id=TEST_KB_ID_SINGLE)
    vector_db.get_file_content.return_value = TEST_PRINT_CONTENT

    response = await service.get_file_content(get_file_content_request)

    assert_successful_response(response)
    assert response.data[KEY_CONTENT].startswith(CONTENT_STARTS_WITH)
    assert_vector_db_connections(vector_db)


# ---------------------------------------------------------------------------
# get_folder_structure ------------------------------------------------------
# ---------------------------------------------------------------------------
@pytest.mark.asyncio
async def test_get_folder_structure_subset(graph_service, mock_folder_structure_kb):
    """Test get_folder_structure returns folder subset."""
    service, _, kb_dao, _, _ = graph_service

    kb_dao.exist_by_graph_id.return_value = mock_folder_structure_kb

    response = await service.get_folder_structure(TEST_SHORT_GRAPH_ID, folder_path=TEST_FOLDER_PATH)
    
    assert_successful_response(response)
    assert response.data[KEY_FOLDER_STRUCTURE] == {"helpers.py": None} 