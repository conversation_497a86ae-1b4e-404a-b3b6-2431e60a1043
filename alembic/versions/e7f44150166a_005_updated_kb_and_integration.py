"""005 updated KB and integration

Revision ID: e7f44150166a
Revises: 2d6997f19617
Create Date: 2025-04-28 11:51:12.883215

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e7f44150166a'
down_revision: Union[str, None] = '2d6997f19617'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.add_column('integrations', sa.Column('image_name', sa.String(), nullable=True))
    op.add_column('integrations', sa.Column('integration_help_url', sa.String(), nullable=True))
    op.add_column('knowledge_bases', sa.Column('team_id', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.drop_column('knowledge_bases', 'team_id')
    op.drop_column('integrations', 'integration_help_url')
    op.drop_column('integrations', 'image_name')
    # ### end Alembic commands ###
