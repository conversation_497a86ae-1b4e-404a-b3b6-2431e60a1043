"""012 svg to png for quip

Revision ID: 50d9dd976119
Revises: 6a1f049d0f15
Create Date: 2025-05-12 19:15:50.840738

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '50d9dd976119'
down_revision: Union[str, None] = '6a1f049d0f15'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.execute(
        sa.text("""
                UPDATE integrations
                SET image_name = 'quip.png'
                WHERE type = 'quip'
            """)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    pass
    # ### end Alembic commands ###
