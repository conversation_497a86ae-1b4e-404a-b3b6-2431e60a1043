"""009 added integration github gitlab quip

Revision ID: 27585964be56
Revises: 91ba190ec365
Create Date: 2025-05-09 12:55:00.482994

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '27585964be56'
down_revision: Union[str, None] = '91ba190ec365'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    
    # Add GitHub integration
    op.execute(sa.text("""
        INSERT INTO integrations (
            type,
            name,
            created_by,
            created_at,
            credentials_json,
            image_name,
            integration_help_url
        )
        SELECT
            'github',
            'GitHub',
            1,
            CURRENT_TIMESTAMP,
            '{"url": null, "pat": null, "branch_name": null}'::jsonb,
            'github.svg',
            'https://chatgpt.com/'
        WHERE NOT EXISTS (
            SELECT 1 FROM integrations
            WHERE type = 'github'
        )
    """))
    
    # Add GitLab integration
    op.execute(sa.text("""
        INSERT INTO integrations (
            type,
            name,
            created_by,
            created_at,
            credentials_json,
            image_name,
            integration_help_url
        )
        SELECT
            'gitlab',
            'GitLab',
            1,
            CURRENT_TIMESTAMP,
            '{"url": null, "pat": null, "branch_name": null}'::jsonb,
            'gitlab.svg',
            'https://chatgpt.com/'
        WHERE NOT EXISTS (
            SELECT 1 FROM integrations
            WHERE type = 'gitlab'
        )
    """))
    
    # Add Quip integration
    op.execute(sa.text("""
        INSERT INTO integrations (
            type,
            name,
            created_by,
            created_at,
            credentials_json,
            image_name,
            integration_help_url
        )
        SELECT
            'quip',
            'Quip',
            1,
            CURRENT_TIMESTAMP,
            '{"pat": null, "urls": []}'::jsonb,
            'quip.svg',
            'https://chatgpt.com/'
        WHERE NOT EXISTS (
            SELECT 1 FROM integrations
            WHERE type = 'quip'
        )
    """))
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    pass
    # ### end Alembic commands ###
