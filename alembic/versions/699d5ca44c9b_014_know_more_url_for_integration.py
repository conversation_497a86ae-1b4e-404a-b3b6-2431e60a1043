"""014 know more url for integration

Revision ID: 699d5ca44c9b
Revises: e95034c7040e
Create Date: 2025-05-14 18:16:20.538181

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '699d5ca44c9b'
down_revision: Union[str, None] = 'e95034c7040e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """upgrade"""
    op.execute(
        sa.text("""
                UPDATE integrations
                SET integration_help_url = 'https://docs.fynix.ai/fynix-code-assistant/knowledge-source/connect-repositories/#-connecting-azure-devops-to-fynix-pat-token-and-clone-url'
                WHERE type = 'azure_devops'
            """)
    )

    op.execute(
        sa.text("""
                UPDATE integrations
                SET integration_help_url = 'https://docs.fynix.ai/fynix-code-assistant/knowledge-source/connect-repositories/#-connecting-github-to-fynix-pat-token-and-clone-url'
                WHERE type = 'github'
            """)
    )

    op.execute(
        sa.text("""
                UPDATE integrations
                SET integration_help_url = 'https://docs.fynix.ai/fynix-code-assistant/knowledge-source/connect-repositories/#-connecting-gitlab-to-fynix-pat-token-and-clone-url-guide'
                WHERE type = 'gitlab'
            """)
    )

    op.execute(
        sa.text("""
                UPDATE integrations
                SET integration_help_url = 'https://docs.fynix.ai/fynix-code-assistant/knowledge-source/connect-documents/#connecting-quip-documents-to-fynix'
                WHERE type = 'quip'
            """)
    )


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    pass
    # ### end Alembic commands ###
