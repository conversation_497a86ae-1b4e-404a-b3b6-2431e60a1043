"""007 memory and error for KB

Revision ID: d542f813a6fb
Revises: 8e5bedf7de30
Create Date: 2025-05-05 17:49:09.597002

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'd542f813a6fb'
down_revision: Union[str, None] = '8e5bedf7de30'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.create_table('ingestion_errors',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('run_id', sa.BigInteger(), nullable=False),
    sa.Column('error_message', sa.Text(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['run_id'], ['ingestion_runs.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ingestion_errors_run_id'), 'ingestion_errors', ['run_id'], unique=False)
    op.add_column('knowledge_bases', sa.Column('memory', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.drop_column('knowledge_bases', 'memory')
    op.drop_index(op.f('ix_ingestion_errors_run_id'), table_name='ingestion_errors')
    op.drop_table('ingestion_errors')
    # ### end Alembic commands ###
