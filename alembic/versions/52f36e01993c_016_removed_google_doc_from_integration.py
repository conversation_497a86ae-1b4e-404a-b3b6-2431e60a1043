""" 016 removed google doc from integration

Revision ID: 52f36e01993c
Revises: 3f87e005a0ad
Create Date: 2025-05-19 15:35:43.615205

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '52f36e01993c'
down_revision: Union[str, None] = '3f87e005a0ad'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.execute(
        sa.text("""
                DELETE FROM integrations
                WHERE type = 'google_docs'
            """)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.execute(sa.text("""
                INSERT INTO integrations (
                    type,
                    name,
                    created_by,
                    created_at,
                    credentials_json,
                    image_name,
                    integration_help_url
                )
                SELECT
                    'google_docs',
                    'Google Docs',
                    1,
                    CURRENT_TIMESTAMP,
                    '{"docs": null, "token": null}'::jsonb,
                    'google_docs.svg',
                    ''
                WHERE NOT EXISTS (
                    SELECT 1 FROM integrations
                    WHERE type = 'google_docs'
                )
            """))
    # ### end Alembic commands ###
