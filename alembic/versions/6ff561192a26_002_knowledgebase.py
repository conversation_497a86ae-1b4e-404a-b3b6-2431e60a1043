"""002 - knowledgebase

Revision ID: 6ff561192a26
Revises: 7a4b118ab0a8
Create Date: 2025-04-22 13:52:27.412940

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '6ff561192a26'
down_revision: Union[str, None] = '7a4b118ab0a8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.create_table('integrations',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('org_id', sa.BigInteger(), nullable=False),
    sa.Column('type', sa.Enum('github', 'gitlab', 'azure_devops', 'google_docs', 'quip', 'slack', 'custom', name='integration_type'), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('credentials_json', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('created_by', sa.BigInteger(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_integrations_org_id'), 'integrations', ['org_id'], unique=False)
    op.create_table('knowledge_bases',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('org_id', sa.BigInteger(), nullable=False),
    sa.Column('integration_id', sa.BigInteger(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('source_identifier', sa.Text(), nullable=False),
    sa.Column('kb_type', sa.String(length=50), nullable=True),
    sa.Column('settings_json', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('state', sa.Enum('ready', 'indexing', 'error', 'disabled', name='kb_state'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('last_indexed_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['integration_id'], ['integrations.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_knowledge_bases_org_id'), 'knowledge_bases', ['org_id'], unique=False)
    op.create_table('ingestion_runs',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('org_id', sa.BigInteger(), nullable=False),
    sa.Column('kb_id', sa.BigInteger(), nullable=False),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('finished_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('stats_json', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.ForeignKeyConstraint(['kb_id'], ['knowledge_bases.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ingestion_runs_org_id'), 'ingestion_runs', ['org_id'], unique=False)


    op.execute(sa.text("""
        INSERT INTO integrations (
            org_id,
            type,
            name,
            created_by,
            created_at,
            credentials_json
        )
        SELECT
            1,
            'azure_devops',
            'Default Azure DevOps Integration',
            1,
            CURRENT_TIMESTAMP,
            '{"url": null, "pat": null, "branch_name": null}'::jsonb
        WHERE NOT EXISTS (
            SELECT 1 FROM integrations
            WHERE org_id = 1 AND type = 'azure_devops'
        )
    """))
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.drop_index(op.f('ix_ingestion_runs_org_id'), table_name='ingestion_runs')
    op.drop_table('ingestion_runs')
    op.drop_index(op.f('ix_knowledge_bases_org_id'), table_name='knowledge_bases')
    op.drop_table('knowledge_bases')
    op.drop_index(op.f('ix_integrations_org_id'), table_name='integrations')
    op.drop_table('integrations')
    # ### end Alembic commands ###
