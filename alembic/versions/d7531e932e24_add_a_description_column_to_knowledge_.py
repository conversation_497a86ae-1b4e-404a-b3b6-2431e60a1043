"""Add a description column to knowledge base table

Revision ID: d7531e932e24
Revises: 3cfa916cb05c
Create Date: 2025-06-24 13:01:15.956810

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd7531e932e24'
down_revision: Union[str, None] = '3cfa916cb05c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.add_column('knowledge_bases', sa.Column('description', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.drop_column('knowledge_bases', 'description')
    # ### end Alembic commands ###
