"""removed GraphDetail and FileDetail table that were added in code_indexing

Revision ID: b381143418d7
Revises: d7531e932e24
Create Date: 2025-07-03 04:34:34.337141

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b381143418d7'
down_revision: Union[str, None] = 'd7531e932e24'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.drop_table('file_indexing_details')
    op.drop_table('graph_indexing_details')
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    pass
    # ### end Alembic commands ###
