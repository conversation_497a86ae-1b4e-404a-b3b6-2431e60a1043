"""011 know more urls for provider

Revision ID: 6a1f049d0f15
Revises: 9d57998cdbfe
Create Date: 2025-05-12 18:39:53.318526

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6a1f049d0f15'
down_revision: Union[str, None] = '9d57998cdbfe'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.execute(
        sa.text("""
            UPDATE integrations
            SET integration_help_url = 'https://learn.microsoft.com/en-us/azure/devops/organizations/accounts/use-personal-access-tokens-to-authenticate'
            WHERE type = 'azure_devops'
        """)
    )

    op.execute(
        sa.text("""
            UPDATE integrations
            SET integration_help_url = 'https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens'
            WHERE type = 'github'
        """)
    )

    op.execute(
        sa.text("""
            UPDATE integrations
            SET integration_help_url = 'https://docs.gitlab.com/user/profile/personal_access_tokens/'
            WHERE type = 'gitlab'
        """)
    )

    op.execute(
        sa.text("""
            UPDATE integrations
            SET integration_help_url = 'https://quip.com/dev/token'
            WHERE type = 'quip'
        """)
    )


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    pass
    # ### end Alembic commands ###
