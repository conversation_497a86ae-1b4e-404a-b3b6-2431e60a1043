"""006 column update in integration table

Revision ID: 8e5bedf7de30
Revises: e7f44150166a
Create Date: 2025-04-29 15:47:23.293377

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8e5bedf7de30'
down_revision: Union[str, None] = 'e7f44150166a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.create_index(op.f('ix_knowledge_bases_team_id'), 'knowledge_bases', ['team_id'], unique=False)

    # Update Azure DevOps integration record
    op.execute("""
            UPDATE integrations 
            SET 
                name = 'Azure DevOps',
                image_name = 'azure_devops.svg',
                integration_help_url = 'https://chatgpt.com/'
            WHERE type = 'azure_devops'
        """)
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.drop_index(op.f('ix_knowledge_bases_team_id'), table_name='knowledge_bases')
    # ### end Alembic commands ###
