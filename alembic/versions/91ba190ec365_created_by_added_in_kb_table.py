"""created by added in KB table

Revision ID: 91ba190ec365
Revises: 21e6ce4c764e
Create Date: 2025-05-07 11:56:46.317489

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '91ba190ec365'
down_revision: Union[str, None] = '21e6ce4c764e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.add_column('knowledge_bases', sa.Column('created_by', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.drop_column('knowledge_bases', 'created_by')
    # ### end Alembic commands ###
