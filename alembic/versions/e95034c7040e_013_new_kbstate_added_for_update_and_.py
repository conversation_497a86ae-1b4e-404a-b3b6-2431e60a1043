"""013 new KBState added for update and token

Revision ID: e95034c7040e
Revises: 50d9dd976119
Create Date: 2025-05-13 16:15:03.446993

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e95034c7040e'
down_revision: Union[str, None] = '50d9dd976119'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.execute("ALTER TYPE kb_state ADD VALUE IF NOT EXISTS 'update_error'")
    op.execute("ALTER TYPE kb_state ADD VALUE IF NOT EXISTS 'updating'")
    op.execute("ALTER TYPE kb_state ADD VALUE IF NOT EXISTS 'token_error'")
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    pass
    # ### end Alembic commands ###
