"""004 removed org_id from knowledge_base

Revision ID: 2d6997f19617
Revises: a8a3b90a04a9
Create Date: 2025-04-23 17:09:03.952165

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2d6997f19617'
down_revision: Union[str, None] = 'a8a3b90a04a9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:

    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.drop_index('ix_chunks_org_id', table_name='chunks')
    op.drop_column('chunks', 'org_id')
    op.drop_index('ix_ingestion_runs_org_id', table_name='ingestion_runs')
    op.drop_column('ingestion_runs', 'org_id')
    op.alter_column('integrations', 'created_by',
               existing_type=sa.BIGINT(),
               type_=sa.String(),
               existing_nullable=True)
    op.drop_index('ix_integrations_org_id', table_name='integrations')
    op.drop_column('integrations', 'org_id')
    op.drop_index('ix_knowledge_bases_org_id', table_name='knowledge_bases')
    op.drop_column('knowledge_bases', 'org_id')
    op.drop_index('ix_source_items_org_id', table_name='source_items')
    op.drop_constraint('uq_source_item_version', 'source_items', type_='unique')
    op.create_unique_constraint('uq_source_item_version', 'source_items', ['kb_id', 'provider_item_id', 'version_tag'])
    op.drop_column('source_items', 'org_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.add_column('source_items', sa.Column('org_id', sa.BIGINT(), autoincrement=False, nullable=False))
    op.drop_constraint('uq_source_item_version', 'source_items', type_='unique')
    op.create_unique_constraint('uq_source_item_version', 'source_items', ['org_id', 'kb_id', 'provider_item_id', 'version_tag'])
    op.create_index('ix_source_items_org_id', 'source_items', ['org_id'], unique=False)
    op.add_column('knowledge_bases', sa.Column('org_id', sa.BIGINT(), autoincrement=False, nullable=False))
    op.create_index('ix_knowledge_bases_org_id', 'knowledge_bases', ['org_id'], unique=False)
    op.add_column('integrations', sa.Column('org_id', sa.BIGINT(), autoincrement=False, nullable=False))
    op.create_index('ix_integrations_org_id', 'integrations', ['org_id'], unique=False)
    op.alter_column('integrations', 'created_by',
               existing_type=sa.String(),
               type_=sa.BIGINT(),
               existing_nullable=True)
    op.add_column('ingestion_runs', sa.Column('org_id', sa.BIGINT(), autoincrement=False, nullable=False))
    op.create_index('ix_ingestion_runs_org_id', 'ingestion_runs', ['org_id'], unique=False)
    op.add_column('chunks', sa.Column('org_id', sa.BIGINT(), autoincrement=False, nullable=False))
    op.create_index('ix_chunks_org_id', 'chunks', ['org_id'], unique=False)
    # ### end Alembic commands ###
