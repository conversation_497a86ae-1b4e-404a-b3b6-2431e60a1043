"""017 Add integration type data analyst

Revision ID: 4cefc42d534f
Revises: 52f36e01993c
Create Date: 2025-05-22 15:35:10.042086

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4cefc42d534f'
down_revision: Union[str, None] = '52f36e01993c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.execute("COMMIT")  # End the current transaction
    op.execute("ALTER TYPE integration_type ADD VALUE IF NOT EXISTS 'bigquery'")
    op.execute("BEGIN")  # Start a new transaction

    # Proceed with the insertion
    op.execute(sa.text("""
            INSERT INTO integrations (
                type,
                name,
                created_by,
                created_at,
                credentials_json,
                image_name,
                integration_help_url
            )
            SELECT
                'bigquery',
                'Big Query',
                1,
                CURRENT_TIMESTAMP,
                '{}'::jsonb,
                'bigquery.svg',
                ''
            WHERE NOT EXISTS (
                SELECT 1 FROM integrations
                WHERE type = 'bigquery'
            )
        """))
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    pass
    # ### end Alembic commands ###
