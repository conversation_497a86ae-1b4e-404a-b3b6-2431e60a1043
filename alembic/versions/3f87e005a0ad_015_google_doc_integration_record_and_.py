"""015 google doc integration record and is updatable coloumn added

Revision ID: 3f87e005a0ad
Revises: 699d5ca44c9b
Create Date: 2025-05-16 13:07:24.950727

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3f87e005a0ad'
down_revision: Union[str, None] = '699d5ca44c9b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    # First add the column as nullable
    op.add_column('knowledge_bases', sa.Column('is_updatable', sa.Boolean(), nullable=True))

    # Then update existing rows with a default value
    op.execute(sa.text("UPDATE knowledge_bases SET is_updatable = FALSE"))

    # Finally, set the column to not nullable
    op.alter_column('knowledge_bases', 'is_updatable', nullable=False)

    op.execute(sa.text("""
            INSERT INTO integrations (
                type,
                name,
                created_by,
                created_at,
                credentials_json,
                image_name,
                integration_help_url
            )
            SELECT
                'google_docs',
                'Google Docs',
                1,
                CURRENT_TIMESTAMP,
                '{"docs": null, "token": null}'::jsonb,
                'google_docs.svg',
                ''
            WHERE NOT EXISTS (
                SELECT 1 FROM integrations
                WHERE type = 'google_docs'
            )
        """))
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.drop_column('knowledge_bases', 'is_updatable')
    # ### end Alembic commands ###
