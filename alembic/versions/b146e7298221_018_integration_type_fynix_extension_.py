"""018 integration type fynix_extension added

Revision ID: b146e7298221
Revises: 4cefc42d534f
Create Date: 2025-05-23 13:48:01.697243

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b146e7298221'
down_revision: Union[str, None] = '4cefc42d534f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.execute("COMMIT")

    op.execute("ALTER TYPE integration_type ADD VALUE IF NOT EXISTS 'fynix_extension'")

    op.execute("BEGIN")
    op.execute(sa.text("""
                    INSERT INTO integrations (
                        type,
                        name,
                        created_by,
                        created_at,
                        credentials_json,
                        image_name,
                        integration_help_url
                    )
                    SELECT
                        'fynix_extension',
                        'Fynix Extension',
                        1,
                        CURRENT_TIMESTAMP,
                        '{}'::jsonb,
                        'fynix_extension.svg',
                        ''
                    WHERE NOT EXISTS (
                        SELECT 1 FROM integrations
                        WHERE type = 'fynix_extension'
                    )
                """))
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    pass
    # ### end Alembic commands ###
