"""019 total_files and graph_id added in KB table

Revision ID: fa9f89b5338d
Revises: b146e7298221
Create Date: 2025-05-26 19:00:35.350525

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fa9f89b5338d'
down_revision: Union[str, None] = 'b146e7298221'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.add_column('knowledge_bases', sa.Column('graph_id', sa.String(), nullable=True))
    op.add_column('knowledge_bases', sa.Column('total_files', sa.BigInteger(), nullable=True))
    op.create_unique_constraint(None, 'knowledge_bases', ['graph_id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.drop_constraint(None, 'knowledge_bases', type_='unique')
    op.drop_column('knowledge_bases', 'total_files')
    op.drop_column('knowledge_bases', 'graph_id')
    # ### end Alembic commands ###
