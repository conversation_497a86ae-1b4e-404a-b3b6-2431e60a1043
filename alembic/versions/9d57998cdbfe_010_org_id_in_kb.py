"""010 org_id in KB

Revision ID: 9d57998cdbfe
Revises: 27585964be56
Create Date: 2025-05-09 15:09:50.594590

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9d57998cdbfe'
down_revision: Union[str, None] = '27585964be56'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.add_column('knowledge_bases', sa.Column('org_id', sa.String(), nullable=True))
    op.create_index(op.f('ix_knowledge_bases_org_id'), 'knowledge_bases', ['org_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.drop_index(op.f('ix_knowledge_bases_org_id'), table_name='knowledge_bases')
    op.drop_column('knowledge_bases', 'org_id')
    # ### end Alembic commands ###
