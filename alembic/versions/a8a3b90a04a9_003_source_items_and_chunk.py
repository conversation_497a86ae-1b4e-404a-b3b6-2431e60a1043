"""003 source_items and chunk

Revision ID: a8a3b90a04a9
Revises: 6ff561192a26
Create Date: 2025-04-22 18:31:14.910744

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'a8a3b90a04a9'
down_revision: Union[str, None] = '6ff561192a26'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.create_table('source_items',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('org_id', sa.BigInteger(), nullable=False),
    sa.Column('kb_id', sa.BigInteger(), nullable=False),
    sa.Column('provider_item_id', sa.Text(), nullable=False),
    sa.Column('kind', sa.Enum('file', 'document', 'folder', 'message', name='source_item_kind'), nullable=False),
    sa.Column('logical_path', sa.Text(), nullable=True),
    sa.Column('version_tag', sa.String(length=256), nullable=True),
    sa.Column('checksum', sa.String(length=64), nullable=True),
    sa.Column('metadata_json', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.ForeignKeyConstraint(['kb_id'], ['knowledge_bases.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('org_id', 'kb_id', 'provider_item_id', 'version_tag', name='uq_source_item_version')
    )
    op.create_index(op.f('ix_source_items_id'), 'source_items', ['id'], unique=False)
    op.create_index(op.f('ix_source_items_org_id'), 'source_items', ['org_id'], unique=False)
    op.create_table('chunks',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('org_id', sa.BigInteger(), nullable=False),
    sa.Column('source_item_id', sa.String(), nullable=False),
    sa.Column('chunk_index', sa.Integer(), nullable=False),
    sa.Column('start_offset', sa.Integer(), nullable=True),
    sa.Column('end_offset', sa.Integer(), nullable=True),
    sa.Column('text', sa.Text(), nullable=True),
    sa.Column('es_doc_id', sa.String(length=255), nullable=True),
    sa.Column('metadata_json', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.ForeignKeyConstraint(['source_item_id'], ['source_items.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('source_item_id', 'chunk_index', name='uq_chunk_per_source_index')
    )
    op.create_index(op.f('ix_chunks_org_id'), 'chunks', ['org_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.drop_index(op.f('ix_chunks_org_id'), table_name='chunks')
    op.drop_table('chunks')
    op.drop_index(op.f('ix_source_items_org_id'), table_name='source_items')
    op.drop_index(op.f('ix_source_items_id'), table_name='source_items')
    op.drop_table('source_items')
    # ### end Alembic commands ###
