"""020 checksum in chunks

Revision ID: 3cfa916cb05c
Revises: fa9f89b5338d
Create Date: 2025-05-27 15:19:40.102005

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3cfa916cb05c'
down_revision: Union[str, None] = 'fa9f89b5338d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.add_column('chunks', sa.Column('checksum', sa.String(length=64), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.drop_column('chunks', 'checksum')
    # ### end Alembic commands ###
