"""Combined initial migration

Revision ID: 7a4b118ab0a8
Revises: 
Create Date: 2025-04-07 18:28:10.351488

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7a4b118ab0a8'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.create_table('external_data_source',
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_external_data_source_id'), 'external_data_source', ['id'], unique=False)
    op.create_table('graph_indexing_details',
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('graph_id', sa.String(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=False),
    sa.Column('local_path', sa.String(), nullable=False),
    sa.Column('remote_path', sa.String(), nullable=True),
    sa.Column('branch', sa.String(), nullable=True),
    sa.Column('total_files', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('graph_id')
    )
    op.create_index(op.f('ix_graph_indexing_details_graph_id'), 'graph_indexing_details', ['graph_id'], unique=False)
    op.create_index(op.f('ix_graph_indexing_details_user_id'), 'graph_indexing_details', ['user_id'], unique=False)
    op.create_table('external_data_documents',
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('vector_indexing_status', sa.String(), nullable=False),
    sa.Column('document_references', sa.JSON(), nullable=False),
    sa.Column('datasource_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['datasource_id'], ['external_data_source.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_external_data_documents_id'), 'external_data_documents', ['id'], unique=False)
    op.create_table('file_indexing_details',
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('file_id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('hash', sa.String(), nullable=False),
    sa.Column('local_path', sa.String(), nullable=False),
    sa.Column('vector_indexing_status', sa.String(), nullable=False),
    sa.Column('graph_id', sa.String(), nullable=False),
    sa.ForeignKeyConstraint(['graph_id'], ['graph_indexing_details.graph_id'], ),
    sa.PrimaryKeyConstraint('file_id')
    )
    op.create_index(op.f('ix_file_indexing_details_file_id'), 'file_indexing_details', ['file_id'], unique=False)
    op.create_index(op.f('ix_file_indexing_details_hash'), 'file_indexing_details', ['hash'], unique=False)
    bind = op.get_bind()
    bind.execute(sa.text("""
        INSERT INTO external_data_source (name, created_at, updated_at)
        SELECT 'stackoverflow', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        WHERE NOT EXISTS (
            SELECT 1 FROM external_data_source WHERE name = 'stackoverflow'
        )
    """))
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.drop_index(op.f('ix_file_indexing_details_hash'), table_name='file_indexing_details')
    op.drop_index(op.f('ix_file_indexing_details_file_id'), table_name='file_indexing_details')
    op.drop_table('file_indexing_details')
    op.drop_index(op.f('ix_external_data_documents_id'), table_name='external_data_documents')
    op.drop_table('external_data_documents')
    op.drop_index(op.f('ix_graph_indexing_details_user_id'), table_name='graph_indexing_details')
    op.drop_index(op.f('ix_graph_indexing_details_graph_id'), table_name='graph_indexing_details')
    op.drop_table('graph_indexing_details')
    op.drop_index(op.f('ix_external_data_source_id'), table_name='external_data_source')
    op.drop_table('external_data_source')
    # ### end Alembic commands ###
