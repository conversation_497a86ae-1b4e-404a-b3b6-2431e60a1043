"""008 removed contraints

Revision ID: 21e6ce4c764e
Revises: d542f813a6fb
Create Date: 2025-05-05 18:47:23.973594

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '21e6ce4c764e'
down_revision: Union[str, None] = 'd542f813a6fb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.drop_constraint('uq_source_item_version', 'source_items', type_='unique')
    # ### end Alembic commands ###


def downgrade() -> None:
    """# ### commands auto generated by Alembic - please adjust! ###"""
    op.create_unique_constraint('uq_source_item_version', 'source_items', ['kb_id', 'provider_item_id', 'version_tag'])
    # ### end Alembic commands ###
