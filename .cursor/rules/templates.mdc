---
description: Comprehensive guidelines for developing robust, scalable, and maintainable FastAPI applications. Covers code structure, performance, security, testing, and common pitfalls.
globs: *.py
---

# CEREBRUM BACKEND DEVELOPMENT RULES

## CORE PRINCIPLES
- Follow DRY (Don't Repeat Yourself) principles
- Use async/await for all I/O operations
- Implement proper error handling with structured exceptions
- Follow consistent naming conventions
- Use type hints for all function parameters and return values
- Implement comprehensive logging
- Use dependency injection patterns

## PROJECT STRUCTURE TEMPLATE

```
project_root/
├── app/                    # Application entry point
│   ├── application.py      # FastAPI app configuration
│   ├── main.py            # Application startup
│   ├── router.py          # Main router
│   └── routing.py         # Route definitions
├── config/                 # Configuration management
│   ├── settings.py        # Application settings
│   ├── logging.py         # Logging configuration
│   ├── sentry.py          # Error tracking
│   └── default.{env}.yaml # Environment configs
├── surface/               # API surface layer
│   ├── views.py           # API endpoints
│   ├── services.py        # Business logic
│   ├── models.py          # Database models
│   ├── serializers.py     # Request/response schemas
│   ├── dao.py             # Data access layer
│   ├── constants.py       # Domain constants
│   └── router.py          # Domain router
├── utils/                 # Shared utilities
│   ├── base_view.py       # Base view class
│   ├── exceptions.py      # Exception definitions
│   ├── dao.py             # Base DAO class
│   ├── connection_handler.py # DB connections
│   └── middlewares/       # Custom middlewares
├── integrations/          # External service integrations
├── requirements/          # Dependency management
└── tests/                # Test suite
```

## FASTAPI APPLICATION TEMPLATE

```python
# app/application.py
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.sessions import SessionMiddleware

from config.settings import loaded_config
from config.sentry import configure_sentry
from config.logging import logger
from utils.middlewares.custom_middleware import SecurityHeadersMiddleware
from app.router import api_router
from utils.load_config import run_on_startup, run_on_exit


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    await run_on_startup()
    yield
    await run_on_exit()


def get_app() -> FastAPI:
    """
    Get FastAPI application with all configurations.

    :return: Configured FastAPI application
    """
    configure_sentry()

    app = FastAPI(
        title="{PROJECT_NAME}",
        docs_url="/api-reference",
        openapi_url="/openapi.json",
        lifespan=lifespan,
        root_path="/"
    )

    # CORS Configuration
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Custom Middlewares
    app.add_middleware(SessionMiddleware, secret_key="your-secret-key")
    app.add_middleware(SecurityHeadersMiddleware)

    # Include routers
    app.include_router(api_router)

    return app
```

## VIEW CLASS TEMPLATE

```python
# surface/views.py
import asyncio
from fastapi import BackgroundTasks, Depends, HTTPException
from starlette.responses import StreamingResponse

from config.logging import logger
from surface.serializers import {EntityName}Request, {EntityName}Response
from surface.services import {EntityName}Service
from utils.base_view import BaseView
from utils.connection_handler import ConnectionHandler, get_connection_handler_for_app
from utils.exceptions import {EntityName}Exception


class {EntityName}View(BaseView):
    """Handles {entity_name}-related endpoints"""

    @classmethod
    async def create_{entity_name}(
        cls,
        request: {EntityName}Request,
        connection_handler: ConnectionHandler = Depends(get_connection_handler_for_app)
    ):
        """
        Create a new {entity_name}.

        :param request: {EntityName} creation request
        :param connection_handler: Database connection handler
        :return: ResponseData with created {entity_name}
        """
        try:
            service = {EntityName}Service(connection_handler=connection_handler)
            result = await service.create_{entity_name}(request)
            await connection_handler.session.commit()
            return cls.construct_success_response(data=result)
        except Exception as e:
            logger.error(f"Error creating {entity_name}: {str(e)}")
            return cls.construct_error_response({EntityName}Exception(str(e)))

    @classmethod
    async def get_{entity_name}(
        cls,
        {entity_name}_id: int,
        connection_handler: ConnectionHandler = Depends(get_connection_handler_for_app)
    ):
        """
        Get {entity_name} by ID.

        :param {entity_name}_id: ID of the {entity_name}
        :param connection_handler: Database connection handler
        :return: ResponseData with {entity_name} data
        """
        try:
            service = {EntityName}Service(connection_handler=connection_handler)
            result = await service.get_{entity_name}({entity_name}_id)
            if not result:
                raise {EntityName}Exception(f"{EntityName} not found")
            return cls.construct_success_response(data=result)
        except Exception as e:
            logger.error(f"Error fetching {entity_name}: {str(e)}")
            return cls.construct_error_response({EntityName}Exception(str(e)))

    @classmethod
    async def update_{entity_name}(
        cls,
        {entity_name}_id: int,
        request: {EntityName}Request,
        connection_handler: ConnectionHandler = Depends(get_connection_handler_for_app)
    ):
        """
        Update existing {entity_name}.

        :param {entity_name}_id: ID of the {entity_name}
        :param request: Update request data
        :param connection_handler: Database connection handler
        :return: ResponseData with updated {entity_name}
        """
        try:
            service = {EntityName}Service(connection_handler=connection_handler)
            result = await service.update_{entity_name}({entity_name}_id, request)
            await connection_handler.session.commit()
            return cls.construct_success_response(data=result)
        except Exception as e:
            logger.error(f"Error updating {entity_name}: {str(e)}")
            return cls.construct_error_response({EntityName}Exception(str(e)))

    @classmethod
    async def delete_{entity_name}(
        cls,
        {entity_name}_id: int,
        connection_handler: ConnectionHandler = Depends(get_connection_handler_for_app)
    ):
        """
        Delete {entity_name} by ID.

        :param {entity_name}_id: ID of the {entity_name}
        :param connection_handler: Database connection handler
        :return: ResponseData confirming deletion
        """
        try:
            service = {EntityName}Service(connection_handler=connection_handler)
            await service.delete_{entity_name}({entity_name}_id)
            await connection_handler.session.commit()
            return cls.construct_success_response(message=f"{EntityName} deleted successfully")
        except Exception as e:
            logger.error(f"Error deleting {entity_name}: {str(e)}")
            return cls.construct_error_response({EntityName}Exception(str(e)))

    @classmethod
    async def list_{entity_name}s(
        cls,
        skip: int = 0,
        limit: int = 100,
        connection_handler: ConnectionHandler = Depends(get_connection_handler_for_app)
    ):
        """
        List {entity_name}s with pagination.

        :param skip: Number of records to skip
        :param limit: Maximum number of records to return
        :param connection_handler: Database connection handler
        :return: ResponseData with list of {entity_name}s
        """
        try:
            service = {EntityName}Service(connection_handler=connection_handler)
            result = await service.list_{entity_name}s(skip=skip, limit=limit)
            return cls.construct_success_response(data=result)
        except Exception as e:
            logger.error(f"Error listing {entity_name}s: {str(e)}")
            return cls.construct_error_response({EntityName}Exception(str(e)))
```

## SERVICE CLASS TEMPLATE

```python
# surface/services.py
import asyncio
from typing import List, Optional
from config.logging import logger
from surface.dao import {EntityName}Dao
from surface.serializers import {EntityName}Request, {EntityName}Response
from utils.connection_handler import ConnectionHandler
from utils.exceptions import {EntityName}Exception


class {EntityName}Service:
    """Business logic for {entity_name} operations"""

    def __init__(self, connection_handler: ConnectionHandler = None):
        self.connection_handler = connection_handler
        if connection_handler:
            self.{entity_name}_dao = {EntityName}Dao(session=connection_handler.session)

    async def create_{entity_name}(self, request: {EntityName}Request) -> dict:
        """
        Create a new {entity_name}.

        :param request: {EntityName} creation request
        :return: Created {entity_name} data
        """
        try:
            # Validation logic here
            await self._validate_{entity_name}_request(request)

            # Create {entity_name}
            {entity_name}_data = request.model_dump()
            {entity_name} = self.{entity_name}_dao.add_object({entity_name}_data)

            logger.info(f"{EntityName} created successfully: {{entity_name}.id}")
            return self._serialize_{entity_name}({entity_name})
        except Exception as e:
            logger.error(f"Error creating {entity_name}: {str(e)}")
            raise {EntityName}Exception(f"Failed to create {entity_name}: {str(e)}")

    async def get_{entity_name}(self, {entity_name}_id: int) -> Optional[dict]:
        """
        Get {entity_name} by ID.

        :param {entity_name}_id: ID of the {entity_name}
        :return: {EntityName} data or None
        """
        try:
            {entity_name} = await self.{entity_name}_dao.get_by_pk({entity_name}_id)
            if not {entity_name}:
                return None
            return self._serialize_{entity_name}({entity_name})
        except Exception as e:
            logger.error(f"Error fetching {entity_name} {{entity_name}_id}: {str(e)}")
            raise {EntityName}Exception(f"Failed to fetch {entity_name}: {str(e)}")

    async def update_{entity_name}(self, {entity_name}_id: int, request: {EntityName}Request) -> dict:
        """
        Update existing {entity_name}.

        :param {entity_name}_id: ID of the {entity_name}
        :param request: Update request data
        :return: Updated {entity_name} data
        """
        try:
            # Check if {entity_name} exists
            {entity_name} = await self.{entity_name}_dao.get_by_pk({entity_name}_id)
            if not {entity_name}:
                raise {EntityName}Exception(f"{EntityName} not found")

            # Validation logic here
            await self._validate_{entity_name}_request(request)

            # Update {entity_name}
            update_data = request.model_dump(exclude_unset=True)
            await self.{entity_name}_dao.update_by_pk({entity_name}_id, update_data)

            # Fetch updated {entity_name}
            updated_{entity_name} = await self.{entity_name}_dao.get_by_pk({entity_name}_id)

            logger.info(f"{EntityName} updated successfully: {{entity_name}_id}")
            return self._serialize_{entity_name}(updated_{entity_name})
        except Exception as e:
            logger.error(f"Error updating {entity_name} {{entity_name}_id}: {str(e)}")
            raise {EntityName}Exception(f"Failed to update {entity_name}: {str(e)}")

    async def delete_{entity_name}(self, {entity_name}_id: int) -> None:
        """
        Delete {entity_name} by ID.

        :param {entity_name}_id: ID of the {entity_name}
        """
        try:
            # Check if {entity_name} exists
            {entity_name} = await self.{entity_name}_dao.get_by_pk({entity_name}_id)
            if not {entity_name}:
                raise {EntityName}Exception(f"{EntityName} not found")

            # Perform deletion logic here
            # For soft delete: await self.{entity_name}_dao.update_by_pk({entity_name}_id, {"is_deleted": True})
            # For hard delete: await self.{entity_name}_dao.delete_by_pk({entity_name}_id)

            logger.info(f"{EntityName} deleted successfully: {{entity_name}_id}")
        except Exception as e:
            logger.error(f"Error deleting {entity_name} {{entity_name}_id}: {str(e)}")
            raise {EntityName}Exception(f"Failed to delete {entity_name}: {str(e)}")

    async def list_{entity_name}s(self, skip: int = 0, limit: int = 100) -> List[dict]:
        """
        List {entity_name}s with pagination.

        :param skip: Number of records to skip
        :param limit: Maximum number of records to return
        :return: List of {entity_name} data
        """
        try:
            {entity_name}s = await self.{entity_name}_dao.list_with_pagination(skip=skip, limit=limit)
            return [self._serialize_{entity_name}({entity_name}) for {entity_name} in {entity_name}s]
        except Exception as e:
            logger.error(f"Error listing {entity_name}s: {str(e)}")
            raise {EntityName}Exception(f"Failed to list {entity_name}s: {str(e)}")

    async def _validate_{entity_name}_request(self, request: {EntityName}Request) -> None:
        """
        Validate {entity_name} request data.

        :param request: Request to validate
        """
        # Add validation logic here
        pass

    def _serialize_{entity_name}(self, {entity_name}) -> dict:
        """
        Serialize {entity_name} object to dictionary.

        :param {entity_name}: {EntityName} object
        :return: Serialized data
        """
        return {
            "id": {entity_name}.id,
            "created_at": {entity_name}.created_at.isoformat() if {entity_name}.created_at else None,
            "updated_at": {entity_name}.updated_at.isoformat() if {entity_name}.updated_at else None,
            # Add other fields as needed
        }

    @staticmethod
    async def background_operation(data: dict) -> None:
        """
        Template for background operations.

        :param data: Operation data
        """
        try:
            # Implement background logic here
            logger.info("Background operation completed")
        except Exception as e:
            logger.error(f"Background operation failed: {str(e)}")
```

## MODEL TEMPLATE

```python
# surface/models.py
from sqlalchemy import String, Column, Integer, JSON, ForeignKey, Enum, Boolean, DateTime
from sqlalchemy.orm import Mapped, relationship
from datetime import datetime

from utils.sqlalchemy import TimestampMixin, Base


class {EntityName}(TimestampMixin, Base):
    """
    {EntityName} model representing {entity_description}.
    """
    __tablename__ = "{table_name}"

    id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    name: Mapped[str] = Column(String(255), nullable=False, index=True)
    description: Mapped[str] = Column(String(1000), nullable=True)
    status: Mapped[str] = Column(String(50), default="active", index=True)
    config: Mapped[dict] = Column(JSON, nullable=True)
    is_active: Mapped[bool] = Column(Boolean, default=True, index=True)

    # Foreign key relationships
    user_id: Mapped[int] = Column(ForeignKey('users.id'), nullable=True, index=True)

    # Relationships
    user = relationship("User", back_populates="{entity_name}s")
    related_entities = relationship("{RelatedEntity}", back_populates="{entity_name}")

    def __repr__(self) -> str:
        return f"<{EntityName}(id={self.id}, name='{self.name}')>"

    def to_dict(self) -> dict:
        """Convert model to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "status": self.status,
            "config": self.config,
            "is_active": self.is_active,
            "user_id": self.user_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
```

## DAO TEMPLATE

```python
# surface/dao.py
from typing import List, Optional
from sqlalchemy import select, update, delete, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

from surface.models import {EntityName}
from utils.dao import BaseDao


class {EntityName}Dao(BaseDao):
    """Data Access Object for {EntityName} operations"""

    def __init__(self, session: AsyncSession):
        super().__init__(session, {EntityName})

    async def get_by_name(self, name: str) -> Optional[{EntityName}]:
        """
        Get {entity_name} by name.

        :param name: Name to search for
        :return: {EntityName} instance or None
        """
        query = select({EntityName}).where({EntityName}.name == name)
        result = await self._execute_query(query)
        return result.scalar_one_or_none()

    async def get_by_user_id(self, user_id: int) -> List[{EntityName}]:
        """
        Get all {entity_name}s for a user.

        :param user_id: User ID
        :return: List of {EntityName} instances
        """
        query = select({EntityName}).where({EntityName}.user_id == user_id)
        result = await self._execute_query(query)
        return result.scalars().all()

    async def get_active_{entity_name}s(self) -> List[{EntityName}]:
        """
        Get all active {entity_name}s.

        :return: List of active {EntityName} instances
        """
        query = select({EntityName}).where({EntityName}.is_active == True)
        result = await self._execute_query(query)
        return result.scalars().all()

    async def search_by_name(self, search_term: str, limit: int = 50) -> List[{EntityName}]:
        """
        Search {entity_name}s by name.

        :param search_term: Search term
        :param limit: Maximum results
        :return: List of matching {EntityName} instances
        """
        query = (
            select({EntityName})
            .where({EntityName}.name.ilike(f"%{search_term}%"))
            .limit(limit)
        )
        result = await self._execute_query(query)
        return result.scalars().all()

    async def list_with_pagination(self, skip: int = 0, limit: int = 100) -> List[{EntityName}]:
        """
        Get paginated list of {entity_name}s.

        :param skip: Number of records to skip
        :param limit: Maximum records to return
        :return: List of {EntityName} instances
        """
        query = (
            select({EntityName})
            .order_by({EntityName}.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        result = await self._execute_query(query)
        return result.scalars().all()

    async def update_status(self, {entity_name}_id: int, status: str) -> None:
        """
        Update {entity_name} status.

        :param {entity_name}_id: {EntityName} ID
        :param status: New status
        """
        await self.update_by_pk({entity_name}_id, {"status": status})

    async def soft_delete(self, {entity_name}_id: int) -> None:
        """
        Soft delete {entity_name}.

        :param {entity_name}_id: {EntityName} ID
        """
        await self.update_by_pk({entity_name}_id, {"is_active": False})

    async def bulk_create(self, {entity_name}_data_list: List[dict]) -> None:
        """
        Bulk create {entity_name}s.

        :param {entity_name}_data_list: List of {entity_name} data
        """
        await self.bulk_insert({entity_name}_data_list)
```

## SERIALIZER TEMPLATE

```python
# surface/serializers.py
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field, validator, ConfigDict
from enum import Enum


class {EntityName}Status(str, Enum):
    """Status enumeration for {EntityName}"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    DELETED = "deleted"


class {EntityName}Request(BaseModel):
    """Request schema for {EntityName} operations"""
    model_config = ConfigDict(str_strip_whitespace=True)

    name: str = Field(..., min_length=1, max_length=255, description="{EntityName} name")
    description: Optional[str] = Field(None, max_length=1000, description="{EntityName} description")
    status: {EntityName}Status = Field(default={EntityName}Status.ACTIVE, description="{EntityName} status")
    config: Optional[Dict[str, Any]] = Field(default=None, description="Configuration data")
    user_id: Optional[int] = Field(None, description="Associated user ID")

    @validator('name')
    def validate_name(cls, v):
        """Validate {entity_name} name"""
        if not v or len(v.strip()) == 0:
            raise ValueError("{EntityName} name cannot be empty")
        return v.strip()

    @validator('config')
    def validate_config(cls, v):
        """Validate configuration data"""
        if v is not None and not isinstance(v, dict):
            raise ValueError("Config must be a valid JSON object")
        return v


class {EntityName}Response(BaseModel):
    """Response schema for {EntityName}"""
    model_config = ConfigDict(from_attributes=True)

    id: int = Field(..., description="{EntityName} ID")
    name: str = Field(..., description="{EntityName} name")
    description: Optional[str] = Field(None, description="{EntityName} description")
    status: {EntityName}Status = Field(..., description="{EntityName} status")
    config: Optional[Dict[str, Any]] = Field(None, description="Configuration data")
    user_id: Optional[int] = Field(None, description="Associated user ID")
    is_active: bool = Field(..., description="Whether {entity_name} is active")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")


class {EntityName}ListRequest(BaseModel):
    """Request schema for listing {entity_name}s"""
    skip: int = Field(default=0, ge=0, description="Number of records to skip")
    limit: int = Field(default=100, ge=1, le=1000, description="Maximum records to return")
    search: Optional[str] = Field(None, max_length=255, description="Search term")
    status: Optional[{EntityName}Status] = Field(None, description="Filter by status")
    user_id: Optional[int] = Field(None, description="Filter by user ID")


class {EntityName}ListResponse(BaseModel):
    """Response schema for {entity_name} list"""
    items: List[{EntityName}Response] = Field(..., description="List of {entity_name}s")
    total: int = Field(..., description="Total count")
    skip: int = Field(..., description="Records skipped")
    limit: int = Field(..., description="Records limit")


class {EntityName}UpdateRequest(BaseModel):
    """Request schema for updating {EntityName}"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    status: Optional[{EntityName}Status] = Field(None)
    config: Optional[Dict[str, Any]] = Field(None)

    @validator('name')
    def validate_name(cls, v):
        if v is not None and (not v or len(v.strip()) == 0):
            raise ValueError("{EntityName} name cannot be empty")
        return v.strip() if v else v


class {EntityName}CreateRequest({EntityName}Request):
    """Request schema for creating {EntityName} - inherits from base request"""
    pass


class {EntityName}BulkRequest(BaseModel):
    """Request schema for bulk operations"""
    {entity_name}s: List[{EntityName}CreateRequest] = Field(..., min_length=1, max_length=100)

    @validator('{entity_name}s')
    def validate_{entity_name}s(cls, v):
        if len(v) == 0:
            raise ValueError("At least one {entity_name} is required")
        return v
```

## EXCEPTION TEMPLATE

```python
# utils/exceptions.py
class {EntityName}Exception(ApplicationException):
    """Exception for {entity_name}-related errors"""
    DEFAULT_MESSAGE = "An error occurred while processing {entity_name}"
    ERROR_CODE = {error_code}  # Use appropriate 4-digit code

class {EntityName}NotFoundException({EntityName}Exception):
    """Exception for when {entity_name} is not found"""
    DEFAULT_MESSAGE = "{EntityName} not found"
    ERROR_CODE = {error_code + 1}

class {EntityName}ValidationException({EntityName}Exception):
    """Exception for {entity_name} validation errors"""
    DEFAULT_MESSAGE = "{EntityName} validation failed"
    ERROR_CODE = {error_code + 2}

class {EntityName}DuplicateException({EntityName}Exception):
    """Exception for duplicate {entity_name}"""
    DEFAULT_MESSAGE = "{EntityName} already exists"
    ERROR_CODE = {error_code + 3}
```

## ROUTER TEMPLATE

```python
# surface/router.py
from fastapi import APIRouter, Depends, Query, Path
from typing import Optional

from surface.views import {EntityName}View
from surface.serializers import (
    {EntityName}Request, {EntityName}Response, {EntityName}ListResponse,
    {EntityName}UpdateRequest, {EntityName}ListRequest
)

router = APIRouter(prefix="/{entity_name}s", tags=["{entity_name}s"])


@router.post("/", response_model=dict, summary="Create {entity_name}")
async def create_{entity_name}(request: {EntityName}Request):
    """Create a new {entity_name}"""
    return await {EntityName}View.create_{entity_name}(request)


@router.get("/{{entity_name}_id}", response_model=dict, summary="Get {entity_name}")
async def get_{entity_name}(
    {entity_name}_id: int = Path(..., description="{EntityName} ID")
):
    """Get {entity_name} by ID"""
    return await {EntityName}View.get_{entity_name}({entity_name}_id)


@router.put("/{{entity_name}_id}", response_model=dict, summary="Update {entity_name}")
async def update_{entity_name}(
    {entity_name}_id: int = Path(..., description="{EntityName} ID"),
    request: {EntityName}UpdateRequest = ...
):
    """Update existing {entity_name}"""
    return await {EntityName}View.update_{entity_name}({entity_name}_id, request)


@router.delete("/{{entity_name}_id}", response_model=dict, summary="Delete {entity_name}")
async def delete_{entity_name}(
    {entity_name}_id: int = Path(..., description="{EntityName} ID")
):
    """Delete {entity_name} by ID"""
    return await {EntityName}View.delete_{entity_name}({entity_name}_id)


@router.get("/", response_model=dict, summary="List {entity_name}s")
async def list_{entity_name}s(
    skip: int = Query(default=0, ge=0, description="Records to skip"),
    limit: int = Query(default=100, ge=1, le=1000, description="Records limit"),
    search: Optional[str] = Query(default=None, description="Search term"),
):
    """List {entity_name}s with pagination and search"""
    return await {EntityName}View.list_{entity_name}s(skip=skip, limit=limit)
```

## CONFIGURATION TEMPLATE

```yaml
# config/default.{env}.yaml
app:
  name: "{PROJECT_NAME}"
  debug: false
  cors_origins: ["*"]

database:
  url: "${DATABASE_URL}"
  echo: false
  pool_size: 10
  max_overflow: 20

redis:
  url: "${REDIS_URL}"
  max_connections: 10

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

sentry:
  dsn: "${SENTRY_DSN}"
  environment: "{env}"

external_apis:
  api_timeout: 30
  max_retries: 3
```

## TESTING TEMPLATE

```python
# tests/test_{entity_name}.py
import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from surface.models import {EntityName}
from surface.serializers import {EntityName}Request
from tests.conftest import test_db


class Test{EntityName}View:
    """Test cases for {EntityName} views"""

    async def test_create_{entity_name}(self, client: AsyncClient, test_db: AsyncSession):
        """Test {entity_name} creation"""
        request_data = {
            "name": "Test {EntityName}",
            "description": "Test description",
            "status": "active"
        }

        response = await client.post("/{entity_name}s/", json=request_data)

        assert response.status_code == 200
        assert response.json()["success"] is True
        assert response.json()["data"]["name"] == request_data["name"]

    async def test_get_{entity_name}(self, client: AsyncClient, test_db: AsyncSession):
        """Test {entity_name} retrieval"""
        # Create test {entity_name}
        {entity_name} = {EntityName}(name="Test {EntityName}", description="Test")
        test_db.add({entity_name})
        await test_db.commit()
        await test_db.refresh({entity_name})

        response = await client.get(f"/{entity_name}s/{{entity_name}.id}")

        assert response.status_code == 200
        assert response.json()["success"] is True
        assert response.json()["data"]["id"] == {entity_name}.id

    async def test_update_{entity_name}(self, client: AsyncClient, test_db: AsyncSession):
        """Test {entity_name} update"""
        # Create and update test {entity_name}
        pass

    async def test_delete_{entity_name}(self, client: AsyncClient, test_db: AsyncSession):
        """Test {entity_name} deletion"""
        # Create and delete test {entity_name}
        pass

    async def test_list_{entity_name}s(self, client: AsyncClient, test_db: AsyncSession):
        """Test {entity_name}s listing"""
        # Create multiple {entity_name}s and test listing
        pass
```

## MIDDLEWARE TEMPLATE

```python
# utils/middlewares/{middleware_name}_middleware.py
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response
from config.logging import logger


class {MiddlewareName}Middleware(BaseHTTPMiddleware):
    """
    {Middleware description}
    """

    def __init__(self, app, **kwargs):
        super().__init__(app)
        self.config = kwargs

    async def dispatch(self, request: Request, call_next) -> Response:
        """
        Process request and response

        :param request: Incoming request
        :param call_next: Next middleware/endpoint
        :return: Response
        """
        try:
            # Pre-processing logic
            logger.info(f"Processing request: {request.method} {request.url}")

            # Call next middleware/endpoint
            response = await call_next(request)

            # Post-processing logic
            logger.info(f"Request completed: {response.status_code}")

            return response
        except Exception as e:
            logger.error(f"Middleware error: {str(e)}")
            raise
```

## API INTEGRATION TEMPLATE

```python
# integrations/{service_name}_integration.py
import aiohttp
import asyncio
from typing import Dict, Any, Optional
from config.logging import logger
from config.settings import loaded_config
from utils.exceptions import ExternalAPIException


class {ServiceName}Integration:
    """Integration with {Service Name} API"""

    def __init__(self):
        self.base_url = loaded_config.{service_name}_base_url
        self.api_key = loaded_config.{service_name}_api_key
        self.timeout = loaded_config.api_timeout
        self.max_retries = loaded_config.max_retries

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None,
        headers: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        Make HTTP request to external API with retry logic

        :param method: HTTP method
        :param endpoint: API endpoint
        :param data: Request body data
        :param params: Query parameters
        :param headers: Request headers
        :return: API response data
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        default_headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        if headers:
            default_headers.update(headers)

        for attempt in range(self.max_retries):
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(self.timeout)) as session:
                    async with session.request(
                        method=method,
                        url=url,
                        json=data,
                        params=params,
                        headers=default_headers
                    ) as response:
                        response.raise_for_status()
                        return await response.json()
            except Exception as e:
                logger.warning(f"API request attempt {attempt + 1} failed: {str(e)}")
                if attempt == self.max_retries - 1:
                    raise ExternalAPIException(f"{ServiceName} API request failed: {str(e)}")
                await asyncio.sleep(2 ** attempt)  # Exponential backoff

    async def get_{resource}(self, {resource}_id: str) -> Dict[str, Any]:
        """
        Get {resource} from {Service Name}

        :param {resource}_id: {Resource} identifier
        :return: {Resource} data
        """
        try:
            endpoint = f"{resource}s/{{resource}_id}"
            return await self._make_request("GET", endpoint)
        except Exception as e:
            logger.error(f"Error fetching {resource} {{resource}_id}: {str(e)}")
            raise

    async def create_{resource}(self, {resource}_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create {resource} in {Service Name}

        :param {resource}_data: {Resource} creation data
        :return: Created {resource} data
        """
        try:
            endpoint = f"{resource}s"
            return await self._make_request("POST", endpoint, data={resource}_data)
        except Exception as e:
            logger.error(f"Error creating {resource}: {str(e)}")
            raise
```

## UTILITY FUNCTION TEMPLATE

```python
# utils/{utility_name}.py
import asyncio
from typing import Any, Dict, List, Optional, Union
from config.logging import logger


class {UtilityName}:
    """
    Utility class for {utility_description}
    """

    @staticmethod
    async def process_data(data: Union[Dict, List]) -> Any:
        """
        Process data with specific logic

        :param data: Input data to process
        :return: Processed data
        """
        try:
            # Implementation logic
            logger.info("Processing data")
            return data
        except Exception as e:
            logger.error(f"Error processing data: {str(e)}")
            raise

    @staticmethod
    def validate_input(input_data: Dict) -> bool:
        """
        Validate input data

        :param input_data: Data to validate
        :return: True if valid, False otherwise
        """
        try:
            # Validation logic
            required_fields = ["field1", "field2"]
            return all(field in input_data for field in required_fields)
        except Exception as e:
            logger.error(f"Validation error: {str(e)}")
            return False

    @staticmethod
    async def async_operation(data: Any) -> Any:
        """
        Perform asynchronous operation

        :param data: Operation data
        :return: Operation result
        """
        try:
            # Async operation logic
            await asyncio.sleep(0.1)  # Simulated async work
            return data
        except Exception as e:
            logger.error(f"Async operation error: {str(e)}")
            raise


# Helper functions
async def background_task(data: Dict) -> None:
    """
    Background task template

    :param data: Task data
    """
    try:
        logger.info("Starting background task")
        # Task logic here
        logger.info("Background task completed")
    except Exception as e:
        logger.error(f"Background task error: {str(e)}")


def format_response(data: Any, message: str = "") -> Dict:
    """
    Format standard API response

    :param data: Response data
    :param message: Response message
    :return: Formatted response
    """
    return {
        "success": True,
        "data": data,
        "message": message,
        "timestamp": datetime.utcnow().isoformat()
    }
```

## NAMING CONVENTIONS

### File Naming
- Use snake_case for all file names
- Use descriptive, clear names
- Add appropriate suffixes: `_view.py`, `_service.py`, `_dao.py`, `_model.py`

### Class Naming
- Use PascalCase for class names
- Use descriptive names that clearly indicate purpose
- Suffix with type: `UserView`, `UserService`, `UserDao`

### Function/Method Naming
- Use snake_case for function and method names
- Use verbs for actions: `create_user`, `get_user`, `update_user`
- Use descriptive names that explain what the function does

### Variable Naming
- Use snake_case for variable names
- Use descriptive names
- Avoid abbreviations unless commonly understood

### Constant Naming
- Use UPPER_SNAKE_CASE for constants
- Group related constants in enums or classes

## ERROR HANDLING RULES

1. Always use structured exceptions from `utils.exceptions`
2. Include proper error codes in exception classes
3. Log errors with appropriate log levels
4. Return consistent error responses via `BaseView.construct_error_response`
5. Include user-friendly error messages
6. Add proper exception tracking with Sentry

## ASYNC/AWAIT RULES

1. Use `async def` for all I/O operations
2. Use `await` for all async calls
3. Use `asyncio.create_task()` for fire-and-forget operations
4. Implement proper async context managers where needed
5. Use `aiohttp` for external HTTP requests

## DATABASE RULES

1. Always use connection handlers for database operations
2. Commit transactions explicitly
3. Use proper session management
4. Implement DAOs for data access
5. Use type hints for SQLAlchemy models
6. Add proper indexes for query performance

## API RULES

1. Use proper HTTP status codes
2. Implement pagination for list endpoints
3. Add request/response validation with Pydantic
4. Include comprehensive API documentation
5. Use consistent response formats
6. Implement proper authentication/authorization

## LOGGING RULES

1. Use structured logging with appropriate levels
2. Include context information in logs
3. Log all external API calls
4. Log performance metrics for critical operations
5. Use correlation IDs for request tracking

## TESTING RULES

1. Write unit tests for all business logic
2. Write integration tests for API endpoints
3. Use proper test fixtures and factories
4. Mock external dependencies
5. Achieve minimum 80% code coverage
6. Use descriptive test names that explain what is being tested

## DEPENDENCY MANAGEMENT

1. Pin exact versions in requirements files
2. Separate development and production dependencies
3. Use virtual environments
4. Document all external dependencies
5. Regularly update dependencies for security

## PERFORMANCE RULES

1. Use connection pooling for databases
2. Implement caching where appropriate
3. Use async operations for I/O
4. Optimize database queries
5. Monitor performance metrics
6. Use background tasks for heavy operations

## SECURITY RULES

1. Validate all input data
2. Use proper authentication/authorization
3. Implement rate limiting
4. Sanitize data before storage
5. Use HTTPS for all external communications
6. Follow OWASP security guidelines

Remember to:
- Replace all `{EntityName}`, `{entity_name}`, `{ServiceName}`, etc. with actual names
- Update error codes to match your numbering scheme
- Customize validation logic based on your requirements
- Add any domain-specific rules and patterns
- Update configuration based on your environment needs

