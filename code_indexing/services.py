import hashlib
from datetime import datetime, timezone
from typing import Optional
from code_indexing.file_utils import is_ignored
from code_indexing.serializers import GetGraphRequest, GetGraphResponse, SyncGraphFileRequest, SyncGraphFileResponse, \
    SyncStatusResponse, KeywordSearchRequest, ResponseData, VectorSearchRequest, GetFileContentRequest, \
    GetIndexingStatusRequest
from clerk_integration.utils import UserData

from knowledge_base.dao import KnowledgeBaseDao, IntegrationDao, IngestionRunDao
from knowledge_base.models import IntegrationType, KBState
from utils.connection_handler import ConnectionHandler
from utils.exceptions import CustomException
from utils.kafka.constants import KAFKA_SERVICE_CONFIG_MAPPING, KafkaServices, ETL_EXTERNAL_DATA
from utils.kafka.kafka_utils import almanac_partitioner
from utils.vector_db import VectorDBAdapter
from utils.vector_db.exceptions import SearchError
from config.logging import logger
from knowledge_base.locksmith_calls import _call_grant_data_source_access


class GraphService:
    def __init__(self,
                 vector_db_adapter: VectorDBAdapter,
                 knowledge_base_dao: KnowledgeBaseDao,
                 integration_dao : IntegrationDao,
                 ingestion_run_dao: IngestionRunDao
    ):
        """Initializes the GraphService with required DAOs and vector DB adapter.

        Args:
            vector_db_adapter (VectorDBAdapter): The vector database interface.
            knowledge_base_dao (KnowledgeBaseDao): DAO for knowledge base records.
            integration_dao (IntegrationDao): DAO for integration records.
            ingestion_run_dao (IngestionRunDao): DAO for ingestion run tracking.
        """

        self.vector_db_adapter = vector_db_adapter
        self.knowledge_base_dao = knowledge_base_dao
        self.integration_dao = integration_dao
        self.ingestion_run_dao = ingestion_run_dao


    async def get_local_graph(self, request: GetGraphRequest, user_data: UserData) -> ResponseData:
        """Creates or fetches a knowledge base based on graph metadata and user context."""

        response_data = ResponseData.model_construct(success=False)
        response_data.success = True

        unique_str = f"{str(user_data.userId)}_{request.local_path}_{request.remote_path}"
        graph_id = hashlib.sha256(unique_str.encode()).hexdigest()

        existing_entry = await self.knowledge_base_dao.exist_by_graph_id(graph_id)
        if existing_entry:
            # If total_files doesn't match, update the record
            if existing_entry.total_files != request.total_files:
                await self.knowledge_base_dao.update_graph_details(
                    graph_id, {"total_files": request.total_files, "created_by": user_data.userId}
                )
            logger.info("Graph ID already exists.")
            response_data.data = GetGraphResponse(
                status="success",
                message="Graph ID already exists.",
                graph_id=graph_id,
                total_files=request.total_files,
                id=graph_id
            ).model_dump()
            return response_data

        integration = await self.integration_dao.get_by_type(IntegrationType.fynix_extension.value)
        settings_json = {
            "branch_name": request.branch
        }
        
        # Extract the last part of the path for KB name
        kb_name = (
            request.remote_path.rstrip("/").split("/")[-1] 
            if request.remote_path 
            else request.local_path.rstrip("/").split("/")[-1]
        )
        
        new_entry = {
            "graph_id": graph_id,
            "created_by": user_data.userId,
            "org_id" : user_data.orgId,
            "source_identifier": request.remote_path or request.local_path,
            "settings_json": settings_json,
            "kb_type": IntegrationType.fynix_extension.value,
            "total_files": request.total_files,
            "name": kb_name,
            "is_updatable": False,
            "last_indexed_at": datetime.now(timezone.utc),
            "state": KBState.indexing,
            "integration_id": integration.id
        }

        kb = await self.knowledge_base_dao.create_knowledge_base(commit=True, **new_entry)
        logger.info(f"Graph ID generated successfully : {graph_id}")
        response_data.data = GetGraphResponse(
            status="success",
            message="Graph ID generated successfully.",
            graph_id=graph_id,
            total_files=request.total_files,
            id=graph_id
        ).model_dump()
        
        await _call_grant_data_source_access(
            knowledge_base_id=kb.id, 
            user_id=user_data.userId, 
            user_data=None,
            org_id=None, 
            team_id=None
        )
        return response_data


    async def sync_graph_file(
        self, 
        request: SyncGraphFileRequest, 
        connection_handler: ConnectionHandler, 
        user_data: UserData
    ) -> SyncGraphFileResponse:
        """Handles ingestion of a single file from the user's graph via Kafka event emission."""

        kb = await self.knowledge_base_dao.exist_by_graph_id(request.graph_id)

        if not kb:
            raise CustomException(f"Knowledge Base not found with graph id: {request.graph_id}")

        if not is_ignored(request.file_path):
            ingestion_run = await self.ingestion_run_dao.get_latest_ingestion_run_by_kb_id(kb_id=kb.id)

            if not ingestion_run:
                ingestion_run = await self.ingestion_run_dao.create_ingestion_run(
                    kb_id=kb.id,
                    status="running",
                    is_default_stats=True
                )
                logger.info(f"[Fynix Extension] IngestionRun created with ID: {ingestion_run.id}")

            # await self.process_file(request)
            event = {
                "payload": {
                    "provider": IntegrationType.fynix_extension.value,
                    "graph_id": request.graph_id,
                    "file_path": request.file_path,
                    "content": request.content,
                    "mode": request.mode,
                    "knowledge_base_id": kb.id,
                    "total_files": kb.total_files,
                    "ingestion_run_id": ingestion_run.id,
                }
            }
            logger.info(f"Event to be emitted: {event}")

            await connection_handler.event_emitter.emit(
                topics=KAFKA_SERVICE_CONFIG_MAPPING[KafkaServices.almanac][ETL_EXTERNAL_DATA]["topics"],
                partition_value=str(almanac_partitioner.partition()),
                event=event
            )
            logger.info("file sent to consumer for indexing")
        await connection_handler.session.commit()
        return SyncGraphFileResponse(status="success", message="File synced successfully.")


    async def get_sync_status(self, request: GetIndexingStatusRequest) -> ResponseData:
        """Returns the indexing progress percentage of a knowledge base using its graph ID."""

        graph_id = request.graph_id
        response_data = ResponseData.model_construct(success=False)

        kb = await self.knowledge_base_dao.exist_by_graph_id(graph_id)

        if kb:
            stats_json = await self.ingestion_run_dao.get_latest_stats_json_by_kb_id(kb.id)

            if stats_json:
                result = (
                    stats_json.get("total_source_item", 0)
                    if kb.total_files is not None and kb.total_files > 0
                    else 0.0
                )
            else:
                result = 0

            status = (result / kb.total_files) * 100 if kb.total_files else 0.0
            logger.info(f"repo sync status percentage: {str(status)}")
            response_data.success = True
            response_data.data = SyncStatusResponse(
                status="COMPLETED" if status >= 100 else "IN_PROGRESS",
                message="Sync percentage calculated",
                percentage=status,
                files_indexing_status =  []
            ).model_dump()
            return response_data
        else:
            raise CustomException("Graph not found")


    async def keyword_search(self, search: KeywordSearchRequest) -> ResponseData:
        """Performs keyword-based search across code indexed in the knowledge base."""

        response_data = ResponseData.model_construct(success=False)
        logger.info(f"keyword_search method invoked")
        try:
            # Get the knowledge base ID from the graph ID
            kb = await self.knowledge_base_dao.exist_by_graph_id(search.graph_id)
            if not kb:
                logger.error(f"No knowledge base found for graph_id: {search.graph_id}")
                return response_data
            
            kb_id = kb.id
            logger.info(f"Found knowledge base ID {kb_id} for graph_id {search.graph_id}")
            
            await self.vector_db_adapter.connect()

            # Call keyword search on Vector DB Adapter with the knowledge_base_index and KB ID
            results = await self.vector_db_adapter.keyword_search_source_code(
                search, index="knowledge_base_index", kb_id=kb_id
            )

            # Extract relevant fields from results, mapping from the new structure
            search_results = await self._build_search_results(
                hits=results["hits"]["hits"],
                graph_id=search.graph_id
            )

            response_data.success = True
            response_data.data = search_results
            logger.info(f"keyword_search result fetched")
            return response_data

        except Exception as e:
            logger.error(f"Keyword search failed for request: {search.dict()}. Error: {str(e)}")
            raise SearchError(f"Keyword search operation failed: {str(e)}")

        finally:
            await self.vector_db_adapter.close()


    async def vector_search(self, search: VectorSearchRequest) -> ResponseData:
        """Performs vector similarity search using embeddings within a knowledge base."""

        response_data = ResponseData.model_construct(success=False)
        logger.info(f"vector_search method invoked")
        try:
            # Resolve graph_id → kb_id
            kb = await self.knowledge_base_dao.exist_by_graph_id(search.graph_id)
            if not kb:
                logger.error(f"No knowledge base found for graph_id: {search.graph_id}")
                return response_data

            kb_id = kb.id
            logger.info(f"Found knowledge base ID {kb_id} for graph_id {search.graph_id}")

            await self.vector_db_adapter.connect()

            # Pass kb_id to the adapter
            results = await self.vector_db_adapter.knn_similarity_search(
                request=search,
                index="knowledge_base_index",
                kb_id=kb_id
            )

            # Process results (no change here)
            search_results = await self._build_search_results(
                hits=results["hits"]["hits"],
                graph_id=search.graph_id,
                threshold=search.threshold
            )

            response_data.success = True
            response_data.data = search_results
            logger.info("vector_search method fetched")
            return response_data

        except Exception as e:
            logger.error(f"Vector search failed for request: {search.dict()}. Error: {str(e)}")
            raise SearchError(f"Vector search operation failed: {str(e)}")

        finally:
            await self.vector_db_adapter.close()


    async def get_file_content(self, request: GetFileContentRequest) -> ResponseData:
        """Retrieves the full content of a file by its logical path within a knowledge base."""

        response_data = ResponseData.model_construct(success=False)
        try:
            # Resolve knowledge base ID from graph_id
            kb = await self.knowledge_base_dao.exist_by_graph_id(request.graph_id)
            if not kb:
                return response_data

            await self.vector_db_adapter.connect()

            result = await self.vector_db_adapter.get_file_content(
                index_name="knowledge_base_index",
                file_path=request.file_path,
                kb_id=kb.id
            )
            if not result:
                return response_data

            response_data.success = True
            response_data.data = {"content": result}
            return response_data

        except Exception as e:
            logger.error(f"Get file content failed for request: {request.dict()}. Error: {str(e)}")
            raise SearchError(f"Get file content operation failed: {str(e)}")

        finally:
            await self.vector_db_adapter.close()


    async def get_folder_structure(self, graph_id: str, folder_path: Optional[str] = None) -> ResponseData:
        """Returns the folder structure associated with a knowledge base's memory."""

        response_data = ResponseData.model_construct(success=False)
        try:
            # Fetch knowledge base by graph_id
            kb = await self.knowledge_base_dao.exist_by_graph_id(graph_id)
            if not kb:
                logger.error(f"No knowledge base found for graph_id: {graph_id}")
                return response_data

            memory = kb.memory or {}
            folder_structure = memory.get("folder_structure", {})

            if folder_path:
                # Normalize and split the path into parts
                parts = folder_path.strip("/").split("/")
                current = folder_structure.get("")
                for part in parts:
                    if isinstance(current, dict) and part in current:
                        current = current[part]
                    else:
                        logger.warning(f"Folder path '{folder_path}' not found in memory.")
                        return response_data  # Folder path not found

                target_structure = current
            else:
                # Return full structure if no path specified
                target_structure = folder_structure.get("")

            response_data.success = True
            response_data.data = {"folder_structure": target_structure}
            return response_data

        except Exception as e:
            logger.error(f"Get folder structure failed for graph_id: {graph_id}. Error: {str(e)}")
            raise SearchError(f"Get folder structure operation failed: {str(e)}")

    async def _build_search_results(
            self,
            hits: list,
            graph_id: str,
            threshold: float | None = None
    ) -> list[dict]:
        """Constructs structured search result objects from Elasticsearch hits."""

        return [
            {
                "path": element['_source']['metadata'].get("path", ""),
                "type": element['_source']['metadata'].get("type", "file"),
                "name": element['_source']['title'].split("/")[-1] if element['_source'].get("title") else "",
                "source_code": element['_source'].get("content", ""),
                "graph_id": graph_id,
                "start_line": element['_source']['metadata'].get("start_line", 1),
                "end_line": element['_source']['metadata'].get("end_line", "")
            }
            for element in hits
            if threshold is None or element.get("_score", 0) >= (threshold / 100.0)
        ]
