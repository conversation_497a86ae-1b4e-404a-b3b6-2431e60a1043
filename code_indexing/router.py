from fastapi import APIRouter

from app.routing import CustomRequestRoute
from code_indexing.views import (
    get_local_graph,
    sync_graph_file,
    get_sync_status,
    keyword_search,
    vector_search,
    get_file_content,
    get_folder_structure
)


router = APIRouter(tags=["Code Indexing"], route_class=CustomRequestRoute)


router.add_api_route(
    "/get_local_graph",
    endpoint=get_local_graph,
    methods=["POST"],
    description="Fetch the local graph structure for a given graph request"
)


router.add_api_route(
    "/sync_local_file",
    endpoint=sync_graph_file,
    methods=["POST"],
    description="Sync a graph file into the knowledge base"
)


router.add_api_route(
    "/graph/status",
    endpoint=get_sync_status,
    methods=["POST"],
    description="Get the indexing status of a code graph"
)


router.add_api_route(
    "/get-keyword-search",
    endpoint=keyword_search,
    methods=["POST"],
    description="Search code graph using keyword search"
)


router.add_api_route(
    "/get-vector-search",
    endpoint=vector_search,
    methods=["POST"],
    description="Search code graph using vector search"
)


router.add_api_route(
    "/get-file-content",
    endpoint=get_file_content,
    methods=["POST"],
    description="Fetch the content of a file from the code graph"
)


router.add_api_route(
    "/get-folder-structure/{graph_id}",
    endpoint=get_folder_structure,
    methods=["GET"],
    description="Fetch folder structure for a graph ID"
)
