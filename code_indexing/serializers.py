from pydantic import BaseModel, <PERSON>, validator
from typing import List, Dict, Optional
from uuid import uuid4

DEFAULT_PAGE_SIZE = 20
DEFAULT_PAGE_NUMBER = 1

class GetFileContentRequest(BaseModel):
    graph_id: str = Field(..., min_length=1)
    file_path: str = Field(..., min_length=1)


class GetGraphRequest(BaseModel):
    local_path: str = Field(..., min_length=1, alias="path")
    remote_path: Optional[str] = Field(None, alias="remote_url")
    branch: Optional[str] = Field(None)
    total_files: Optional[int] = Field(None)

    @validator("local_path")
    def validate_local_path(cls, value):
        """local path validator"""
        if not value:
            raise ValueError("Local path is required and cannot be empty.")
        return value



class GetGraphResponse(BaseModel):
    status: str
    message: str
    graph_id: str
    total_files: Optional[int] = None
    id: str

class SyncGraphFileRequest(BaseModel):
    graph_id: str
    file_path: str
    content: str
    mode: str

class SyncGraphFileResponse(BaseModel):
    status: str
    message: str

class SyncStatusResponse(BaseModel):
    status: str
    message: str
    percentage: float
    files_indexing_status: Optional[List[Dict[str, str]]] = None

class GetIndexingStatusRequest(BaseModel):
    graph_id: str

class ResponseData(BaseModel):
    identifier: str = Field(default_factory=lambda: str(uuid4()))
    success: bool
    message: str
    errors: Optional[List] = None
    data: Optional[List | Dict] = None
    failed_entries: Optional[List] = None
    pagination: Dict = {}

    def dict(self, *args, **kwargs):
        """Overrides the default dict method to use Pydantic's model_construct for serialization.

        Returns:
            dict: The dictionary representation of the ResponseData object.
        """

        return super().model_construct(*args, **kwargs)

class KeywordSearchRequest(BaseModel):
    graph_id: str
    keywords: Optional[List[str]] = None
    max_results: int = 5
    file_paths: Optional[List[str]] = None
    folder_paths: Optional[List[str]] = None
    entire_workspace: bool = False


class VectorSearchRequest(BaseModel):
    graph_id: str
    query: str
    max_results: int = 5
    file_paths: Optional[List[str]] = None
    folder_paths: Optional[List[str]] = None
    entire_workspace: bool = False
    threshold: Optional[float] = Field(default=None, ge=0, le=100)
