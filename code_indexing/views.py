from fastapi import Body, Depends
from typing import Optional

from clerk_integration.utils import UserData
from code_indexing.serializers import (
    GetGraphRequest,
    SyncGraphFileRequest,
    KeywordSearchRequest,
    VectorSearchRequest,
    GetFileContentRequest,
    GetIndexingStatusRequest,
    ResponseData,
    SyncGraphFileResponse
)
from code_indexing.services import GraphService
from knowledge_base.dao import KnowledgeBaseDao, IntegrationDao, IngestionRunDao
from utils.connection_handler import get_connection_handler_for_app, ConnectionHandler
from utils.common import get_user_data_from_request
from utils.vector_db import ElasticSearchAdapter


async def get_local_graph(
    request: GetGraphRequest = Body(...),
    user_data: UserData = Depends(get_user_data_from_request),
    connection_handler: ConnectionHandler = Depends(get_connection_handler_for_app)
):
    """Creates or retrieves a local graph knowledge base entry."""

    session = connection_handler.session
    service = GraphService(
        ElasticSearchAdapter(),
        KnowledgeBaseDao(session),
        <PERSON><PERSON><PERSON>(session),
        IngestionRunDao(session)
    )
    return await service.get_local_graph(request, user_data)


async def sync_graph_file(
    request: SyncGraphFileRequest = Body(...),
    connection_handler: ConnectionHandler = Depends(get_connection_handler_for_app),
    user_data: UserData = Depends(get_user_data_from_request)
):
    """Sends a file to the backend for indexing via event emission."""

    session = connection_handler.session
    service = GraphService(
        ElasticSearchAdapter(),
        KnowledgeBaseDao(session),
        IntegrationDao(session),
        IngestionRunDao(session)
    )
    return await service.sync_graph_file(request, connection_handler, user_data)


async def get_sync_status(
    request: GetIndexingStatusRequest = Body(...),
    connection_handler: ConnectionHandler = Depends(get_connection_handler_for_app)
):
    """Returns the current indexing status of a graph."""

    session = connection_handler.session
    service = GraphService(
        ElasticSearchAdapter(),
        KnowledgeBaseDao(session),
        IntegrationDao(session),
        IngestionRunDao(session)
    )
    return await service.get_sync_status(request)


async def keyword_search(
    request: KeywordSearchRequest = Body(...),
    connection_handler: ConnectionHandler = Depends(get_connection_handler_for_app)
):
    """Performs keyword-based code search within the knowledge base."""

    session = connection_handler.session
    service = GraphService(
        ElasticSearchAdapter(),
        KnowledgeBaseDao(session),
        IntegrationDao(session),
        IngestionRunDao(session)
    )
    return await service.keyword_search(request)


async def vector_search(
    request: VectorSearchRequest = Body(...),
    connection_handler: ConnectionHandler = Depends(get_connection_handler_for_app)
):
    """Performs vector-based semantic search in the knowledge base."""

    session = connection_handler.session
    service = GraphService(
        ElasticSearchAdapter(),
        KnowledgeBaseDao(session),
        IntegrationDao(session),
        IngestionRunDao(session)
    )
    return await service.vector_search(request)


async def get_file_content(
    request: GetFileContentRequest = Body(...),
    connection_handler: ConnectionHandler = Depends(get_connection_handler_for_app)
):
    """Fetches the full content of a specific file by path."""

    session = connection_handler.session
    service = GraphService(
        ElasticSearchAdapter(),
        KnowledgeBaseDao(session),
        IntegrationDao(session),
        IngestionRunDao(session)
    )
    return await service.get_file_content(request)


async def get_folder_structure(
    graph_id: str,
    folder_path: Optional[str] = None,
    connection_handler: ConnectionHandler = Depends(get_connection_handler_for_app)
):
    """Returns the folder structure for a given graph ID."""

    session = connection_handler.session
    service = GraphService(
        ElasticSearchAdapter(),
        KnowledgeBaseDao(session),
        IntegrationDao(session),
        IngestionRunDao(session)
    )
    return await service.get_folder_structure(graph_id, folder_path)
